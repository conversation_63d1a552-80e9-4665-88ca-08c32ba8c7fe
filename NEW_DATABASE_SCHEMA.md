# 🗄️ NEW DATABASE SCHEMA - API-BASED COMMENT FLOW

## 📋 **Thiết kế mới cho API-based comment detection**

### **1. COMMENTS TABLE (Session Storage)**
```sql
CREATE TABLE comments (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  pk TEXT NOT NULL UNIQUE,              -- Instagram pk ID (unique từ API)
  username TEXT NOT NULL,
  comment_text TEXT NOT NULL,
  timestamp DATETIME NOT NULL,          -- Comment timestamp từ API
  session_id TEXT NOT NULL,             -- Session ID để group comments
  detected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  is_processed BOOLEAN DEFAULT FALSE,   -- <PERSON><PERSON> được xử lý (in/queue) chưa
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_comments_pk ON comments(pk);
CREATE INDEX idx_comments_session ON comments(session_id);
CREATE INDEX idx_comments_username ON comments(username);
CREATE INDEX idx_comments_timestamp ON comments(timestamp);
```

### **2. PRINTED_HISTORY TABLE (Updated)**
```sql
CREATE TABLE printed_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  comment_pk TEXT NOT NULL,             -- Instagram pk ID (thay vì comment_id)
  username TEXT NOT NULL,
  comment_text TEXT NOT NULL,
  print_type TEXT DEFAULT 'comment',    -- 'comment' hoặc 'backup'
  printed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  session_id TEXT,                      -- Link back to session
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  synced_at DATETIME DEFAULT NULL,
  is_deleted INTEGER DEFAULT 0
);

-- Indexes
CREATE INDEX idx_printed_history_pk ON printed_history(comment_pk);
CREATE INDEX idx_printed_history_username ON printed_history(username);
CREATE INDEX idx_printed_history_printed_at ON printed_history(printed_at);
CREATE INDEX idx_printed_history_session ON printed_history(session_id);
```

### **3. MESSAGE_QUEUE TABLE (Updated)**
```sql
CREATE TABLE message_queue (
  id TEXT PRIMARY KEY,
  comment_pk TEXT,                      -- Instagram pk ID (thay vì comment_id)
  username TEXT NOT NULL,
  original_comment TEXT NOT NULL,
  customer_type TEXT NOT NULL,
  template_name TEXT,
  template_type TEXT DEFAULT 'normal',
  status TEXT DEFAULT 'pending',
  retries INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  error_message TEXT,
  session_id TEXT,                      -- Link back to session
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  scheduled_at DATETIME,
  processed_at DATETIME
);

-- Indexes
CREATE INDEX idx_message_queue_pk ON message_queue(comment_pk);
CREATE INDEX idx_message_queue_session ON message_queue(session_id);
CREATE INDEX idx_message_queue_status ON message_queue(status);
```

### **4. SESSIONS TABLE (New)**
```sql
CREATE TABLE live_sessions (
  id TEXT PRIMARY KEY,                  -- Unique session ID
  started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  ended_at DATETIME DEFAULT NULL,
  total_comments INTEGER DEFAULT 0,
  processed_comments INTEGER DEFAULT 0,
  printed_comments INTEGER DEFAULT 0,
  status TEXT DEFAULT 'active',         -- 'active', 'ended', 'archived'
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Indexes
CREATE INDEX idx_sessions_started_at ON live_sessions(started_at);
CREATE INDEX idx_sessions_status ON live_sessions(status);
```

## 🔄 **MongoDB Collections (Updated)**

### **1. printed_history**
```javascript
{
  local_id: Number,           // ID từ local SQLite
  comment_pk: String,         // Instagram pk ID (unique)
  username: String,
  comment_text: String,
  print_type: String,
  printed_at: Date,
  session_id: String,         // Session reference
  created_at: Date,
  is_deleted: Boolean,
  synced_at: Date
}
```

### **2. sessions** (New collection)
```javascript
{
  local_id: String,           // Session ID từ local
  started_at: Date,
  ended_at: Date,
  total_comments: Number,
  processed_comments: Number,
  printed_comments: Number,
  status: String,
  created_at: Date,
  updated_at: Date,
  synced_at: Date
}
```

## 🎯 **Key Improvements**

### **1. Instagram pk ID as Primary Key**
- Sử dụng Instagram **pk** từ API làm unique identifier
- Loại bỏ duplicate detection logic phức tạp
- Đảm bảo mỗi comment chỉ có 1 record duy nhất

### **2. Session-based Organization**
- Comments được group theo session
- Dễ dàng track và manage theo từng live session
- Clear separation giữa các lần live khác nhau

### **3. Simplified Sync Logic**
- Sử dụng **comment_pk** (Instagram pk) làm unique key
- Loại bỏ device_id dependency
- MongoDB sync dựa trên **local_id + comment_pk**

### **4. Better Data Flow**
```
API Detection → comments table (session)
     ↓
Print Action → printed_history (với comment_pk)
     ↓
MongoDB Sync → Sử dụng comment_pk làm unique key
```

## 🚀 **Migration Strategy**

### **Phase 1: Create New Tables**
1. Tạo comments table mới
2. Tạo sessions table
3. Update printed_history schema
4. Update message_queue schema

### **Phase 2: Data Migration**
1. Clean up duplicate records trong printed_history
2. Generate session_id cho existing records
3. Convert comment_id → comment_pk (nếu có mapping)
4. Update MongoDB collections

### **Phase 3: Code Updates**
1. Update Database.js methods
2. Update MongoDBService.js sync logic
3. Update API comment detection để lưu vào comments table
4. Update printing logic để sử dụng comment_pk

### **Phase 4: Testing & Cleanup**
1. Test complete flow
2. Verify MongoDB sync
3. Remove old unused columns
4. Optimize indexes
