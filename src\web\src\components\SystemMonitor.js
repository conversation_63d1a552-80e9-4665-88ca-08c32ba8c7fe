import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { getApiUrl } from '../config/api';

const SystemMonitor = () => {
  const [resources, setResources] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(false);

  useEffect(() => {
    loadResources();
  }, []);

  useEffect(() => {
    let interval;
    if (autoRefresh) {
      interval = setInterval(loadResources, 5000); // Refresh every 5 seconds
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [autoRefresh]);

  const loadResources = async () => {
    try {
      const response = await fetch(getApiUrl('/api/system-resources'));
      const data = await response.json();
      if (data.success) {
        setResources(data.resources);
      }
    } catch (error) {
      console.error('Failed to load system resources:', error);
    }
  };

  const cleanupChrome = async (forceFull = false) => {
    const scraperRunning = resources?.messengerStatus?.isRunning || false;

    let confirmMessage;
    if (forceFull) {
      confirmMessage = '🚨 CẢNH BÁO: Bạn có chắc muốn dọn dẹp TẤT CẢ Chrome processes?\n\n⚠️ NGUY HIỂM: Điều này sẽ:\n- TẮT SCRAPER nếu đang chạy\n- Tắt auto messages nếu đang chạy\n- Có thể làm mất dữ liệu chưa lưu\n\n🛡️ KHUYẾN NGHỊ: Dùng chế độ AN TOÀN thay vì force full!';
    } else {
      confirmMessage = scraperRunning
        ? '✅ Dọn dẹp AN TOÀN Chrome processes?\n\n🛡️ Chế độ an toàn sẽ:\n- BẢO VỆ scraper đang chạy\n- Chỉ dọn dẹp messenger processes và processes rác\n- KHÔNG ảnh hưởng đến thu thập bình luận\n- Scraper sẽ tiếp tục hoạt động bình thường'
        : '✅ Dọn dẹp Chrome processes?\n\nScraper không chạy nên sẽ dọn dẹp an toàn.';
    }

    if (!confirm(confirmMessage)) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/cleanup-chrome'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ forceFull })
      });

      const data = await response.json();
      if (data.success) {
        if (forceFull) {
          toast.success('🚨 Đã dọn dẹp TẤT CẢ Chrome processes');
        } else if (data.details?.scraperProtected) {
          toast.success('✅ Đã dọn dẹp an toàn - Scraper được bảo vệ');
        } else {
          toast.success('✅ Đã dọn dẹp Chrome processes thành công');
        }
        await loadResources(); // Refresh data
      } else {
        throw new Error(data.error || 'Failed to cleanup Chrome processes');
      }
    } catch (error) {
      toast.error('Lỗi khi dọn dẹp Chrome: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmergencyRestart = async () => {
    if (!confirm('🚨 EMERGENCY RESTART AUTO-MESSAGING?\n\nĐiều này sẽ:\n- Kill hoàn toàn auto-messaging và tất cả task ẩn\n- Restart lại auto-messaging service\n- Tiếp tục hàng chờ tin nhắn\n\nChỉ dùng khi CPU 100% hoặc không thể load tin nhắn!')) {
      return;
    }

    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/system/emergency-restart-messenger'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();
      if (data.success) {
        toast.success('🚨 Emergency restart hoàn tất! Auto-messaging đã được khởi động lại');
        await loadResources();
      } else {
        throw new Error(data.error || 'Emergency restart failed');
      }
    } catch (error) {
      toast.error('Lỗi emergency restart: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const handleForceCpuCheck = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/system/force-cpu-check'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const data = await response.json();
      if (data.success) {
        toast.success(`CPU check hoàn tất: ${data.result.cpuUsage}%`);
        if (data.result.isHigh) {
          toast.warning('⚠️ CPU cao được phát hiện!');
        }
        await loadResources();
      } else {
        throw new Error(data.error || 'CPU check failed');
      }
    } catch (error) {
      toast.error('Lỗi kiểm tra CPU: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const formatMemory = (mb) => {
    if (mb > 1024) {
      return `${(mb / 1024).toFixed(1)} GB`;
    }
    return `${mb} MB`;
  };

  const getMemoryUsageColor = (used, total) => {
    const percentage = (used / total) * 100;
    if (percentage > 80) return 'text-red-600';
    if (percentage > 60) return 'text-yellow-600';
    return 'text-green-600';
  };

  if (!resources) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4">Giám sát hệ thống</h3>
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Đang tải thông tin hệ thống...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-semibold">Giám sát hệ thống</h3>
        <div className="flex items-center space-x-2">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="mr-2"
            />
            <span className="text-sm">Tự động làm mới</span>
          </label>
          <button
            onClick={loadResources}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
          >
            Làm mới
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        {/* Memory Usage */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Bộ nhớ RAM</h4>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Tổng:</span>
              <span>{formatMemory(resources.totalMemory)}</span>
            </div>
            <div className="flex justify-between">
              <span>Đã dùng:</span>
              <span className={getMemoryUsageColor(resources.usedMemory, resources.totalMemory)}>
                {formatMemory(resources.usedMemory)}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Còn trống:</span>
              <span className="text-green-600">{formatMemory(resources.freeMemory)}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${
                  (resources.usedMemory / resources.totalMemory) * 100 > 80
                    ? 'bg-red-500'
                    : (resources.usedMemory / resources.totalMemory) * 100 > 60
                    ? 'bg-yellow-500'
                    : 'bg-green-500'
                }`}
                style={{ width: `${(resources.usedMemory / resources.totalMemory) * 100}%` }}
              ></div>
            </div>
          </div>
        </div>

        {/* Chrome Processes */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">Chrome Processes</h4>
          <div className="space-y-1">
            <div className="flex justify-between">
              <span>Tổng số:</span>
              <span className={resources.chromeProcessCount > 15 ? 'text-red-600' : resources.chromeProcessCount > 10 ? 'text-yellow-600' : 'text-green-600'}>
                {resources.chromeProcessCount}
              </span>
            </div>
            {resources.chromeDetailedInfo && (
              <>
                <div className="flex justify-between text-sm">
                  <span>Testing processes:</span>
                  <span className={resources.chromeDetailedInfo.testingProcesses > 0 ? 'text-red-600' : 'text-green-600'}>
                    {resources.chromeDetailedInfo.testingProcesses}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>High memory (>900MB):</span>
                  <span className={resources.chromeDetailedInfo.highMemoryProcesses > 0 ? 'text-red-600' : 'text-green-600'}>
                    {resources.chromeDetailedInfo.highMemoryProcesses}
                  </span>
                </div>
                {resources.chromeDetailedInfo.totalMemory > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>Chrome RAM:</span>
                    <span>{formatMemory(resources.chromeDetailedInfo.totalMemory)}</span>
                  </div>
                )}
              </>
            )}
            {resources.chromeProcessCount > 15 && (
              <div className="text-sm text-red-600 mt-2">
                🚨 Có quá nhiều Chrome processes đang chạy
              </div>
            )}
            {resources.chromeProcessCount > 10 && resources.chromeProcessCount <= 15 && (
              <div className="text-sm text-yellow-600 mt-2">
                ⚠️ Chrome processes đang tăng cao
              </div>
            )}
          </div>
        </div>

        {/* PowerShell Processes */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">PowerShell Processes</h4>
          <div className="space-y-1">
            {resources.powershellStatus && (
              <>
                <div className="flex justify-between">
                  <span>Tổng số:</span>
                  <span className={
                    resources.powershellStatus.critical ? 'text-red-600' :
                    resources.powershellStatus.warning ? 'text-yellow-600' :
                    'text-green-600'
                  }>
                    {resources.powershellStatus.count || 0}
                  </span>
                </div>
                {resources.powershellStatus.critical && (
                  <div className="text-sm text-red-600 mt-2">
                    🚨 CRITICAL: PowerShell process explosion detected!
                  </div>
                )}
                {resources.powershellStatus.warning && (
                  <div className="text-sm text-yellow-600 mt-2">
                    ⚠️ High PowerShell process count
                  </div>
                )}
              </>
            )}
            {resources.processManagerStatus && (
              <>
                <div className="flex justify-between text-sm">
                  <span>Active commands:</span>
                  <span className={resources.processManagerStatus.activeProcesses > 5 ? 'text-red-600' : 'text-green-600'}>
                    {resources.processManagerStatus.activeProcesses}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Queued commands:</span>
                  <span className={resources.processManagerStatus.queuedProcesses > 0 ? 'text-yellow-600' : 'text-green-600'}>
                    {resources.processManagerStatus.queuedProcesses}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* System CPU Monitoring */}
        <div className="bg-sky-50 rounded-lg p-4">
          <h4 className="font-medium mb-2">System CPU Monitoring</h4>
          <div className="space-y-1">
            {resources.windowsCpuUsage !== undefined && (
              <div className="flex justify-between">
                <span>CPU Usage:</span>
                <span className={
                  resources.windowsCpuUsage >= 95 ? 'text-red-600 font-bold' :
                  resources.windowsCpuUsage >= 80 ? 'text-yellow-600' :
                  'text-green-600'
                }>
                  {resources.windowsCpuUsage}%
                </span>
              </div>
            )}
            {resources.systemCpuStatus && (
              <>
                {resources.systemCpuStatus.isHigh && (
                  <div className="text-sm text-red-600 mt-2">
                    🚨 HIGH CPU DETECTED! Auto-restart monitoring active
                  </div>
                )}
                {resources.systemMonitorStatus && (
                  <>
                    <div className="flex justify-between text-sm">
                      <span>Monitoring:</span>
                      <span className={resources.systemMonitorStatus.isMonitoring ? 'text-green-600' : 'text-red-600'}>
                        {resources.systemMonitorStatus.isMonitoring ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>High CPU count:</span>
                      <span className={resources.systemMonitorStatus.consecutiveHighCpuCount > 0 ? 'text-yellow-600' : 'text-green-600'}>
                        {resources.systemMonitorStatus.consecutiveHighCpuCount}/{resources.systemMonitorStatus.maxConsecutiveHighCpu}
                      </span>
                    </div>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Auto Messages Status */}
      {resources.messengerStatus && (
        <div className="bg-gray-50 rounded-lg p-4 mb-4">
          <h4 className="font-medium mb-2">Trạng thái Auto Messages</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span>Đang chạy:</span>
              <span className={resources.messengerStatus.isRunning ? 'text-green-600' : 'text-red-600'}>
                {resources.messengerStatus.isRunning ? 'Có' : 'Không'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Đã xử lý:</span>
              <span>{resources.messengerStatus.messagesProcessed}</span>
            </div>
            <div className="flex justify-between">
              <span>Hàng đợi:</span>
              <span>{resources.messengerStatus.queueLength}</span>
            </div>
            <div className="flex justify-between">
              <span>Giới hạn bộ nhớ:</span>
              <span>{resources.messengerStatus.memoryLimit} MB</span>
            </div>
          </div>
        </div>
      )}

      {/* Chrome Cleanup Manager Status */}
      {resources.cleanupManagerStatus && (
        <div className="bg-blue-50 rounded-lg p-4 mb-4">
          <h4 className="font-medium mb-2">Chrome Cleanup Manager</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex justify-between">
              <span>Trạng thái:</span>
              <span className={resources.cleanupManagerStatus.isRunning ? 'text-green-600' : 'text-red-600'}>
                {resources.cleanupManagerStatus.isRunning ? 'Đang chạy' : 'Đã dừng'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Giới hạn processes:</span>
              <span>{resources.cleanupManagerStatus.maxChromeProcesses}</span>
            </div>
            <div className="flex justify-between">
              <span>Chu kỳ dọn dẹp:</span>
              <span>{Math.round(resources.cleanupManagerStatus.cleanupIntervalMs / 60000)} phút</span>
            </div>
            {resources.cleanupManagerStatus.nextCleanup && (
              <div className="flex justify-between">
                <span>Dọn dẹp tiếp theo:</span>
                <span className="text-xs">{new Date(resources.cleanupManagerStatus.nextCleanup).toLocaleTimeString('vi-VN')}</span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => cleanupChrome(false)}
          disabled={isLoading}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Đang dọn dẹp...' : '✅ Dọn dẹp an toàn'}
        </button>

        <button
          onClick={() => cleanupChrome(true)}
          disabled={isLoading}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Đang dọn dẹp...' : '🚨 Dọn dẹp toàn bộ'}
        </button>

        <button
          onClick={handleEmergencyRestart}
          disabled={isLoading}
          className="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Đang restart...' : '🚨 Emergency Restart'}
        </button>

        <button
          onClick={handleForceCpuCheck}
          disabled={isLoading}
          className="px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isLoading ? 'Đang kiểm tra...' : 'Kiểm tra CPU'}
        </button>

        {/* Status and recommendations */}
        <div className="flex flex-col gap-2 flex-1">
          {resources.chromeProcessCount > 15 && (
            <div className="flex items-center text-sm text-red-600 bg-red-50 px-3 py-2 rounded">
              <span>🚨 Khuyến nghị: Dọn dẹp Chrome processes ngay để tránh lag CPU</span>
            </div>
          )}

          {resources.chromeProcessCount > 10 && resources.chromeProcessCount <= 15 && (
            <div className="flex items-center text-sm text-yellow-600 bg-yellow-50 px-3 py-2 rounded">
              <span>⚠️ Chrome processes đang tăng cao, nên dọn dẹp để cải thiện hiệu suất</span>
            </div>
          )}

          {resources.chromeDetailedInfo && resources.chromeDetailedInfo.testingProcesses > 0 && (
            <div className="flex items-center text-sm text-orange-600 bg-orange-50 px-3 py-2 rounded">
              <span>🔧 Phát hiện {resources.chromeDetailedInfo.testingProcesses} Chrome testing processes có thể gây lag</span>
            </div>
          )}

          {/* CPU overload warnings */}
          {resources.windowsCpuUsage >= 95 && (
            <div className="flex items-center text-sm text-red-600 bg-red-50 px-3 py-2 rounded">
              <span>🚨 CRITICAL CPU OVERLOAD: {resources.windowsCpuUsage}%! Auto-restart sẽ được kích hoạt</span>
            </div>
          )}

          {resources.systemCpuStatus?.isHigh && resources.windowsCpuUsage < 95 && (
            <div className="flex items-center text-sm text-yellow-600 bg-yellow-50 px-3 py-2 rounded">
              <span>⚠️ CPU cao: {resources.windowsCpuUsage}% - Đang theo dõi để auto-restart</span>
            </div>
          )}

          {/* PowerShell process warnings */}
          {resources.powershellStatus?.critical && (
            <div className="flex items-center text-sm text-red-600 bg-red-50 px-3 py-2 rounded">
              <span>🚨 CRITICAL: {resources.powershellStatus.count} PowerShell processes! Hệ thống đang tự động dọn dẹp</span>
            </div>
          )}

          {resources.powershellStatus?.warning && (
            <div className="flex items-center text-sm text-yellow-600 bg-yellow-50 px-3 py-2 rounded">
              <span>⚠️ Cảnh báo: {resources.powershellStatus.count} PowerShell processes đang chạy</span>
            </div>
          )}

          {resources.processManagerStatus?.queuedProcesses > 0 && (
            <div className="flex items-center text-sm text-blue-600 bg-blue-50 px-3 py-2 rounded">
              <span>⏳ {resources.processManagerStatus.queuedProcesses} lệnh đang chờ xử lý để tránh quá tải CPU</span>
            </div>
          )}

          {/* Scraper protection notice */}
          {resources.messengerStatus?.isRunning && (
            <div className="flex items-center text-sm text-green-600 bg-green-50 px-3 py-2 rounded">
              <span>🛡️ Scraper đang chạy - Dọn dẹp an toàn sẽ bảo vệ scraper</span>
            </div>
          )}
        </div>
      </div>

      <div className="mt-4 text-xs text-gray-500">
        Cập nhật lần cuối: {new Date().toLocaleTimeString('vi-VN')}
      </div>
    </div>
  );
};

export default SystemMonitor;
