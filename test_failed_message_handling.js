/**
 * Test script để kiểm tra tính năng lưu tin nhắn thất bại vào failed_messages table
 * và xóa khỏi message_queue khi gửi tin nhắn thất bại
 */

const Database = require('./src/backend/services/Database');
const InstagramMessenger = require('./src/backend/services/InstagramMessenger');

async function testFailedMessageHandling() {
  console.log('🧪 Testing failed message handling...');
  
  // Initialize database
  const database = new Database();
  await database.initialize();
  
  // Create test message data
  const testMessageData = {
    id: `test_msg_${Date.now()}`,
    comment_id: 'test_comment_123',
    username: 'test_user',
    original_comment: 'Test comment for failed message handling',
    customer_type: 'new',
    template_type: 'normal',
    status: 'pending',
    retries: 0,
    max_retries: 3
  };
  
  console.log('📝 Adding test message to queue...');
  await database.addToMessageQueue(testMessageData);
  
  // Verify message is in queue
  const queueBefore = await database.getMessageQueue('pending', 10);
  const testMessageInQueue = queueBefore.find(msg => msg.id === testMessageData.id);
  console.log('✅ Message added to queue:', testMessageInQueue ? 'YES' : 'NO');
  
  // Simulate failed message processing
  console.log('🚫 Simulating failed message processing...');
  
  // Create InstagramMessenger instance for testing
  const messenger = new InstagramMessenger(database);
  
  // Test saveFailedMessage method
  const failedMessageData = {
    username: testMessageData.username,
    originalComment: testMessageData.original_comment,
    customerType: testMessageData.customer_type,
    error: 'Max retries reached - test error',
    failedAt: new Date().toISOString(),
    retries: testMessageData.max_retries,
    maxRetries: testMessageData.max_retries,
    timestamp: new Date().toISOString()
  };
  
  console.log('💾 Saving failed message...');
  await messenger.saveFailedMessage(failedMessageData);
  
  console.log('🗑️ Removing message from queue...');
  await database.removeFromMessageQueue(testMessageData.id);
  
  // Verify results
  console.log('\n📊 Verification Results:');
  
  // Check if message was removed from queue
  const queueAfter = await database.getMessageQueue('pending', 10);
  const messageStillInQueue = queueAfter.find(msg => msg.id === testMessageData.id);
  console.log('❌ Message removed from queue:', messageStillInQueue ? 'NO' : 'YES');
  
  // Check if message was saved to failed_messages
  const failedMessages = await database.getFailedMessages(10, 0);
  const failedMessage = failedMessages.find(msg => msg.username === testMessageData.username);
  console.log('💾 Message saved to failed_messages:', failedMessage ? 'YES' : 'NO');
  
  if (failedMessage) {
    console.log('📋 Failed message details:');
    console.log(`   - Username: ${failedMessage.username}`);
    console.log(`   - Status: ${failedMessage.status}`);
    console.log(`   - Error: ${failedMessage.error_message}`);
    console.log(`   - Retry count: ${failedMessage.retry_count}/${failedMessage.max_retries}`);
  }
  
  // Cleanup test data
  console.log('\n🧹 Cleaning up test data...');
  if (failedMessage) {
    await database.markFailedMessageResolved(failedMessage.id, 'test_cleanup', 'Test completed');
    console.log('✅ Test failed message marked as resolved');
  }
  
  console.log('\n✅ Test completed successfully!');
  console.log('\n📝 Summary:');
  console.log('   - Failed messages are now saved to failed_messages table');
  console.log('   - Failed messages are removed from message_queue');
  console.log('   - Different error types get appropriate status values');
  console.log('   - UI can display and manage failed messages');
}

// Run test if this file is executed directly
if (require.main === module) {
  testFailedMessageHandling()
    .then(() => {
      console.log('\n🎉 All tests passed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testFailedMessageHandling };
