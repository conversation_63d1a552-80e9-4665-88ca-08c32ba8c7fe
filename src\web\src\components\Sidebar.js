import React, { useState } from 'react';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';
import {
  History,
  MessageCircle,
  MessageSquare,
  Settings,
  ChevronRight,
  ChevronDown,
  Cog,
  Bell,
  Printer,
  FileText,
  Shield,
  Users,
  Palette,
  Database,
  XCircle,
  Monitor,
  Activity,
  Bug
} from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { useSocket } from '../contexts/SocketContext';

const Sidebar = () => {
  const { state, actions } = useApp();
  const { systemState } = useSocket();
  const { sidebarOpen } = state;
  const location = useLocation();
  const navigate = useNavigate();
  const [settingsDropdownOpen, setSettingsDropdownOpen] = useState(false);

  const navigation = [
    {
      name: 'Bình luận',
      href: '/comments',
      icon: MessageCircle,
      badge: systemState.totalComments > 0 ? systemState.totalComments : null
    },
    {
      name: '<PERSON>hắn tự động',
      href: '/auto-messages',
      icon: MessageSquare,
      badge: null
    },
    {
      name: '<PERSON><PERSON><PERSON> sử',
      href: '/history',
      icon: History,
      badge: null
    },
    {
      name: 'Quản lý khách hàng',
      href: '/customers',
      icon: Users,
      badge: null
    }
  ];

  const settingsSubMenu = [
    {
      name: 'Tổng quan cài đặt',
      href: '/settings',
      icon: Settings,
      description: 'Xem tất cả cài đặt',
      isGeneral: true
    },
    {
      name: 'Thu thập bình luận',
      href: '/settings#scraping',
      icon: Cog,
      description: 'Quản lý thu thập bình luận và đăng nhập'
    },
    {
      name: 'Cài đặt máy in',
      href: '/settings#printer',
      icon: Printer,
      description: 'Cấu hình máy in và định dạng bill'
    },
    {
      name: 'Cài đặt nút hủy',
      href: '/settings#cancel-settings',
      icon: XCircle,
      description: 'Tùy chỉnh tính năng hủy cho nút in và dự bị'
    },
    {
      name: 'Cài đặt màu sắc',
      href: '/settings#colors',
      icon: Palette,
      description: 'Tùy chỉnh màu sắc bình luận'
    },
    {
      name: 'MongoDB Atlas',
      href: '/settings#mongodb',
      icon: Database,
      description: 'Kết nối và đồng bộ với MongoDB Atlas'
    },
    {
      name: 'Auto Message',
      href: '/settings#auto-message',
      icon: MessageSquare,
      description: 'Cài đặt giới hạn message và tự động restart'
    },
    {
      name: 'System Monitor',
      href: '/settings#system-monitor',
      icon: Monitor,
      description: 'Theo dõi hệ thống, CPU và Chrome processes'
    },
    {
      name: 'Debug Comments',
      href: '/settings#debug-comments',
      icon: Bug,
      description: 'Thêm comment giả để test hệ thống'
    },
    {
      name: 'Cài đặt chung',
      href: '/settings#general',
      icon: Bell,
      description: 'Thông báo và cài đặt khác'
    },
    {
      name: 'Cài đặt nâng cao',
      href: '/settings#advanced',
      icon: Shield,
      description: 'Tùy chọn nâng cao và bảo mật'
    }
  ];

  const closeSidebar = () => {
    if (window.innerWidth < 768) {
      actions.setSidebarOpen(false);
    }
  };

  const toggleSettingsDropdown = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Simply toggle dropdown - don't auto-navigate
    setSettingsDropdownOpen(!settingsDropdownOpen);
  };

  const handleSettingsSubMenuClick = (href, item, e) => {
    e.preventDefault();

    // If it's the general settings item, just navigate to settings page
    if (item.isGeneral) {
      navigate('/settings');
      // Scroll to top of the page
      setTimeout(() => {
        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        });
      }, 100);
      closeSidebar();
      return;
    }

    // Extract the hash from href (e.g., '/settings#scraping' -> 'scraping')
    const hash = href.split('#')[1];

    // If we're not on settings page, navigate there first (without hash)
    if (location.pathname !== '/settings') {
      navigate('/settings');
      // Store the target section to scroll to after navigation
      sessionStorage.setItem('scrollToSection', hash);
      return;
    }

    // If we're already on settings page, scroll to the section
    const element = document.getElementById(hash);
    if (element) {
      // Scroll with smooth behavior and offset for header
      const headerOffset = 80; // Adjust based on your header height
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });

      // Add highlight effect
      element.style.transition = 'box-shadow 0.3s ease';
      element.style.boxShadow = '0 0 0 3px rgba(147, 51, 234, 0.3)';
      setTimeout(() => {
        element.style.boxShadow = '';
      }, 2000);
    }

    closeSidebar();
  };

  const isSettingsActive = location.pathname === '/settings';

  return (
    <div className="flex flex-col w-full h-full bg-white border-r border-gray-200">
      {/* Navigation */}
      <nav className="flex-1 px-2 py-4 space-y-1 overflow-y-auto md:pt-0 pt-16">
        {/* Regular navigation items */}
        {navigation.map((item) => {
          const Icon = item.icon;

          return (
            <NavLink
              key={item.name}
              to={item.href}
              onClick={closeSidebar}
              className={({ isActive }) =>
                `group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${isActive
                  ? 'bg-sky-100 text-sky-900 border-r-2 border-sky-500'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`
              }
            >
              {({ isActive }) => (
                <>
                  <Icon
                    className={`flex-shrink-0 h-5 w-5 ${isActive ? 'text-sky-500' : 'text-gray-400 group-hover:text-gray-500'
                      }`}
                  />

                  {sidebarOpen && (
                    <>
                      <span className="ml-3 flex-1">{item.name}</span>

                      {item.badge && (
                        <span className="ml-3 inline-block py-0.5 px-2 text-xs font-medium bg-sky-100 text-sky-800 rounded-full">
                          {item.badge}
                        </span>
                      )}

                      {isActive && (
                        <ChevronRight className="ml-2 h-4 w-4 text-sky-500" />
                      )}
                    </>
                  )}

                  {!sidebarOpen && item.badge && (
                    <span className="absolute left-8 top-1 inline-block w-2 h-2 bg-sky-500 rounded-full"></span>
                  )}
                </>
              )}
            </NavLink>
          );
        })}

        {/* Settings with dropdown */}
        <div className="space-y-1">
          <button
            onClick={toggleSettingsDropdown}
            className={`group flex items-center w-full px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${isSettingsActive
              ? 'bg-sky-100 text-sky-900 border-r-2 border-sky-500'
              : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
          >
            <Settings
              className={`flex-shrink-0 h-5 w-5 ${isSettingsActive ? 'text-sky-500' : 'text-gray-400 group-hover:text-gray-500'
                }`}
            />

            {sidebarOpen && (
              <>
                <span className="ml-3 flex-1 text-left">Cài đặt</span>
                {settingsDropdownOpen ? (
                  <ChevronDown className="ml-2 h-4 w-4 text-gray-400" />
                ) : (
                  <ChevronRight className="ml-2 h-4 w-4 text-gray-400" />
                )}
              </>
            )}
          </button>

          {/* Settings submenu */}
          {sidebarOpen && settingsDropdownOpen && (
            <div className="ml-6 space-y-1">
              {settingsSubMenu.map((item) => {
                const Icon = item.icon;
                return (
                  <a
                    key={item.name}
                    href={item.href}
                    onClick={(e) => handleSettingsSubMenuClick(item.href, item, e)}
                    className={`group flex items-center px-2 py-2 text-sm rounded-md transition-colors duration-200 cursor-pointer ${item.isGeneral
                      ? 'text-sky-600 hover:bg-sky-50 hover:text-sky-700 font-medium'
                      : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    title={item.description}
                  >
                    <Icon className={`flex-shrink-0 h-4 w-4 ${item.isGeneral
                      ? 'text-sky-500 group-hover:text-sky-600'
                      : 'text-gray-400 group-hover:text-gray-500'
                      }`} />
                    <span className="ml-3 text-xs">{item.name}</span>
                  </a>
                );
              })}
            </div>
          )}
        </div>
      </nav>

      {/* System status */}
      {sidebarOpen && (
        <div className="p-4 border-t border-gray-200">
          <div className="bg-gray-50 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Trạng thái</span>
              <div className={`w-2 h-2 rounded-full ${systemState.isRunning ? 'bg-green-500' : 'bg-gray-400'
                }`}></div>
            </div>

            <div className="space-y-1 text-xs text-gray-600">
              <div className="flex justify-between">
                <span>Bình luận:</span>
                <span className="font-medium">{systemState.totalComments}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-blue-600">Đã in:</span>
                <span className="font-medium text-blue-600">{systemState.printedComments}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-green-600">Đã gửi:</span>
                <span className="font-medium text-green-600">{systemState.sentMessages}</span>
              </div>
              <div className="flex justify-between">
                <span>Chờ gửi:</span>
                <span className="font-medium">{systemState.queuedMessages}</span>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Sidebar;
