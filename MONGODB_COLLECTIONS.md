# 🗄️ MongoDB Atlas Collections Overview

## 📋 Collections được sử dụng

Hệ thống CommiLive chỉ đồng bộ **4 collections quan trọng** lên <PERSON>goDB Atlas để tối ưu storage và performance:

### **1. regular_customers**
```javascript
{
  username: String,           // Tên người dùng Instagram
  marked_at: Date,           // Thời gian đánh dấu là khách hàng thường
  notes: String,             // Ghi chú về khách hàng
  updated_at: Date,          // Thời gian cập nhật cuối
  device_id: String,         // ID thiết bị tạo record
  is_deleted: Boolean,       // Đã xóa hay chưa
  synced_at: Date           // Thời gian sync cuối
}
```
**Mục đích**: Quản lý danh sách khách hàng thường xuyên across devices

### **2. instagram_threads**
```javascript
{
  local_id: Number,          // ID từ local SQLite
  thread_id: String,         // Instagram thread ID
  username: String,          // Userna<PERSON> của thread
  thread_url: String,        // URL đầy đủ của thread
  created_at: Date,          // Thời gian tạo
  updated_at: Date,          // Thời gian cập nhật cuối
  device_id: String,         // ID thiết bị
  is_deleted: Boolean,       // Đã xóa hay chưa
  synced_at: Date           // Thời gian sync cuối
}
```
**Mục đích**: Lưu trữ Instagram thread IDs để direct navigation

### **3. printed_history**
```javascript
{
  local_id: Number,          // ID từ local SQLite
  comment_id: String,        // ID của comment
  username: String,          // Username của người comment
  comment_text: String,      // Nội dung comment
  print_type: String,        // 'comment' hoặc 'backup'
  printed_at: Date,         // Thời gian in
  created_at: Date,         // Thời gian tạo record
  device_id: String,        // ID thiết bị
  is_deleted: Boolean,      // Đã xóa hay chưa
  synced_at: Date          // Thời gian sync cuối
}
```
**Mục đích**: Backup lịch sử in để khôi phục khi cần

### **4. send_once_history**
```javascript
{
  local_id: Number,          // ID từ local SQLite
  username: String,          // Username của người nhận
  template_name: String,     // Tên template đã gửi
  customer_type: String,     // 'regular' hoặc 'vip'
  template_type: String,     // 'normal' hoặc 'backup'
  sent_at: Date,            // Thời gian gửi
  created_at: Date,         // Thời gian tạo record
  updated_at: Date,         // Thời gian cập nhật cuối
  device_id: String,        // ID thiết bị
  is_deleted: Boolean,      // Đã xóa hay chưa (auto-cleanup sau 2 ngày)
  synced_at: Date          // Thời gian sync cuối
}
```
**Mục đích**: Tracking send_once templates để tránh gửi duplicate

## ❌ Collections KHÔNG được sử dụng

### **comments** (Đã loại bỏ)
- **Lý do**: Không cần lưu tất cả comments vào Atlas
- **Tác động**: Tiết kiệm storage và bandwidth đáng kể
- **Alternative**: Comments chỉ được lưu local để real-time processing

## 🎯 Lợi ích của việc selective sync

### **1. Storage Optimization**
- Chỉ sync dữ liệu quan trọng và cần thiết
- Không lưu trữ comments (có thể rất nhiều và không cần backup)
- Giảm chi phí MongoDB Atlas storage

### **2. Performance**
- Sync nhanh hơn với ít dữ liệu
- Bandwidth usage thấp hơn
- Query performance tốt hơn

### **3. Data Management**
- Focus vào business-critical data
- Easier backup và restore
- Clear data retention policies

## 🔄 Sync Strategy

### **Real-time Sync:**
- **Customers**: Khi add/remove khách hàng thường
- **Threads**: Khi lưu thread ID mới
- **Printed History**: Khi in comment
- **Send_once History**: Khi gửi send_once template

### **Periodic Sync:**
- **Interval**: Mỗi 5 phút
- **Method**: Smart bidirectional sync
- **Conflict Resolution**: Timestamp-based

### **Cleanup:**
- **Send_once History**: Auto soft-delete sau 2 ngày
- **Printed History**: Auto hard-delete sau 90 ngày (chỉ records đã soft-delete)
- **Soft Delete**: Mark as deleted thay vì hard delete (immediate)
- **Hard Delete**: Permanent deletion sau retention period
- **Sync Deletions**: Đồng bộ soft deletions lên Atlas

## 📊 Storage Estimates

### **Typical Usage (1000 customers, daily usage):**
- **regular_customers**: ~50KB (1000 records × 50 bytes)
- **instagram_threads**: ~20KB (400 threads × 50 bytes)
- **printed_history**: ~500KB/day (1000 prints × 500 bytes)
- **send_once_history**: ~100KB/day (auto-cleanup sau 2 ngày)

**Total**: ~670KB/day (vs. nhiều MB nếu lưu tất cả comments)

## 🛠️ Database Indexes

### **Performance Optimization:**
```javascript
// regular_customers
db.regular_customers.createIndex({ username: 1 }, { unique: true });
db.regular_customers.createIndex({ device_id: 1 });

// instagram_threads  
db.instagram_threads.createIndex({ thread_id: 1 }, { unique: true });
db.instagram_threads.createIndex({ username: 1 });

// printed_history
db.printed_history.createIndex({ username: 1 });
db.printed_history.createIndex({ printed_at: -1 });
db.printed_history.createIndex({ comment_id: 1 });

// send_once_history
db.send_once_history.createIndex({ 
  username: 1, 
  template_name: 1, 
  customer_type: 1, 
  template_type: 1 
}, { unique: true });
```

## 🔍 Monitoring

### **Collection Sizes:**
```bash
# Check collection sizes
db.stats()
db.regular_customers.stats()
db.instagram_threads.stats()
db.printed_history.stats()
db.send_once_history.stats()
```

### **Sync Status:**
- Real-time indicators trong UI
- API endpoints để check sync status
- Socket events cho sync notifications

## 📝 Migration Notes

### **Existing Data:**
- Chỉ sync dữ liệu quan trọng từ local SQLite
- Không migrate comments (giữ local only)
- Auto-migration khi connect MongoDB lần đầu

### **Future Considerations:**
- Có thể thêm collections mới nếu cần
- Flexible architecture cho expansion
- Maintain backward compatibility
