import React, { useEffect, useState, useCallback } from 'react';
import {
  Printer,
  Search,
  Calendar,
  User,
  Clock,
  FileText,
  Trash2,
  UserX,
  Filter,
  X,
  RefreshCw,
  ChevronDown,
  ChevronUp,
  Eye,
  RotateCcw
} from 'lucide-react';
import { useApp } from '../contexts/AppContext';
import { useSocket } from '../contexts/SocketContext';
import SyncStatusIndicator from '../components/SyncStatusIndicator';
import SyncButton from '../components/SyncButton';
import moment from 'moment';
import { showToast, showErrorToast } from '../utils/toastManager';

const History = () => {
  const { actions } = useApp();
  const { socket } = useSocket();
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState('all');
  const [printedComments, setPrintedComments] = useState([]);
  const [groupedComments, setGroupedComments] = useState({});
  const [expandedUsers, setExpandedUsers] = useState({});
  const [loading, setLoading] = useState(false);
  const [showAdvancedFilter, setShowAdvancedFilter] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());
  const [showDeleted, setShowDeleted] = useState(false);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50,
    total: 0,
    totalPages: 0
  });

  // Advanced filter states
  const [advancedFilter, setAdvancedFilter] = useState({
    filterType: 'date', // 'date', 'time', 'datetime'
    startDate: '',
    endDate: '',
    startTime: '00:00',
    endTime: '23:59',
    applyTimeToAllDays: false
  });

  // Applied filter state (only changes when user clicks "Apply")
  const [appliedAdvancedFilter, setAppliedAdvancedFilter] = useState({
    filterType: 'date',
    startDate: '',
    endDate: '',
    startTime: '00:00',
    endTime: '23:59',
    applyTimeToAllDays: false
  });

  useEffect(() => {
    actions.setCurrentPage('history');
    loadPrintedComments();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Load printed comments when filters change
  useEffect(() => {
    loadPrintedComments();
  }, [searchTerm, dateFilter, pagination.page, appliedAdvancedFilter, showDeleted]); // eslint-disable-line react-hooks/exhaustive-deps

  // Auto-refresh when new comments are printed
  useEffect(() => {
    if (socket) {
      const handleCommentPrinted = () => {
        setLastRefresh(Date.now());
        loadPrintedComments();
      };

      socket.on('comment-printed', handleCommentPrinted);
      socket.on('print-success', handleCommentPrinted);

      return () => {
        socket.off('comment-printed', handleCommentPrinted);
        socket.off('print-success', handleCommentPrinted);
      };
    }
  }, [socket]); // eslint-disable-line react-hooks/exhaustive-deps

  // Group comments by user
  const groupCommentsByUser = useCallback((comments) => {
    const grouped = {};
    comments.forEach(comment => {
      const username = comment.username;
      if (!grouped[username]) {
        grouped[username] = {
          username,
          comments: [],
          totalComments: 0,
          latestPrintTime: null
        };
      }
      grouped[username].comments.push(comment);
      grouped[username].totalComments++;

      const printTime = new Date(comment.printed_at);
      if (!grouped[username].latestPrintTime || printTime > grouped[username].latestPrintTime) {
        grouped[username].latestPrintTime = printTime;
      }
    });

    // Sort comments within each user by print time (newest first)
    Object.values(grouped).forEach(userGroup => {
      userGroup.comments.sort((a, b) => new Date(b.printed_at) - new Date(a.printed_at));
    });

    return grouped;
  }, []);

  useEffect(() => {
    const grouped = groupCommentsByUser(printedComments);
    setGroupedComments(grouped);

    // Auto-expand all users by default
    const userCount = Object.keys(grouped).length;
    if (userCount > 0) {
      setExpandedUsers(prevExpanded => {
        const autoExpanded = {};
        Object.keys(grouped).forEach(username => {
          // Only auto-expand if this user hasn't been manually toggled before
          if (!(username in prevExpanded)) {
            autoExpanded[username] = true;
          } else {
            // Keep existing state for users that were manually toggled
            autoExpanded[username] = prevExpanded[username];
          }
        });
        return autoExpanded;
      });
    }
  }, [printedComments, groupCommentsByUser]);

  const loadPrintedComments = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        search: searchTerm,
        dateFilter
      });

      // Add advanced filter params (use applied filter, not current editing filter)
      if (appliedAdvancedFilter.startDate) {
        params.append('startDate', appliedAdvancedFilter.startDate);
      }
      if (appliedAdvancedFilter.endDate) {
        params.append('endDate', appliedAdvancedFilter.endDate);
      }
      if (appliedAdvancedFilter.startTime) {
        params.append('startTime', appliedAdvancedFilter.startTime);
      }
      if (appliedAdvancedFilter.endTime) {
        params.append('endTime', appliedAdvancedFilter.endTime);
      }
      if (appliedAdvancedFilter.filterType) {
        params.append('filterType', appliedAdvancedFilter.filterType);
      }
      if (appliedAdvancedFilter.applyTimeToAllDays) {
        params.append('applyTimeToAllDays', 'true');
      }

      // Use different endpoint based on showDeleted state
      const endpoint = showDeleted ? `/api/history/deleted?${params}` : `/api/history?${params}`;
      const response = await fetch(endpoint);
      const data = await response.json();

      if (data.success) {
        // Map printed_history fields to expected frontend fields
        const mappedHistory = (data.history || []).map(item => ({
          id: item.id,
          comment_id: item.comment_id,
          username: item.username,
          comment_text: item.comment_text,
          text: item.comment_text, // Alias for compatibility
          print_type: item.print_type,
          printed_at: item.printed_at,
          created_at: item.created_at
        }));

        setPrintedComments(mappedHistory);
        setPagination(prev => ({
          ...prev,
          total: data.total,
          totalPages: data.totalPages
        }));
      }
    } catch (error) {
      console.error('Failed to load printed comments:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle search with debounce
  const handleSearchChange = (value) => {
    setSearchTerm(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handleDateFilterChange = (value) => {
    setDateFilter(value);
    setPagination(prev => ({ ...prev, page: 1 }));
  };

  const handlePrintUserHistory = async (username) => {
    try {
      const response = await fetch('/api/print-user-history', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username }),
      });

      const data = await response.json();
      if (data.success) {
        showToast(`Đã in lịch sử của @${username}`, 'success');
      } else {
        throw new Error(data.error || 'Failed to print user history');
      }
    } catch (error) {
      showErrorToast('Lỗi khi in lịch sử: ' + error.message);
    }
  };

  const handleReprintComment = async (comment) => {
    try {
      const response = await fetch('/api/print-comment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: comment.username,
          text: comment.comment_text || comment.text,
          timestamp: comment.printed_at
        }),
      });

      const data = await response.json();
      if (data.success) {
        showToast(`Đã in lại bình luận của @${comment.username}`, 'success');
      } else {
        throw new Error(data.error || 'Failed to reprint comment');
      }
    } catch (error) {
      showErrorToast('Lỗi khi in lại: ' + error.message);
    }
  };

  const handleRestoreComment = async (comment) => {
    try {
      const response = await fetch(`/api/comments/${comment.id}/restore`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      if (data.success) {
        showToast(`Đã khôi phục bình luận của @${comment.username}`, 'success');
        loadPrintedComments(); // Reload to update the list
      } else {
        throw new Error(data.error || 'Failed to restore comment');
      }
    } catch (error) {
      showErrorToast('Lỗi khi khôi phục: ' + error.message);
    }
  };

  const handleRestoreUserHistory = async (username) => {
    try {
      const response = await fetch(`/api/comments/user/${username}/restore`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      if (data.success) {
        showToast(`Đã khôi phục ${data.restoredCount} bình luận của @${username}`, 'success');
        loadPrintedComments(); // Reload to update the list
      } else {
        throw new Error(data.error || 'Failed to restore user history');
      }
    } catch (error) {
      showErrorToast('Lỗi khi khôi phục lịch sử: ' + error.message);
    }
  };

  const handleDeleteComment = async (commentId, username) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa bình luận này?')) {
      return;
    }

    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        showToast('Đã xóa bình luận', 'success');
        loadPrintedComments(); // Reload list
      } else {
        throw new Error(data.error || 'Failed to delete comment');
      }
    } catch (error) {
      showErrorToast('Lỗi khi xóa bình luận: ' + error.message);
    }
  };

  const handleDeleteUserComments = async (username) => {
    // Get the user's comments that are currently displayed (filtered)
    const userGroup = groupedComments[username];
    if (!userGroup || userGroup.comments.length === 0) {
      showErrorToast('Không có bình luận nào để xóa');
      return;
    }

    const displayedCommentsCount = userGroup.comments.length;
    const confirmMessage = searchTerm || dateFilter !== 'all' ||
      appliedAdvancedFilter.startDate || appliedAdvancedFilter.endDate ||
      appliedAdvancedFilter.startTime !== '00:00' || appliedAdvancedFilter.endTime !== '23:59'
      ? `Bạn có chắc chắn muốn xóa ${displayedCommentsCount} bình luận ĐANG HIỂN THỊ của @${username}? (Chỉ xóa những bình luận phù hợp với bộ lọc hiện tại)`
      : `Bạn có chắc chắn muốn xóa TẤT CẢ ${displayedCommentsCount} bình luận của @${username}?`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      // Delete each comment individually to respect the current filter
      // Use the printed_history ID (not comment_id)
      const historyIds = userGroup.comments.map(comment => comment.id);
      let deletedCount = 0;
      let errors = [];

      for (const historyId of historyIds) {
        try {
          const response = await fetch(`/api/comments/${historyId}`, {
            method: 'DELETE',
          });

          const data = await response.json();
          if (data.success) {
            deletedCount++;
          } else {
            errors.push(`History ${historyId}: ${data.error}`);
          }
        } catch (error) {
          errors.push(`History ${historyId}: ${error.message}`);
        }
      }

      if (deletedCount > 0) {
        showToast(`Đã xóa ${deletedCount} bình luận của @${username}`, 'success');
        loadPrintedComments(); // Reload list
      }

      if (errors.length > 0) {
        console.error('Some deletions failed:', errors);
        showErrorToast(`Xóa thành công ${deletedCount}/${historyIds.length} bình luận. Một số lỗi xảy ra.`);
      }
    } catch (error) {
      showErrorToast('Lỗi khi xóa bình luận: ' + error.message);
    }
  };

  const applyAdvancedFilter = () => {
    // Apply the current filter settings
    setAppliedAdvancedFilter({ ...advancedFilter });
    setPagination(prev => ({ ...prev, page: 1 }));
    setShowAdvancedFilter(false);
    // loadPrintedComments will be called automatically by useEffect when appliedAdvancedFilter changes
  };

  const clearAdvancedFilter = () => {
    const clearedFilter = {
      filterType: 'date',
      startDate: '',
      endDate: '',
      startTime: '00:00',
      endTime: '23:59',
      applyTimeToAllDays: false
    };
    setAdvancedFilter(clearedFilter);
    setAppliedAdvancedFilter(clearedFilter);
    setDateFilter('all');
    setPagination(prev => ({ ...prev, page: 1 }));
    // loadPrintedComments will be called automatically by useEffect
  };

  const toggleUserExpansion = (username) => {
    setExpandedUsers(prev => ({
      ...prev,
      [username]: !prev[username]
    }));
  };

  const manualRefresh = () => {
    setLastRefresh(Date.now());
    loadPrintedComments();
  };

  // Sort users by latest print time (newest first)
  const sortedUsers = Object.values(groupedComments).sort((a, b) =>
    new Date(b.latestPrintTime) - new Date(a.latestPrintTime)
  );

  // Check if any filters are active (use applied filter for display)
  const hasActiveFilters = searchTerm || dateFilter !== 'all' ||
    appliedAdvancedFilter.startDate || appliedAdvancedFilter.endDate ||
    appliedAdvancedFilter.startTime !== '00:00' || appliedAdvancedFilter.endTime !== '23:59';

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Printer className="h-6 w-6 text-blue-600" />
            <h1 className="text-lg font-semibold text-gray-900">Lịch sử in</h1>
            {/* MongoDB Sync Status & Button */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 px-3 py-1 bg-gray-50 rounded-lg border">
                <SyncStatusIndicator
                  type="history"
                  size="sm"
                  showText={true}
                />
              </div>
              <SyncButton
                type="printed-history"
                size="sm"
                showText={true}
                onSyncComplete={() => {
                  // Reload history after sync
                  loadPrintedComments();
                }}
              />
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {/* Toggle Deleted View */}
            <button
              onClick={() => {
                setShowDeleted(!showDeleted);
                setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page
              }}
              className={`inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                showDeleted
                  ? 'bg-red-100 text-red-700 hover:bg-red-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              title={showDeleted ? "Xem lịch sử thường" : "Xem đã xóa"}
            >
              {showDeleted ? (
                <>
                  <Eye className="h-4 w-4 mr-1" />
                  Xem thường
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-1" />
                  Xem đã xóa
                </>
              )}
            </button>
            <button
              onClick={manualRefresh}
              className="inline-flex items-center px-3 py-1 rounded-lg text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
              title="Làm mới"
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Làm mới
            </button>
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span>
                {pagination.total} bình luận • {Object.keys(groupedComments).length} người dùng
              </span>
              {hasActiveFilters && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  <Filter className="h-3 w-3 mr-1" />
                  Đã lọc
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Active Filter Display */}
        {hasActiveFilters && (
          <div className="mt-3 flex flex-wrap gap-2">
            {searchTerm && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700">
                Tìm kiếm: "{searchTerm}"
              </span>
            )}
            {dateFilter !== 'all' && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700">
                Thời gian: {dateFilter === 'today' ? 'Hôm nay' : dateFilter === 'yesterday' ? 'Hôm qua' : dateFilter === 'week' ? '7 ngày qua' : dateFilter}
              </span>
            )}
            {appliedAdvancedFilter.startDate && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
                Từ ngày: {appliedAdvancedFilter.startDate}
              </span>
            )}
            {appliedAdvancedFilter.endDate && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
                Đến ngày: {appliedAdvancedFilter.endDate}
              </span>
            )}
            {appliedAdvancedFilter.startTime !== '00:00' && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
                Từ giờ: {appliedAdvancedFilter.startTime}
              </span>
            )}
            {appliedAdvancedFilter.endTime !== '23:59' && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
                Đến giờ: {appliedAdvancedFilter.endTime}
              </span>
            )}
            {appliedAdvancedFilter.applyTimeToAllDays && (
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-700">
                Áp dụng cho tất cả ngày
              </span>
            )}
            <button
              onClick={clearAdvancedFilter}
              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-700 hover:bg-red-200"
            >
              <X className="h-3 w-3 mr-1" />
              Xóa tất cả bộ lọc
            </button>
          </div>
        )}


        <div className="flex flex-col gap-3">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Tìm kiếm theo tên hoặc nội dung..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-gray-400" />
              <select
                value={dateFilter}
                onChange={(e) => handleDateFilterChange(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="all">Tất cả</option>
                <option value="today">Hôm nay</option>
                <option value="yesterday">Hôm qua</option>
                <option value="week">7 ngày qua</option>
              </select>

              <button
                onClick={() => setShowAdvancedFilter(!showAdvancedFilter)}
                className={`inline-flex items-center px-3 py-2 border rounded-lg text-sm font-medium transition-colors ${showAdvancedFilter
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : (appliedAdvancedFilter.startDate || appliedAdvancedFilter.endDate ||
                     appliedAdvancedFilter.startTime !== '00:00' || appliedAdvancedFilter.endTime !== '23:59')
                    ? 'border-blue-500 bg-blue-100 text-blue-700'
                    : 'border-gray-300 bg-white text-gray-700 hover:bg-gray-50'
                  }`}
              >
                <Filter className="h-4 w-4 mr-2" />
                Lọc nâng cao
                {(appliedAdvancedFilter.startDate || appliedAdvancedFilter.endDate ||
                  appliedAdvancedFilter.startTime !== '00:00' || appliedAdvancedFilter.endTime !== '23:59') && (
                  <span className="ml-1 inline-flex items-center justify-center w-2 h-2 bg-blue-600 rounded-full"></span>
                )}
              </button>
            </div>
          </div>

          {/* Advanced Filter Panel */}
          {showAdvancedFilter && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-medium text-gray-900">Bộ lọc nâng cao</h3>
                <button
                  onClick={() => setShowAdvancedFilter(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>

              <div className="space-y-4">
                {/* Filter Type */}
                <div>
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Loại lọc
                  </label>
                  <select
                    value={advancedFilter.filterType}
                    onChange={(e) => setAdvancedFilter(prev => ({ ...prev, filterType: e.target.value }))}
                    className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                  >
                    <option value="date">Theo ngày</option>
                    <option value="time">Theo giờ (mỗi ngày)</option>
                    <option value="datetime">Theo khoảng giờ trong nhiều ngày</option>
                  </select>

                  {/* Filter description */}
                  <div className="mt-2 text-xs text-gray-500">
                    {advancedFilter.filterType === 'date' && (
                      <span>Lọc theo khoảng ngày (VD: từ 5/6 đến 7/6)</span>
                    )}
                    {advancedFilter.filterType === 'time' && (
                      <span>Lọc theo khoảng giờ mỗi ngày (VD: 8h00-10h00 mỗi ngày)</span>
                    )}
                    {advancedFilter.filterType === 'datetime' && (
                      <span>Lọc theo khoảng giờ trong nhiều ngày (VD: 10h00-20h00 từ ngày 5/6 đến 7/6)</span>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {/* Start Date */}
                  {(advancedFilter.filterType === 'date' || advancedFilter.filterType === 'datetime') && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Từ ngày
                      </label>
                      <input
                        type="date"
                        value={advancedFilter.startDate}
                        onChange={(e) => setAdvancedFilter(prev => ({ ...prev, startDate: e.target.value }))}
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                      />
                    </div>
                  )}

                  {/* End Date */}
                  {(advancedFilter.filterType === 'date' || advancedFilter.filterType === 'datetime') && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Đến ngày
                      </label>
                      <input
                        type="date"
                        value={advancedFilter.endDate}
                        onChange={(e) => setAdvancedFilter(prev => ({ ...prev, endDate: e.target.value }))}
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                      />
                    </div>
                  )}

                  {/* Start Time */}
                  {(advancedFilter.filterType === 'time' || advancedFilter.filterType === 'datetime') && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Từ giờ
                      </label>
                      <input
                        type="time"
                        value={advancedFilter.startTime}
                        onChange={(e) => setAdvancedFilter(prev => ({ ...prev, startTime: e.target.value }))}
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                      />
                    </div>
                  )}

                  {/* End Time */}
                  {(advancedFilter.filterType === 'time' || advancedFilter.filterType === 'datetime') && (
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Đến giờ
                      </label>
                      <input
                        type="time"
                        value={advancedFilter.endTime}
                        onChange={(e) => setAdvancedFilter(prev => ({ ...prev, endTime: e.target.value }))}
                        className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
                      />
                    </div>
                  )}

                  {/* Apply Time to All Days */}
                  {advancedFilter.filterType === 'time' && (
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="applyTimeToAllDays"
                        checked={advancedFilter.applyTimeToAllDays}
                        onChange={(e) => setAdvancedFilter(prev => ({ ...prev, applyTimeToAllDays: e.target.checked }))}
                        className="mr-2"
                      />
                      <label htmlFor="applyTimeToAllDays" className="text-xs text-gray-700">
                        Áp dụng cho tất cả các ngày
                      </label>
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center justify-end space-x-2 mt-4">
                <button
                  onClick={clearAdvancedFilter}
                  className="px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                >
                  Xóa bộ lọc
                </button>
                <button
                  onClick={applyAdvancedFilter}
                  className="px-4 py-2 bg-blue-600 text-white text-sm rounded-lg hover:bg-blue-700"
                >
                  Áp dụng
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* History List */}
      <div className="flex-1 overflow-y-auto p-4">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Đang tải lịch sử...</p>
            </div>
          </div>
        ) : sortedUsers.length > 0 ? (
          <div className="space-y-4">
            {sortedUsers.map((userGroup) => (
              <div
                key={userGroup.username}
                className="bg-white rounded-lg border border-gray-200 overflow-hidden"
              >
                {/* User Header */}
                <div className="bg-gray-50 border-b border-gray-200 p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-sm font-semibold text-gray-900">
                          @{userGroup.username}
                        </h3>
                        <p className="text-xs text-gray-500">
                          {userGroup.totalComments} bình luận • Lần cuối: {moment(userGroup.latestPrintTime).format('DD/MM HH:mm')}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      {showDeleted ? (
                        // Deleted view - show restore button
                        <button
                          onClick={() => handleRestoreUserHistory(userGroup.username)}
                          className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                          title={`Khôi phục ${userGroup.totalComments} bình luận đã xóa của @${userGroup.username}`}
                        >
                          <RotateCcw className="h-3 w-3 mr-1" />
                          Khôi phục ({userGroup.totalComments})
                        </button>
                      ) : (
                        // Normal view - show delete and print buttons
                        <>
                          <button
                            onClick={() => handleDeleteUserComments(userGroup.username)}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 transition-colors"
                            title={`Xóa ${userGroup.totalComments} bình luận đang hiển thị của @${userGroup.username}`}
                          >
                            <UserX className="h-3 w-3 mr-1" />
                            Xóa ({userGroup.totalComments})
                          </button>

                          <button
                            onClick={() => handlePrintUserHistory(userGroup.username)}
                            className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                            title={`In tất cả bình luận của @${userGroup.username}`}
                          >
                            <FileText className="h-3 w-3 mr-1" />
                            In lịch sử
                          </button>
                        </>
                      )}

                      <button
                        onClick={() => toggleUserExpansion(userGroup.username)}
                        className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors"
                      >
                        {expandedUsers[userGroup.username] ? (
                          <>
                            <ChevronUp className="h-3 w-3 mr-1" />
                            Thu gọn
                          </>
                        ) : (
                          <>
                            <ChevronDown className="h-3 w-3 mr-1" />
                            Xem chi tiết
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>

                {/* Comments List */}
                {expandedUsers[userGroup.username] && (
                  <div className="divide-y divide-gray-100">
                    {userGroup.comments.map((comment) => (
                      <div key={comment.id} className="p-4 hover:bg-gray-50 transition-colors">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-700 mb-2 break-words">
                              {comment.comment_text || comment.text}
                            </p>
                            <div className="flex items-center space-x-4 text-xs text-gray-500">
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>{moment(comment.printed_at).format('DD/MM/YYYY HH:mm:ss')}</span>
                              </div>
                              <span>Loại: {comment.print_type || 'comment'}</span>
                              {/* Individual comment sync status */}
                              {comment.synced_at && (
                                <div className="flex items-center space-x-1 text-green-600">
                                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                                  <span>Atlas</span>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2 ml-4">
                            {showDeleted ? (
                              // Deleted view - show restore button
                              <button
                                onClick={() => handleRestoreComment(comment)}
                                className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
                                title="Khôi phục bình luận này"
                              >
                                <RotateCcw className="h-3 w-3 mr-1" />
                                Khôi phục
                              </button>
                            ) : (
                              // Normal view - show reprint and delete buttons
                              <>
                                <button
                                  onClick={() => handleReprintComment(comment)}
                                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors"
                                  title="In lại bình luận này"
                                >
                                  <Printer className="h-3 w-3 mr-1" />
                                  In lại
                                </button>

                                <button
                                  onClick={() => handleDeleteComment(comment.id, comment.username)}
                                  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 transition-colors"
                                  title="Xóa lịch sử in này"
                                >
                                  <Trash2 className="h-3 w-3 mr-1" />
                                  Xóa
                                </button>
                              </>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-500">
            <Printer className="h-16 w-16 mb-4 text-gray-300" />
            <h3 className="text-lg font-medium mb-2">
              {searchTerm || dateFilter !== 'all'
                ? 'Không tìm thấy kết quả'
                : showDeleted
                  ? 'Chưa có bình luận nào bị xóa'
                  : 'Chưa có bình luận nào được in'
              }
            </h3>
            <p className="text-sm text-center">
              {searchTerm || dateFilter !== 'all'
                ? 'Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc thời gian'
                : showDeleted
                  ? 'Các bình luận đã xóa sẽ xuất hiện ở đây'
                  : 'Lịch sử các bình luận đã in sẽ xuất hiện ở đây'
              }
            </p>
          </div>
        )}
      </div>

      {/* Footer Stats */}
      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center space-x-4">
            <span>
              {hasActiveFilters ? 'Hiển thị' : 'Tổng cộng'}: {pagination.total} bình luận {showDeleted ? 'đã xóa' : 'đã in'}
            </span>
            <span>•</span>
            <span>{Object.keys(groupedComments).length} người dùng</span>
            <span>•</span>
            <span>Cập nhật lúc: {moment(lastRefresh).format('HH:mm:ss')}</span>
            {hasActiveFilters && (
              <>
                <span>•</span>
                <span className="text-blue-600 font-medium">Đang áp dụng bộ lọc</span>
              </>
            )}
            <span>•</span>
            <div className="flex items-center">
              <span className="mr-2">Atlas:</span>
              <SyncStatusIndicator
                type="history"
                size="xs"
                showText={false}
              />
            </div>
          </div>
          <span>Trang {pagination.page}/{pagination.totalPages}</span>
        </div>
      </div>
    </div>
  );
};

export default History;
