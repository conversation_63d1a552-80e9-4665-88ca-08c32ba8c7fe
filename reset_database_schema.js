#!/usr/bin/env node

/**
 * Reset và rebuild database schema cho API-based comment flow
 * Xóa printed_history cũ và tạo lại với schema mới
 */

const sqlite3 = require('sqlite3').verbose();
const fs = require('fs').promises;
const path = require('path');

// Database path
const DB_PATH = './src/backend/data/instagram_live.db';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function runQuery(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.run(sql, params, function (error) {
      if (error) {
        reject(error);
      } else {
        resolve({ id: this.lastID, changes: this.changes });
      }
    });
  });
}

function queryDatabase(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

async function getTableCount(db, tableName) {
  try {
    const result = await queryDatabase(db, `SELECT COUNT(*) as count FROM ${tableName}`);
    return result[0].count;
  } catch (error) {
    return 0;
  }
}

async function resetPrintedHistoryTable(db) {
  try {
    logInfo('Resetting printed_history table...');
    
    // Get current count
    const currentCount = await getTableCount(db, 'printed_history');
    logWarning(`Current printed_history records: ${currentCount}`);
    
    // Drop existing table
    await runQuery(db, 'DROP TABLE IF EXISTS printed_history');
    logSuccess('Dropped old printed_history table');
    
    // Create new table with optimized schema for API flow
    const newTableSQL = `
      CREATE TABLE printed_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        comment_pk TEXT NOT NULL,           -- Instagram pk (direct from API)
        username TEXT NOT NULL,
        comment_text TEXT NOT NULL,
        print_type TEXT DEFAULT 'comment', -- 'comment' or 'backup'
        printed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        device_id TEXT NOT NULL,           -- Always required, no fallback
        is_deleted INTEGER DEFAULT 0,
        synced_at DATETIME DEFAULT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `;
    
    await runQuery(db, newTableSQL);
    logSuccess('Created new printed_history table with API-optimized schema');
    
    // Create indexes for better performance
    await runQuery(db, 'CREATE INDEX IF NOT EXISTS idx_printed_history_comment_pk ON printed_history(comment_pk)');
    await runQuery(db, 'CREATE INDEX IF NOT EXISTS idx_printed_history_username ON printed_history(username)');
    await runQuery(db, 'CREATE INDEX IF NOT EXISTS idx_printed_history_device_id ON printed_history(device_id)');
    await runQuery(db, 'CREATE INDEX IF NOT EXISTS idx_printed_history_printed_at ON printed_history(printed_at)');
    await runQuery(db, 'CREATE INDEX IF NOT EXISTS idx_printed_history_is_deleted ON printed_history(is_deleted)');
    
    logSuccess('Created indexes for printed_history table');
    
    return { success: true, deletedRecords: currentCount };
    
  } catch (error) {
    logError(`Failed to reset printed_history table: ${error.message}`);
    throw error;
  }
}

async function ensureDeviceId(db) {
  try {
    logInfo('Ensuring device_id is set...');
    
    // Check if device_id exists in settings
    const deviceIdResult = await queryDatabase(db, 'SELECT value FROM settings WHERE key = ?', ['device_id']);
    
    if (deviceIdResult.length === 0 || !deviceIdResult[0].value) {
      // Generate new device ID
      const newDeviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      await runQuery(db, 
        'INSERT OR REPLACE INTO settings (key, value, type, description, updated_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)',
        ['device_id', newDeviceId, 'string', 'Unique device identifier for sync']
      );
      
      logSuccess(`Generated new device_id: ${newDeviceId}`);
      return newDeviceId;
    } else {
      const existingDeviceId = deviceIdResult[0].value;
      logInfo(`Using existing device_id: ${existingDeviceId}`);
      return existingDeviceId;
    }
    
  } catch (error) {
    logError(`Failed to ensure device_id: ${error.message}`);
    throw error;
  }
}

async function updateDatabaseVersion(db) {
  try {
    logInfo('Updating database version...');
    
    const version = '2.0.0-api-optimized';
    const timestamp = new Date().toISOString();
    
    await runQuery(db, 
      'INSERT OR REPLACE INTO settings (key, value, type, description, updated_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)',
      ['database_version', version, 'string', `Database schema version - Updated: ${timestamp}`]
    );
    
    await runQuery(db, 
      'INSERT OR REPLACE INTO settings (key, value, type, description, updated_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)',
      ['schema_reset_timestamp', timestamp, 'string', 'Timestamp when database schema was reset for API optimization']
    );
    
    logSuccess(`Database version updated to: ${version}`);
    
  } catch (error) {
    logError(`Failed to update database version: ${error.message}`);
    throw error;
  }
}

async function getTableStats(db) {
  try {
    const tables = ['regular_customers', 'instagram_threads', 'send_once_history', 'printed_history', 'settings'];
    const stats = {};
    
    for (const table of tables) {
      stats[table] = await getTableCount(db, table);
    }
    
    return stats;
  } catch (error) {
    logError(`Failed to get table stats: ${error.message}`);
    return {};
  }
}

async function runDatabaseReset() {
  log('🚀 Starting database schema reset for API optimization...', 'bold');
  
  try {
    // Check if database exists
    try {
      await fs.access(DB_PATH);
    } catch (error) {
      logError(`Database file not found: ${DB_PATH}`);
      return;
    }
    
    // Open database
    const db = new sqlite3.Database(DB_PATH);
    
    try {
      // Get stats before reset
      logInfo('Getting table statistics before reset...');
      const statsBefore = await getTableStats(db);
      
      log('\n=== BEFORE RESET ===', 'bold');
      Object.entries(statsBefore).forEach(([table, count]) => {
        log(`  ${table}: ${count} records`, count > 0 ? 'blue' : 'yellow');
      });
      
      // Confirm reset
      logWarning(`\nThis will DELETE ${statsBefore.printed_history || 0} printed_history records!`);
      logInfo('Backup has been created in: database_backup_2025-06-30T08-20-24-915Z/');
      
      // Reset printed_history table
      const resetResult = await resetPrintedHistoryTable(db);
      
      // Ensure device_id is set
      const deviceId = await ensureDeviceId(db);
      
      // Update database version
      await updateDatabaseVersion(db);
      
      // Get stats after reset
      const statsAfter = await getTableStats(db);
      
      log('\n=== AFTER RESET ===', 'bold');
      Object.entries(statsAfter).forEach(([table, count]) => {
        log(`  ${table}: ${count} records`, 'green');
      });
      
      // Summary
      log('\n=== RESET SUMMARY ===', 'bold');
      logSuccess(`Deleted ${resetResult.deletedRecords} old printed_history records`);
      logSuccess('Created new API-optimized printed_history table');
      logSuccess(`Device ID: ${deviceId}`);
      logSuccess('Database version updated to 2.0.0-api-optimized');
      
      logInfo('\nNext steps:');
      logInfo('1. Clear MongoDB printed_history collection');
      logInfo('2. Update sync logic in code');
      logInfo('3. Test API comment flow');
      
      logSuccess('Database schema reset completed! 🎉');
      
    } finally {
      // Close database
      db.close();
    }
    
  } catch (error) {
    logError(`Database reset failed: ${error.message}`);
    throw error;
  }
}

// Confirmation prompt
async function confirmReset() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    rl.question('Are you sure you want to reset the database? This will delete all printed_history records! (yes/no): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

// Main execution
async function main() {
  try {
    logWarning('⚠️  DATABASE RESET WARNING ⚠️');
    logWarning('This will permanently delete all printed_history records!');
    logInfo('Backup has been created for safety.');
    
    const confirmed = await confirmReset();
    
    if (!confirmed) {
      logInfo('Reset cancelled by user.');
      process.exit(0);
    }
    
    await runDatabaseReset();
    
  } catch (error) {
    logError(`Reset script failed: ${error.message}`);
    process.exit(1);
  }
}

main();
