# 📨 Message Request Handling Feature

## 🎯 Tính năng mới

**User requirement:** "Khi gửi tin nhắn tới 1 user mới và chưa được lưu thread id, có khả năng sẽ phải bấm accept khi tới bước vào được tin nhắn direct với user đó (vì user đó nhắn với mình trước, thì insta sẽ bắt xác nhận). Khi vào đến direct sẽ bấm accept và chọn primary, sau đó tiếp tục như bình thường."

## 🔍 Vấn đề cần giải quyết

### **Scenario: Message Request Dialog**

#### **Khi nào xảy ra:**
1. **User mới**: Gửi tin nhắn tới user chưa từng nhắn tin
2. **User đã nhắn trước**: User đó đã gửi message request cho mình trước đó
3. **Chưa có thread ID**: Chưa đượ<PERSON> lư<PERSON> trong database

#### **Instagram behavior:**
```
1. Navigate to user profile
   ↓
2. Click "Message" button
   ↓
3. Redirect to direct message
   ↓
4. ❌ DIALOG APPEARS: "Accept message request?"
   ↓
5. User must click "Accept"
   ↓
6. ❌ DIALOG APPEARS: "Move to Primary or General?"
   ↓
7. User must click "Primary" (recommended)
   ↓
8. ✅ Now can send messages normally
```

## ✅ Solution Implemented

### **Auto-Handle Message Request Flow**

#### **Integration Point:**
```javascript
// Added after waitForChatRedirect() in sendMessage()
// Step 6: Check and handle message request if needed (for new users)
this.logger.info('🔍 Checking for message request dialog...');
await this.handleMessageRequestIfNeeded(messageData.username);
```

#### **Method: handleMessageRequestIfNeeded()**

### **Step 1: Detect Accept Button**
```javascript
// Method 1: Look for Accept button by text content
const buttons = await this.page.$$('button, [role="button"]');

for (const button of buttons) {
  const text = await this.page.evaluate(el => el.textContent?.trim(), button);
  if (text && (
    text.includes('Accept') || 
    text.includes('Chấp nhận') ||
    text.includes('Accept Request') ||
    text.includes('Chấp nhận yêu cầu')
  )) {
    // Click Accept button
    await button.click();
    acceptButtonFound = true;
    break;
  }
}
```

### **Step 2: Handle Primary/General Choice**
```javascript
// Method 2: If Accept button was clicked, look for Primary/General option
if (acceptButtonFound) {
  const optionButtons = await this.page.$$('button, [role="button"]');
  
  for (const button of optionButtons) {
    const text = await this.page.evaluate(el => el.textContent?.trim(), button);
    if (text && (
      text.includes('Primary') || 
      text.includes('General') ||
      text.includes('Chính') ||
      text.includes('Tổng quát') ||
      text.includes('Move to Primary') ||
      text.includes('Chuyển về Chính')
    )) {
      // Click Primary option (recommended)
      await button.click();
      primaryButtonFound = true;
      break;
    }
  }
}
```

### **Step 3: Graceful Error Handling**
```javascript
} catch (error) {
  this.logger.warn(`⚠️ Error handling message request for @${username}:`, error.message);
  // Don't throw error - this is not critical, continue with normal flow
}
```

## 📊 Flow Integration

### **Before (Without Message Request Handling):**
```
1. Navigate to user profile ✅
   ↓
2. Click Message button ✅
   ↓
3. Wait for chat redirect ✅
   ↓
4. Extract thread ID ✅
   ↓
5. ❌ STUCK: Message request dialog blocks sending
   ↓
6. ❌ FAIL: Cannot send message
```

### **After (With Message Request Handling):**
```
1. Navigate to user profile ✅
   ↓
2. Click Message button ✅
   ↓
3. Wait for chat redirect ✅
   ↓
4. Extract thread ID ✅
   ↓
5. ✅ NEW: Check for message request dialog
   ↓
6. ✅ NEW: Auto-click Accept if found
   ↓
7. ✅ NEW: Auto-click Primary if found
   ↓
8. Check message loading health ✅
   ↓
9. Send template messages ✅
```

## 🎯 Supported Languages

### **English:**
- "Accept"
- "Accept Request"
- "Primary"
- "General"
- "Move to Primary"

### **Vietnamese:**
- "Chấp nhận"
- "Chấp nhận yêu cầu"
- "Chính"
- "Tổng quát"
- "Chuyển về Chính"

## 🔧 Technical Details

### **Detection Method:**
- **Text-based detection**: Searches for button text content
- **Multiple selectors**: `button, [role="button"]`
- **Visibility check**: Ensures button is actually clickable
- **Language support**: English and Vietnamese

### **Timing:**
- **Initial wait**: 2 seconds for dialogs to appear
- **After Accept**: 1 second wait before looking for Primary option
- **After Primary**: 1.5 seconds wait before continuing

### **Error Handling:**
- **Non-critical**: Errors don't stop message sending
- **Graceful fallback**: Continues normal flow if no dialog found
- **Detailed logging**: All actions logged for debugging

## 🧪 Testing Scenarios

### **Test Case 1: New User with Message Request**
```
1. Send message to completely new user
2. Navigate to their profile
3. Click Message button
4. Verify: Accept dialog appears
5. Verify: Auto-clicks Accept ✅
6. Verify: Primary/General dialog appears
7. Verify: Auto-clicks Primary ✅
8. Verify: Message sends successfully ✅
```

### **Test Case 2: User with Existing Thread**
```
1. Send message to user with saved thread ID
2. Navigate directly to thread
3. Verify: No message request dialog
4. Verify: handleMessageRequestIfNeeded() detects no dialog ✅
5. Verify: Message sends normally ✅
```

### **Test Case 3: User with No Message Request**
```
1. Send message to user who hasn't messaged before
2. Navigate to their profile
3. Click Message button
4. Verify: No message request dialog (Instagram allows direct messaging)
5. Verify: handleMessageRequestIfNeeded() detects no dialog ✅
6. Verify: Message sends normally ✅
```

## 📝 Logging Output

### **When Message Request Found:**
```
🔍 Checking for message request dialog for @username...
🔍 Method 1: Looking for Accept button by text...
✅ Found Accept button with text: "Accept"
🔘 Clicking Accept button...
🔍 Method 2: Looking for Primary/General option after Accept...
✅ Found Primary/General option with text: "Primary"
🔘 Clicking Primary/General option...
✅ Message request handled successfully for @username
✅ Message moved to Primary folder for @username
```

### **When No Message Request:**
```
🔍 Checking for message request dialog for @username...
🔍 Method 1: Looking for Accept button by text...
ℹ️ No message request dialog found for @username - proceeding normally
```

### **When Error Occurs:**
```
🔍 Checking for message request dialog for @username...
⚠️ Error handling message request for @username: [error details]
```

## ⚠️ Important Notes

### **Non-Blocking:**
- **Continues on error**: Message sending continues even if request handling fails
- **Optional feature**: Works when needed, invisible when not needed
- **Performance impact**: Minimal - only 2-3 seconds additional wait

### **Instagram Compatibility:**
- **UI changes**: Robust text-based detection works across UI updates
- **Multiple languages**: Supports English and Vietnamese interfaces
- **Fallback safe**: If detection fails, normal flow continues

### **User Experience:**
- **Seamless**: User doesn't need to manually handle requests
- **Automatic**: Works in background without intervention
- **Reliable**: Handles edge cases gracefully

## 🔮 Future Enhancements

### **1. Enhanced Detection:**
- CSS selector-based detection as backup
- Screenshot-based detection for complex cases
- Machine learning for button recognition

### **2. Advanced Options:**
- Configurable choice between Primary/General
- Custom handling for different user types
- Batch message request handling

### **3. Analytics:**
- Track message request frequency
- Success rate monitoring
- Performance impact analysis

## 🎉 Result

**Auto-messaging now handles message requests automatically:**

1. **Detects message request dialogs** for new users
2. **Auto-clicks Accept** to approve the request
3. **Auto-selects Primary folder** (recommended)
4. **Continues normal message sending** seamlessly
5. **Works in background** without user intervention

**User experience:** "Khi vào đến direct sẽ bấm accept và chọn primary, sau đó tiếp tục như bình thường" ✅
