# 🖨️ Hướng dẫn cài đặt máy in HPRT TP80N

## 📋 Tổng quan

Hệ thống hỗ trợ 3 phương thức kết nối với máy in HPRT TP80N:
- **USB** - Kết nối trực tiếp qua cáp USB
- **Mạng (IP tĩnh)** - Kết nối qua Ethernet/WiFi với IP cố định
- **<PERSON><PERSON><PERSON> in hệ thống** - Sử dụng driver Windows

## 🔌 Phương thức 1: Kết nối USB

### Cài đặt:
1. **Kết nối máy in** qua cáp USB
2. **Cài driver** HPRT TP80N từ CD hoặc website
3. **Kiểm tra Device Manager** để xác nhận VID/PID:
   - Vendor ID: `0x0fe6` (HPRT)
   - Product ID: `0x811e` (TP80N)

### Trong ứng dụng:
1. Vào **Settings** → **Cài đặt máy in**
2. <PERSON>ọn **Loại kết nối: USB**
3. <PERSON><PERSON><PERSON> tra **Vendor ID** và **Product ID**
4. Click **Test kết nối** để kiểm tra
5. Click **In thử** để test in
6. **Lưu cài đặt**

### Ưu điểm:
- ✅ Kết nối ổn định
- ✅ Tốc độ in nhanh
- ✅ Không cần cấu hình mạng

### Nhược điểm:
- ❌ Cần cáp USB
- ❌ Khoảng cách giới hạn

## 🌐 Phương thức 2: Kết nối mạng (IP tĩnh)

### Cài đặt máy in:
1. **Kết nối máy in** vào mạng qua Ethernet hoặc WiFi
2. **Cài đặt IP tĩnh** trên máy in:
   - Vào menu máy in: `Settings` → `Network` → `IP Settings`
   - Chọn `Static IP`
   - Đặt IP: `*************` (hoặc IP khác trong dải mạng)
   - Subnet: `*************`
   - Gateway: `***********` (IP router)
3. **Kiểm tra kết nối**: Ping IP từ máy tính

### Trong ứng dụng:
1. Vào **Settings** → **Cài đặt máy in**
2. Chọn **Loại kết nối: Mạng (IP tĩnh)**
3. Nhập **Địa chỉ IP** máy in (VD: `*************`)
4. **Cổng**: `9100` (mặc định cho máy in nhiệt)
5. **Timeout**: `5000ms`
6. Click **Test kết nối** để kiểm tra
7. Click **In thử** để test in
8. **Lưu cài đặt**

### Ưu điểm:
- ✅ Kết nối không dây
- ✅ Khoảng cách xa
- ✅ Nhiều máy tính có thể in

### Nhược điểm:
- ❌ Cần cấu hình mạng
- ❌ Phụ thuộc vào WiFi/Ethernet

## 🖥️ Phương thức 3: Máy in hệ thống

### Cài đặt:
1. **Cài driver** HPRT TP80N từ Windows
2. **Add Printer** trong Windows Settings
3. **Set as default** nếu cần

### Trong ứng dụng:
1. Vào **Settings** → **Cài đặt máy in**
2. Chọn **Loại kết nối: Máy in hệ thống**
3. Click **Quét máy in** để tìm máy in
4. **Chọn máy in** từ dropdown
5. Click **In thử** để test in
6. **Lưu cài đặt**

### Ưu điểm:
- ✅ Dễ cài đặt
- ✅ Tương thích tốt với Windows
- ✅ Hỗ trợ nhiều tính năng

### Nhược điểm:
- ❌ Chậm hơn USB/Network
- ❌ Phụ thuộc vào Windows driver

## ⚙️ Cài đặt định dạng in

### Thông số giấy:
- **Độ rộng**: 80mm
- **Ký tự mỗi dòng**: 48 characters
- **Encoding**: UTF-8 (hỗ trợ tiếng Việt)

### Định dạng bình luận:
- ✅ **Header**: Tiêu đề shop
- ✅ **Timestamp**: Thời gian
- ✅ **Username**: Tên khách hàng
- ✅ **Content**: Nội dung bình luận
- ✅ **Footer**: Thông tin liên hệ

### Định dạng hóa đơn:
- ✅ **Tất cả như bình luận**
- ✅ **Price**: Giá sản phẩm
- ✅ **Total**: Tổng tiền
- ⚪ **QR Code**: Tùy chọn

### Mẫu in tùy chỉnh:
```
================================
    INSTAGRAM LIVE ORDER
================================

Thời gian: 06/06/2024 18:10:42
--------------------------------
Khách hàng: @username
Nội dung:
Comment content here...

Giá: 50,000đ
Tổng: 50,000đ
--------------------------------
Cảm ơn quý khách!
Instagram: @your_shop_name
================================
```

## 🔧 Khắc phục sự cố

### Lỗi USB:
- **Device not found**: Kiểm tra driver, cáp USB
- **Access denied**: Chạy ứng dụng với quyền Admin
- **Transfer error**: Thử lại hoặc restart máy in

### Lỗi mạng:
- **Connection timeout**: Kiểm tra IP, ping máy in
- **Connection refused**: Kiểm tra port 9100, firewall
- **Network unreachable**: Kiểm tra cùng subnet

### Lỗi hệ thống:
- **Printer not found**: Cài đặt lại driver
- **Print spooler error**: Restart Print Spooler service
- **Access denied**: Kiểm tra quyền in

## 📞 Hỗ trợ

### Thông tin máy in HPRT TP80N:
- **Model**: TP80N
- **Paper**: 80mm thermal paper
- **Interface**: USB, Ethernet, WiFi
- **Commands**: ESC/POS compatible
- **Speed**: 250mm/s

### Kiểm tra kết nối:
1. **USB**: Device Manager → Ports/USB
2. **Network**: Ping IP address
3. **System**: Control Panel → Printers

### Test commands:
- **USB test**: Kiểm tra VID/PID trong Device Manager
- **Network test**: `ping *************`
- **System test**: Print test page từ Windows

## 🎯 Khuyến nghị

### Cho shop nhỏ:
- **USB** - Đơn giản, ổn định

### Cho shop lớn:
- **Network** - Linh hoạt, nhiều máy tính

### Cho người mới:
- **System** - Dễ cài đặt

**Lưu ý**: Luôn test kết nối và in thử trước khi sử dụng thực tế!
