@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for the Instagram Live Comment System */

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  margin: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Hide scrollbar on mobile */
@media (max-width: 768px) {
  ::-webkit-scrollbar {
    display: none;
  }

  * {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

/* Custom components */
.card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-4;
}

.btn-primary {
  @apply bg-sky-500 hover:bg-sky-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2;
}

.btn-secondary {
  @apply bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

.btn-warning {
  @apply bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2;
}

/* Button size variants */
.btn-sm {
  @apply py-1.5 px-3 text-sm;
}

.btn-xs {
  @apply py-1 px-2 text-xs;
}

.input-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200;
}

.textarea-field {
  @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 resize-none;
}

/* Input and form element aliases */
.input {
  @apply input-field;
}

.checkbox {
  @apply h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded;
}

/* Loading spinner */
.spinner {
  border: 3px solid #f3f3f3;
  border-top: 3px solid #8b5cf6;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* Comment card styles */
.comment-card {
  @apply bg-white rounded-lg border border-gray-200 p-4 mb-3 shadow-sm hover:shadow-md transition-shadow duration-200;
}

.comment-card.new {
  @apply border-sky-300 bg-sky-50;
  animation: pulse 2s ease-in-out;
}

.comment-card.processed {
  @apply border-green-300 bg-green-50;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.8;
  }
}

/* Mobile optimizations */
@media (max-width: 640px) {
  .card {
    @apply p-3;
  }

  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger,
  .btn-warning {
    @apply py-3 px-4 text-sm;
  }

  .btn-sm {
    @apply py-2 px-3 text-sm;
  }

  .btn-xs {
    @apply py-1.5 px-2 text-xs;
  }

  .comment-card {
    @apply p-3 mb-2;
  }

  /* Better spacing for mobile forms */
  .input-field,
  .textarea-field,
  .input {
    @apply text-base; /* Prevent zoom on iOS */
  }
}

/* Extra small mobile optimizations */
@media (max-width: 420px) {
  .card {
    @apply p-2;
  }

  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger,
  .btn-warning {
    @apply py-2.5 px-3 text-sm;
  }

  .btn-sm {
    @apply py-2 px-2.5 text-xs;
  }

  .btn-xs {
    @apply py-1 px-2 text-xs;
  }

  .comment-card {
    @apply p-2 mb-2;
  }

  /* Ensure header stats are always visible */
  .lg\:hidden {
    font-size: 10px !important;
  }

  .lg\:hidden .font-medium {
    font-weight: 600;
  }
}

/* Touch-friendly buttons */
@media (hover: none) and (pointer: coarse) {
  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger,
  .btn-warning {
    @apply min-h-[44px] min-w-[44px];
  }

  .btn-sm {
    @apply min-h-[40px] min-w-[40px];
  }

  .btn-xs {
    @apply min-h-[36px] min-w-[36px];
  }
}

/* Toast container styles */
.toast-container {
  max-height: 200px;
  overflow: hidden;
}

.toast-container>div:nth-child(n+4) {
  display: none !important;
}

.toast-container>div {
  transition: all 0.3s ease-in-out;
  margin-bottom: 8px;
}

@media (max-width: 640px) {
  .toast-container {
    max-height: 150px;
  }

  .toast-container>div:nth-child(n+3) {
    display: none !important;
  }
}

/* Safe area for notched devices */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
