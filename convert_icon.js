/**
 * <PERSON><PERSON><PERSON> để convert PNG thành ICO file đúng định dạng
 */

const fs = require('fs');
const path = require('path');
const sharp = require('sharp');

async function convertPngToIco() {
  console.log('🔄 Converting PNG to ICO format...\n');
  
  const pngPath = path.join(__dirname, 'src', 'assets', 'icon.png');
  const icoPath = path.join(__dirname, 'src', 'assets', 'icon.ico');
  const backupPath = path.join(__dirname, 'src', 'assets', 'icon.ico.backup');
  
  try {
    // Check if PNG exists
    if (!fs.existsSync(pngPath)) {
      console.log('❌ PNG file not found:', pngPath);
      return false;
    }
    
    // Backup existing ICO file
    if (fs.existsSync(icoPath)) {
      console.log('📦 Backing up existing ICO file...');
      fs.copyFileSync(icoPath, backupPath);
    }
    
    console.log('🔍 Reading PNG file...');
    const pngBuffer = fs.readFileSync(pngPath);
    
    // Get PNG info
    const metadata = await sharp(pngBuffer).metadata();
    console.log(`📊 PNG info: ${metadata.width}x${metadata.height}, ${metadata.format}`);
    
    // Create multiple sizes for ICO (Windows standard sizes)
    const sizes = [16, 24, 32, 48, 64, 128, 256];
    console.log('🎨 Creating ICO with multiple sizes:', sizes.join(', '));
    
    // For now, we'll use the PNG directly as ICO since Sharp doesn't support ICO output
    // This is a workaround - we'll create a proper ICO structure manually
    
    // Simple approach: Use PNG as ICO (many tools accept this)
    console.log('⚠️  Note: Creating PNG-based ICO (compatible with most tools)');
    
    // Create a 256x256 PNG for ICO
    const icoBuffer = await sharp(pngBuffer)
      .resize(256, 256, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .png()
      .toBuffer();
    
    // Write the ICO file
    fs.writeFileSync(icoPath, icoBuffer);
    
    console.log('✅ ICO file created successfully');
    console.log(`📁 Location: ${icoPath}`);
    console.log(`📊 Size: ${(icoBuffer.length / 1024).toFixed(2)} KB`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Error converting PNG to ICO:', error.message);
    
    // Restore backup if exists
    if (fs.existsSync(backupPath)) {
      console.log('🔄 Restoring backup...');
      fs.copyFileSync(backupPath, icoPath);
    }
    
    return false;
  }
}

async function createProperIcoFile() {
  console.log('🔧 Creating proper ICO file using alternative method...\n');
  
  const pngPath = path.join(__dirname, 'src', 'assets', 'icon.png');
  const icoPath = path.join(__dirname, 'src', 'assets', 'icon.ico');
  
  try {
    // Read PNG
    const pngBuffer = fs.readFileSync(pngPath);
    
    // Create 32x32 PNG for ICO (most compatible size)
    const iconBuffer = await sharp(pngBuffer)
      .resize(32, 32, {
        fit: 'contain',
        background: { r: 0, g: 0, b: 0, alpha: 0 }
      })
      .png()
      .toBuffer();
    
    // Create basic ICO header structure
    const icoHeader = Buffer.alloc(6);
    icoHeader.writeUInt16LE(0, 0);      // Reserved (must be 0)
    icoHeader.writeUInt16LE(1, 2);      // Type (1 = ICO)
    icoHeader.writeUInt16LE(1, 4);      // Number of images
    
    // Create ICO directory entry
    const icoEntry = Buffer.alloc(16);
    icoEntry.writeUInt8(32, 0);         // Width (32)
    icoEntry.writeUInt8(32, 1);         // Height (32)
    icoEntry.writeUInt8(0, 2);          // Color palette (0 = no palette)
    icoEntry.writeUInt8(0, 3);          // Reserved
    icoEntry.writeUInt16LE(1, 4);       // Color planes
    icoEntry.writeUInt16LE(32, 6);      // Bits per pixel
    icoEntry.writeUInt32LE(iconBuffer.length, 8);  // Image size
    icoEntry.writeUInt32LE(22, 12);     // Image offset (6 + 16 = 22)
    
    // Combine header, entry, and image data
    const icoBuffer = Buffer.concat([icoHeader, icoEntry, iconBuffer]);
    
    // Write ICO file
    fs.writeFileSync(icoPath, icoBuffer);
    
    console.log('✅ Proper ICO file created');
    console.log(`📁 Location: ${icoPath}`);
    console.log(`📊 Size: ${(icoBuffer.length / 1024).toFixed(2)} KB`);
    
    return true;
    
  } catch (error) {
    console.error('❌ Error creating proper ICO:', error.message);
    return false;
  }
}

async function main() {
  console.log('🎨 CommiLive Icon Converter');
  console.log('===========================\n');
  
  // Try to create proper ICO file
  const success = await createProperIcoFile();
  
  if (success) {
    console.log('\n🎉 Icon conversion completed!');
    console.log('\n💡 Now you can run:');
    console.log('   node check_icon.js     # Verify the new ICO file');
    console.log('   npm run package        # Package with the new icon');
  } else {
    console.log('\n❌ Icon conversion failed');
    console.log('\n🔧 Manual steps:');
    console.log('   1. Use an online PNG to ICO converter');
    console.log('   2. Or use a tool like ImageMagick:');
    console.log('      magick src/assets/icon.png src/assets/icon.ico');
    console.log('   3. Make sure the ICO file is properly formatted');
  }
}

// Run if called directly
if (require.main === module) {
  main()
    .then(() => process.exit(0))
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { convertPngToIco, createProperIcoFile };
