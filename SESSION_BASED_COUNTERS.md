# 📊 Session-Based Status Counters

## 🎯 Tổng quan

Trạng thái "đã in", "đã gửi", "chờ" trên thanh trạng thái là **session-based counters** - chỉ hiển thị thay đổi trong phiên hoạt động hiện tại, không phải persistent data.

## 🔄 Reset Behavior

### **<PERSON><PERSON> nà<PERSON> counters được reset:**
1. **Start Scraping**: Reset `totalComments` và `printedComments`
2. **Stop Scraping**: Reset `totalComments` và `printedComments`
3. **App Restart**: Reset tất cả counters về 0

### **Không reset khi:**
- **Messenger start/stop/restart**: Messenger có auto-restart mechanism, không phải user session
- **Browser restart**: Chỉ restart browser, không stop service
- **Network reconnection**: Technical issue, không phải user action
- **Temporary errors**: System recovery, không phải session end
- **Settings changes**: Configuration changes, session continues

## 🏗️ Implementation

### **System State Structure**
```javascript
let systemState = {
  isRunning: false,
  connectedClients: 0,
  totalComments: 0,      // Session-based: Reset khi start/stop scraping
  queuedMessages: 0,     // Session-based: Reset khi start/stop messaging
  sentMessages: 0,       // Session-based: Reset khi start/stop messaging
  printedComments: 0,    // Session-based: Reset khi start/stop scraping
  errors: 0,
  startTime: null
};
```

### **Reset Logic in API Endpoints**

#### **Start Scraping Reset**
```javascript
app.post('/api/start-scraping', async (req, res) => {
  // Reset session comments and counters when starting new scraping session
  sessionComments = [];
  isScrapingActive = true;
  
  // Reset session counters for new scraping session
  systemState.totalComments = 0;
  systemState.printedComments = 0;
  
  // ... start scraping logic
  
  // Emit updated system state with reset counters
  io.emit('system-state', systemState);
});
```

#### **Stop Scraping Reset**
```javascript
app.post('/api/stop-scraping', async (req, res) => {
  await instagramScraper.stop();
  systemState.isRunning = false;
  systemState.startTime = null;
  isScrapingActive = false;

  // Reset scraping-related counters when stopping
  systemState.totalComments = 0;
  systemState.printedComments = 0;

  // Emit updated system state
  io.emit('system-state', systemState);
});
```

#### **Messenger Operations (NO Reset)**
```javascript
// Messenger start/stop/restart KHÔNG reset counters
// vì có auto-restart mechanism - không phải user session control

app.post('/api/start-messenger', async (req, res) => {
  const result = await instagramMessenger.start(credentials);
  // NO counter reset - messenger có thể restart nhiều lần trong session
});

app.post('/api/stop-messenger', async (req, res) => {
  await instagramMessenger.stop();
  // NO counter reset - có thể là technical stop, không phải end session
});

// Auto-restart events cũng KHÔNG reset counters
instagramMessenger.on('browser-restarted', (data) => {
  // NO counter reset - đây là technical restart
});
```

## 📱 Frontend Display

### **Header Status Bar**
```jsx
{/* Quick stats */}
<div className="hidden lg:flex items-center space-x-4 text-sm text-gray-600">
  <div className="flex items-center space-x-1">
    <span className="font-medium">{systemState.totalComments}</span>
    <span>bình luận</span>
  </div>
  <div className="flex items-center space-x-1 text-blue-600">
    <span className="font-medium">{systemState.printedComments}</span>
    <span>đã in</span>
  </div>
  <div className="flex items-center space-x-1 text-green-600">
    <span className="font-medium">{systemState.sentMessages}</span>
    <span>đã gửi</span>
  </div>
  <div className="flex items-center space-x-1">
    <span className="font-medium">{systemState.queuedMessages}</span>
    <span>chờ gửi</span>
  </div>
</div>
```

### **Sidebar Stats**
```jsx
<div className="space-y-1 text-xs text-gray-600">
  <div className="flex justify-between">
    <span>Bình luận:</span>
    <span className="font-medium">{systemState.totalComments}</span>
  </div>
  <div className="flex justify-between">
    <span className="text-blue-600">Đã in:</span>
    <span className="font-medium text-blue-600">{systemState.printedComments}</span>
  </div>
  <div className="flex justify-between">
    <span className="text-green-600">Đã gửi:</span>
    <span className="font-medium text-green-600">{systemState.sentMessages}</span>
  </div>
  <div className="flex justify-between">
    <span>Chờ gửi:</span>
    <span className="font-medium">{systemState.queuedMessages}</span>
  </div>
</div>
```

## 🔄 Counter Update Flow

### **Comment Processing Flow**
1. **New comment scraped** → `totalComments++`
2. **Comment printed** → `printedComments++`
3. **Message queued** → `queuedMessages++`
4. **Message sent** → `sentMessages++`, `queuedMessages--`

### **Session Reset Flow**
1. **User starts scraping** → Reset scraping counters → Emit state
2. **Comments start coming** → Increment counters → Emit state
3. **User stops scraping** → Reset scraping counters → Emit state
4. **User starts messaging** → Reset messaging counters → Emit state
5. **Messages start sending** → Increment/decrement counters → Emit state
6. **User stops messaging** → Reset messaging counters → Emit state

## 📊 Benefits

### **User Experience**
- **Clear session tracking**: User biết chính xác activity trong session hiện tại
- **Fresh start**: Mỗi session bắt đầu với counters = 0
- **No confusion**: Không bị confuse bởi numbers từ sessions trước
- **Real-time feedback**: Thấy ngay impact của actions trong session

### **Operational Benefits**
- **Session monitoring**: Dễ dàng monitor performance của từng session
- **Troubleshooting**: Dễ debug issues trong specific session
- **Performance tracking**: Track efficiency của từng work session
- **Clean metrics**: Metrics không bị pollute bởi historical data

## ⚠️ Important Notes

### **Data Persistence**
- **Counters**: Session-based, không persist
- **Actual data**: Comments, messages, history vẫn được lưu trong database
- **Statistics**: Historical stats vẫn available trong database

### **Different Types of Restarts**
- **Scraper restart**: Counters reset (user-controlled session)
- **Messenger restart**: Counters KHÔNG reset (auto-restart mechanism)
- **Browser restart**: Counters KHÔNG reset (technical restart)
- **App restart**: Counters reset (new application session)

### **Multi-device Behavior**
- **Counters**: Sync across all connected clients
- **Reset**: Khi một client reset, tất cả clients thấy reset
- **Real-time**: Updates được broadcast qua Socket.IO

## 🔮 Future Enhancements

### **Session Analytics**
1. **Session duration tracking**: Track thời gian của mỗi session
2. **Session performance metrics**: Comments/minute, messages/minute
3. **Session comparison**: So sánh performance giữa các sessions
4. **Session history**: Lưu summary của previous sessions

### **Advanced Reset Options**
1. **Manual reset**: Button để manual reset counters
2. **Selective reset**: Reset specific counters thay vì all
3. **Reset confirmation**: Confirm trước khi reset
4. **Reset logging**: Log reset events for audit

### **Enhanced Display**
1. **Session timer**: Hiển thị thời gian session đã chạy
2. **Rate indicators**: Comments/minute, messages/minute
3. **Progress bars**: Visual progress indicators
4. **Session goals**: Set và track session targets
