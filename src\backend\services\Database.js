const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;
const winston = require('winston');

class Database {
  constructor() {
    this.db = null;
    this.dbPath = path.join(__dirname, '../data/instagram_live.db');
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.simple(),
      transports: [new winston.transports.Console()]
    });
  }

  async initialize() {
    try {
      this.logger.info('Initializing database...');

      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      await fs.mkdir(dataDir, { recursive: true });

      // Open database connection with UTF-8 support
      this.db = new sqlite3.Database(this.dbPath);

      // Set UTF-8 encoding for proper Vietnamese character support
      await this.runQuery("PRAGMA encoding = 'UTF-8'");

      // Create tables
      await this.createTables();

      // Run migrations
      await this.runMigrations();

      // Clean up priority system (one-time migration)
      await this.migrateToPureFIFO();

      // Create indexes (after migrations to ensure columns exist)
      await this.createIndexes();

      this.logger.info('Database initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize database:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // REMOVED: Comments, Messages, Orders tables - not needed for optimized system

      // Send_once tracking table - lightweight table to track send_once templates
      `CREATE TABLE IF NOT EXISTS send_once_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        template_name TEXT NOT NULL,
        customer_type TEXT NOT NULL,
        template_type TEXT DEFAULT 'normal',
        sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(username, template_name, customer_type, template_type)
      )`,

      // Templates table
      `CREATE TABLE IF NOT EXISTS message_templates (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        template TEXT NOT NULL,
        variables TEXT,
        description TEXT,
        customer_type TEXT DEFAULT 'regular',
        template_type TEXT DEFAULT 'normal',
        is_active BOOLEAN DEFAULT TRUE,
        send_once BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(name, customer_type, template_type)
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        type TEXT DEFAULT 'string',
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Statistics table - simplified without comments/messages/orders
      `CREATE TABLE IF NOT EXISTS statistics (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        printed_count INTEGER DEFAULT 0,
        messages_queued INTEGER DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // REMOVED: auto_messages table - not used, replaced by message_templates

      // Failed Messages table for verification failures
      `CREATE TABLE IF NOT EXISTS failed_messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        original_comment TEXT NOT NULL,
        customer_type TEXT,
        status TEXT DEFAULT 'verification_failed',
        error_message TEXT,
        failed_at DATETIME,
        retry_count INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        comment_timestamp DATETIME,
        search_attempted TEXT,
        verification_failed BOOLEAN DEFAULT TRUE,
        resolved BOOLEAN DEFAULT FALSE,
        resolved_at DATETIME,
        resolved_by TEXT,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Print Settings table (unified printer + format settings)
      `CREATE TABLE IF NOT EXISTS print_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        settings TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Auto Message Settings table
      `CREATE TABLE IF NOT EXISTS auto_message_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        settings TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Auto Message Stats table
      `CREATE TABLE IF NOT EXISTS auto_message_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        total_sent INTEGER DEFAULT 0,
        total_success INTEGER DEFAULT 0,
        total_failed INTEGER DEFAULT 0,
        last_sent DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Message Queue table - persistent queue for auto messages (FIFO order)
      `CREATE TABLE IF NOT EXISTS message_queue (
        id TEXT PRIMARY KEY,
        comment_id TEXT,
        username TEXT NOT NULL,
        original_comment TEXT NOT NULL,
        customer_type TEXT NOT NULL,
        template_name TEXT,
        template_type TEXT DEFAULT 'normal',
        status TEXT DEFAULT 'pending',
        retries INTEGER DEFAULT 0,
        max_retries INTEGER DEFAULT 3,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        scheduled_at DATETIME,
        processed_at DATETIME
      )`,

      // Cookies table for saving login session
      `CREATE TABLE IF NOT EXISTS saved_cookies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL,
        cookies TEXT NOT NULL,
        account_type TEXT DEFAULT 'scraper',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(username, account_type)
      )`,

      // Regular customers table with sync support
      `CREATE TABLE IF NOT EXISTS regular_customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        username TEXT NOT NULL UNIQUE,
        marked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        notes TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME DEFAULT NULL,
        device_id TEXT DEFAULT NULL,
        is_deleted INTEGER DEFAULT 0
      )`
    ];

    for (const tableSQL of tables) {
      await this.runQuery(tableSQL);
    }

    // Only insert default templates if database is completely new (no templates exist)
    await this.insertDefaultTemplatesIfEmpty();
  }

  async runMigrations() {
    try {
      this.logger.info('Running database migrations...');

      // Check if account_type column exists in saved_cookies table
      const tableInfo = await this.allQuery("PRAGMA table_info(saved_cookies)");
      const hasAccountType = tableInfo.some(column => column.name === 'account_type');

      // Check if customer_type column exists in message_templates table
      const templateTableInfo = await this.allQuery("PRAGMA table_info(message_templates)");
      const hasCustomerType = templateTableInfo.some(column => column.name === 'customer_type');

      if (!hasAccountType) {
        this.logger.info('Adding account_type column to saved_cookies table...');
        await this.runQuery('ALTER TABLE saved_cookies ADD COLUMN account_type TEXT DEFAULT "scraper"');

        // Create new unique index (skip dropping old one as it may be auto-generated)
        try {
          await this.runQuery('CREATE UNIQUE INDEX IF NOT EXISTS idx_saved_cookies_username_type ON saved_cookies(username, account_type)');
        } catch (indexError) {
          this.logger.warn('Could not create unique index, continuing...', indexError);
        }

        this.logger.info('Migration completed: account_type column added');
      }

      // REMOVED: Migration for comments table - table no longer exists

      // Check if regular_customers table exists
      const tableExists = await this.allQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='regular_customers'"
      );
      const hasRegularCustomersTable = tableExists.length > 0;

      if (!hasRegularCustomersTable) {
        this.logger.info('Creating regular_customers table...');
        await this.runQuery(`
          CREATE TABLE regular_customers (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            marked_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            notes TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Create indexes for new table
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_regular_customers_username ON regular_customers(username)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_regular_customers_marked_at ON regular_customers(marked_at)');

        this.logger.info('Migration completed: regular_customers table created');
      }

      if (!hasCustomerType) {
        this.logger.info('Adding customer_type column to message_templates table...');
        await this.runQuery('ALTER TABLE message_templates ADD COLUMN customer_type TEXT DEFAULT "regular"');

        // Update existing templates to have both regular and vip versions
        await this.migrateExistingTemplates();

        this.logger.info('Migration completed: customer_type column added and templates migrated');
      }

      // Check if send_once column exists in message_templates
      const hasSendOnce = await this.allQuery(`
        PRAGMA table_info(message_templates)
      `).then(columns => columns.some(col => col.name === 'send_once'));

      if (!hasSendOnce) {
        this.logger.info('Adding send_once column to message_templates table...');
        await this.runQuery('ALTER TABLE message_templates ADD COLUMN send_once BOOLEAN DEFAULT FALSE');

        this.logger.info('Migration completed: send_once column added');
      }

      // Check if printed_history table exists
      const printedHistoryTableExists = await this.allQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='printed_history'"
      );
      const hasPrintedHistoryTable = printedHistoryTableExists.length > 0;

      if (!hasPrintedHistoryTable) {
        this.logger.info('Creating printed_history table...');
        await this.runQuery(`
          CREATE TABLE printed_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            comment_id TEXT NOT NULL,
            username TEXT NOT NULL,
            comment_text TEXT NOT NULL,
            print_type TEXT DEFAULT 'comment',
            printed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            synced_at DATETIME DEFAULT NULL,
            device_id TEXT DEFAULT NULL,
            is_deleted INTEGER DEFAULT 0
          )
        `);

        // Create indexes for new table
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_printed_history_comment_id ON printed_history(comment_id)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_printed_history_username ON printed_history(username)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_printed_history_printed_at ON printed_history(printed_at)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_printed_history_sync ON printed_history(synced_at)');

        this.logger.info('Migration completed: printed_history table created');
      }

      // Add sync columns to existing printed_history table if they don't exist
      const printedHistoryTableInfo = await this.allQuery("PRAGMA table_info(printed_history)");
      const hasSyncedAt = printedHistoryTableInfo.some(col => col.name === 'synced_at');
      const hasDeviceId = printedHistoryTableInfo.some(col => col.name === 'device_id');
      const hasIsDeleted = printedHistoryTableInfo.some(col => col.name === 'is_deleted');

      if (!hasSyncedAt) {
        this.logger.info('Adding synced_at column to printed_history table...');
        await this.runQuery('ALTER TABLE printed_history ADD COLUMN synced_at DATETIME DEFAULT NULL');
      }

      if (!hasDeviceId) {
        this.logger.info('Adding device_id column to printed_history table...');
        await this.runQuery('ALTER TABLE printed_history ADD COLUMN device_id TEXT DEFAULT NULL');
      }

      if (!hasIsDeleted) {
        this.logger.info('Adding is_deleted column to printed_history table...');
        await this.runQuery('ALTER TABLE printed_history ADD COLUMN is_deleted INTEGER DEFAULT 0');
      }

      if (!hasSyncedAt || !hasDeviceId || !hasIsDeleted) {
        // Create sync index if new columns were added
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_printed_history_sync ON printed_history(synced_at)');
        this.logger.info('Migration completed: printed_history sync columns added');
      }

      // Add sync columns to send_once_history table if they don't exist
      const sendOnceHistoryTableInfo = await this.allQuery("PRAGMA table_info(send_once_history)");
      const sendOnceHasSyncedAt = sendOnceHistoryTableInfo.some(col => col.name === 'synced_at');
      const sendOnceHasDeviceId = sendOnceHistoryTableInfo.some(col => col.name === 'device_id');
      const sendOnceHasIsDeleted = sendOnceHistoryTableInfo.some(col => col.name === 'is_deleted');
      const sendOnceHasUpdatedAt = sendOnceHistoryTableInfo.some(col => col.name === 'updated_at');

      if (!sendOnceHasSyncedAt) {
        this.logger.info('Adding synced_at column to send_once_history table...');
        await this.runQuery('ALTER TABLE send_once_history ADD COLUMN synced_at DATETIME DEFAULT NULL');
      }

      if (!sendOnceHasDeviceId) {
        this.logger.info('Adding device_id column to send_once_history table...');
        await this.runQuery('ALTER TABLE send_once_history ADD COLUMN device_id TEXT DEFAULT NULL');
      }

      if (!sendOnceHasIsDeleted) {
        this.logger.info('Adding is_deleted column to send_once_history table...');
        await this.runQuery('ALTER TABLE send_once_history ADD COLUMN is_deleted INTEGER DEFAULT 0');
      }

      if (!sendOnceHasUpdatedAt) {
        this.logger.info('Adding updated_at column to send_once_history table...');
        // SQLite doesn't allow non-constant defaults in ALTER TABLE, so use NULL default
        await this.runQuery('ALTER TABLE send_once_history ADD COLUMN updated_at DATETIME DEFAULT NULL');

        // Update existing records to have current timestamp
        this.logger.info('Updating existing send_once_history records with current timestamp...');
        await this.runQuery('UPDATE send_once_history SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL');
      }

      if (!sendOnceHasSyncedAt || !sendOnceHasDeviceId || !sendOnceHasIsDeleted || !sendOnceHasUpdatedAt) {
        // Create sync indexes if new columns were added
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_send_once_history_sync ON send_once_history(synced_at)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_send_once_history_device ON send_once_history(device_id)');
        this.logger.info('Migration completed: send_once_history sync columns added');
      }

      // REMOVED: Migration for comments table - table no longer exists

      // Migration: Add sync fields to regular_customers table
      const regularCustomersTableInfo = await this.allQuery("PRAGMA table_info(regular_customers)");
      const customerHasSyncedAt = regularCustomersTableInfo.some(col => col.name === 'synced_at');
      const customerHasDeviceId = regularCustomersTableInfo.some(col => col.name === 'device_id');
      const customerHasIsDeleted = regularCustomersTableInfo.some(col => col.name === 'is_deleted');

      if (!customerHasSyncedAt) {
        this.logger.info('Adding synced_at column to regular_customers table...');
        await this.runQuery('ALTER TABLE regular_customers ADD COLUMN synced_at DATETIME DEFAULT NULL');
      }

      if (!customerHasDeviceId) {
        this.logger.info('Adding device_id column to regular_customers table...');
        await this.runQuery('ALTER TABLE regular_customers ADD COLUMN device_id TEXT DEFAULT NULL');
      }

      if (!customerHasIsDeleted) {
        this.logger.info('Adding is_deleted column to regular_customers table...');
        await this.runQuery('ALTER TABLE regular_customers ADD COLUMN is_deleted INTEGER DEFAULT 0');
      }

      if (!customerHasSyncedAt || !customerHasDeviceId || !customerHasIsDeleted) {
        this.logger.info('Migration completed: sync fields added to regular_customers table');
      }

      // Migration: Add sync fields to instagram_threads table
      const threadsTableInfo = await this.allQuery("PRAGMA table_info(instagram_threads)");
      const hasThreadsSyncedAt = threadsTableInfo.some(col => col.name === 'synced_at');
      const hasThreadsDeviceId = threadsTableInfo.some(col => col.name === 'device_id');
      const hasThreadsIsDeleted = threadsTableInfo.some(col => col.name === 'is_deleted');

      if (!hasThreadsSyncedAt) {
        this.logger.info('Adding synced_at column to instagram_threads table...');
        await this.runQuery('ALTER TABLE instagram_threads ADD COLUMN synced_at DATETIME DEFAULT NULL');
      }

      if (!hasThreadsDeviceId) {
        this.logger.info('Adding device_id column to instagram_threads table...');
        await this.runQuery('ALTER TABLE instagram_threads ADD COLUMN device_id TEXT DEFAULT NULL');
      }

      if (!hasThreadsIsDeleted) {
        this.logger.info('Adding is_deleted column to instagram_threads table...');
        await this.runQuery('ALTER TABLE instagram_threads ADD COLUMN is_deleted INTEGER DEFAULT 0');
      }

      if (!hasThreadsSyncedAt || !hasThreadsDeviceId || !hasThreadsIsDeleted) {
        this.logger.info('Migration completed: sync fields added to instagram_threads table');
      }

      // Migration: Add template_type field to message_templates table
      const templatesTableInfo = await this.allQuery("PRAGMA table_info(message_templates)");
      const hasTemplateType = templatesTableInfo.some(col => col.name === 'template_type');

      if (!hasTemplateType) {
        this.logger.info('Adding template_type column to message_templates table...');
        await this.runQuery('ALTER TABLE message_templates ADD COLUMN template_type TEXT DEFAULT "normal"');

        // Update existing backup templates to have backup type
        await this.runQuery('UPDATE message_templates SET template_type = "backup" WHERE name = "backup_notification"');

        this.logger.info('Migration completed: template_type column added to message_templates table');
      }

      // Migration: Add template_type field to message_queue table
      const queueTableInfo = await this.allQuery("PRAGMA table_info(message_queue)");
      const hasQueueTemplateType = queueTableInfo.some(col => col.name === 'template_type');

      if (!hasQueueTemplateType) {
        this.logger.info('Adding template_type column to message_queue table...');
        await this.runQuery('ALTER TABLE message_queue ADD COLUMN template_type TEXT DEFAULT "normal"');
        this.logger.info('Migration completed: template_type column added to message_queue table');
      }

      // Migration: Create instagram_threads table for caching direct message thread IDs
      const threadsTableExists = await this.allQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='instagram_threads'"
      );
      const hasThreadsTable = threadsTableExists.length > 0;

      if (!hasThreadsTable) {
        this.logger.info('Creating instagram_threads table...');
        await this.runQuery(`
          CREATE TABLE instagram_threads (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT NOT NULL UNIQUE,
            thread_id TEXT NOT NULL,
            last_used DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Create indexes for new table
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_instagram_threads_username ON instagram_threads(username)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_instagram_threads_thread_id ON instagram_threads(thread_id)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_instagram_threads_last_used ON instagram_threads(last_used)');

        this.logger.info('Migration completed: instagram_threads table created');
      }

      // Migration: Create price_mappings table for template price conversion
      const priceMappingsTableExists = await this.allQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='price_mappings'"
      );
      const hasPriceMappingsTable = priceMappingsTableExists.length > 0;

      if (!hasPriceMappingsTable) {
        this.logger.info('Creating price_mappings table...');
        await this.runQuery(`
          CREATE TABLE price_mappings (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            prefix TEXT NOT NULL UNIQUE,
            price TEXT NOT NULL,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Create indexes for new table
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_price_mappings_prefix ON price_mappings(prefix)');
        await this.runQuery('CREATE INDEX IF NOT EXISTS idx_price_mappings_active ON price_mappings(is_active)');

        // Insert default price mappings
        await this.insertDefaultPriceMappings();

        this.logger.info('Migration completed: price_mappings table created');
      }

      // Migration: Remove old unused tables (comments, messages, orders)
      await this.removeUnusedTables();

      this.logger.info('Database migrations completed');
    } catch (error) {
      this.logger.error('Failed to run migrations:', error);
      // Don't throw error to prevent app from crashing
    }
  }

  async removeUnusedTables() {
    try {
      this.logger.info('🗑️ Checking for unused tables to remove...');

      // List of tables to remove
      const tablesToRemove = ['comments', 'messages', 'orders'];
      let removedCount = 0;

      for (const tableName of tablesToRemove) {
        // Check if table exists
        const tableExists = await this.allQuery(
          "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
          [tableName]
        );

        if (tableExists.length > 0) {
          this.logger.info(`🗑️ Removing unused table: ${tableName}`);
          await this.runQuery(`DROP TABLE ${tableName}`);
          removedCount++;
        }
      }

      if (removedCount > 0) {
        this.logger.info(`✅ Removed ${removedCount} unused tables`);
      } else {
        this.logger.info('✅ No unused tables found');
      }

    } catch (error) {
      this.logger.error('Failed to remove unused tables:', error);
      // Don't throw error - this is not critical for app functionality
    }
  }

  async migrateToPureFIFO() {
    try {
      this.logger.info('🔄 Migrating to pure FIFO queue system...');

      // Check if priority column exists in message_queue table
      const tableInfo = await this.allQuery("PRAGMA table_info(message_queue)");
      const hasPriorityColumn = tableInfo.some(col => col.name === 'priority');

      if (hasPriorityColumn) {
        this.logger.info('📝 Removing priority column from message_queue table...');

        // SQLite doesn't support DROP COLUMN, so we need to recreate the table
        await this.runQuery(`
          CREATE TABLE message_queue_new (
            id TEXT PRIMARY KEY,
            comment_id TEXT,
            username TEXT NOT NULL,
            original_comment TEXT NOT NULL,
            customer_type TEXT NOT NULL,
            template_name TEXT,
            template_type TEXT DEFAULT 'normal',
            status TEXT DEFAULT 'pending',
            retries INTEGER DEFAULT 0,
            max_retries INTEGER DEFAULT 3,
            error_message TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            scheduled_at DATETIME,
            processed_at DATETIME
          )
        `);

        // Copy data without priority column
        await this.runQuery(`
          INSERT INTO message_queue_new
          (id, comment_id, username, original_comment, customer_type, template_name, template_type,
           status, retries, max_retries, error_message, created_at, updated_at, scheduled_at, processed_at)
          SELECT id, comment_id, username, original_comment, customer_type, template_name, template_type,
                 status, retries, max_retries, error_message, created_at, updated_at, scheduled_at, processed_at
          FROM message_queue
        `);

        // Drop old table and rename new one
        await this.runQuery('DROP TABLE message_queue');
        await this.runQuery('ALTER TABLE message_queue_new RENAME TO message_queue');

        this.logger.info('✅ Priority column removed - queue now uses pure FIFO order');
      } else {
        this.logger.info('✅ Priority column already removed - queue is using FIFO order');
      }

    } catch (error) {
      this.logger.error('Failed to migrate to pure FIFO:', error);
      // Don't throw error - this is not critical for app functionality
    }
  }

  async createIndexes() {
    try {
      this.logger.info('Creating database indexes...');

      const indexes = [
        // REMOVED: Indexes for comments, messages, orders tables - tables no longer exist
        'CREATE INDEX IF NOT EXISTS idx_statistics_date ON statistics(date)',
        'CREATE INDEX IF NOT EXISTS idx_send_once_history_username ON send_once_history(username)',
        'CREATE INDEX IF NOT EXISTS idx_send_once_history_template ON send_once_history(template_name)',
        'CREATE INDEX IF NOT EXISTS idx_send_once_history_lookup ON send_once_history(username, template_name, customer_type, template_type)'
      ];

      // Only create regular_customers indexes if table exists
      const tableExists = await this.allQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='regular_customers'"
      );
      const hasRegularCustomersTable = tableExists.length > 0;
      if (hasRegularCustomersTable) {
        indexes.push('CREATE INDEX IF NOT EXISTS idx_regular_customers_username ON regular_customers(username)');
        indexes.push('CREATE INDEX IF NOT EXISTS idx_regular_customers_marked_at ON regular_customers(marked_at)');
      }

      for (const indexSQL of indexes) {
        await this.runQuery(indexSQL);
      }

      this.logger.info('Database indexes created successfully');
    } catch (error) {
      this.logger.error('Failed to create indexes:', error);
      // Don't throw error to prevent app from crashing
    }
  }

  async migrateExistingTemplates() {
    try {
      // Get all existing templates (they should have customer_type = 'regular' by default)
      const existingTemplates = await this.allQuery('SELECT * FROM message_templates');

      // Create VIP versions of existing templates
      for (const template of existingTemplates) {
        // Skip if this is already a VIP template
        if (template.customer_type === 'vip') {
          continue;
        }

        // Check if VIP version already exists
        const vipExists = await this.getQuery(
          'SELECT id FROM message_templates WHERE name = ? AND customer_type = "vip"',
          [template.name]
        );

        if (!vipExists) {
          // Create VIP version with modified template
          let vipTemplate = template.template;

          // Modify template for VIP customers (remove banking info)
          if (template.name === 'print_notification') {
            vipTemplate = 'Chào {{username}}! Cảm ơn bạn đã quan tâm: "{{content}}". Chúng tôi sẽ liên hệ với bạn sớm nhất để xác nhận đơn hàng!';
          } else if (template.name === 'order_confirmation') {
            vipTemplate = 'Chúc mừng {{username}} đã chốt đơn {{content}}. Chúng tôi sẽ chuẩn bị hàng và liên hệ với bạn sớm nhất. Xin cảm ơn!';
          } else if (template.name === 'thank_you') {
            vipTemplate = 'Cảm ơn {{username}} đã quan tâm "{{content}}". Chúng tôi sẽ liên hệ với bạn sớm nhất để tư vấn chi tiết!';
          }

          await this.runQuery(
            'INSERT INTO message_templates (name, template, variables, description, customer_type) VALUES (?, ?, ?, ?, ?)',
            [template.name, vipTemplate, template.variables, template.description + ' (Khách cũ)', 'vip']
          );
        }
      }

      this.logger.info('Existing templates migrated successfully');
    } catch (error) {
      this.logger.error('Failed to migrate existing templates:', error);
    }
  }

  async insertDefaultTemplatesIfEmpty() {
    try {
      // Check if default templates have been initialized before
      const defaultTemplatesFlag = await this.getSetting('default_templates_initialized');

      if (defaultTemplatesFlag === 'true') {
        this.logger.info('Default templates were already initialized, skipping auto-creation');
        return;
      }

      // Check if any templates already exist
      const existingTemplates = await this.allQuery('SELECT COUNT(*) as count FROM message_templates');
      const templateCount = existingTemplates[0].count;

      if (templateCount > 0) {
        this.logger.info(`Database already has ${templateCount} templates, marking as initialized`);
        await this.saveSetting('default_templates_initialized', 'true');
        return;
      }

      this.logger.info('Database is empty, creating default templates...');
      await this.insertDefaultTemplates();

      // Mark as initialized so we don't auto-create again
      await this.saveSetting('default_templates_initialized', 'true');
    } catch (error) {
      this.logger.error('Failed to check/insert default templates:', error);
    }
  }

  async insertDefaultTemplates() {
    const defaultTemplates = [
      // Templates for new customers (regular) - with banking info
      {
        name: 'print_notification',
        template: 'Chào {{username}}! Cảm ơn bạn đã quan tâm: "{{content}}". Để đặt hàng, vui lòng chuyển khoản cọc 50% qua STK: ********** - Ngân hàng ABC - Chủ TK: Tên Shop. Sau khi chuyển khoản, vui lòng gửi bill để xác nhận!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Tin nhắn gửi khi in bình luận (Khách mới)',
        customer_type: 'regular',
        template_type: 'normal'
      },
      {
        name: 'order_confirmation',
        template: 'Chúc mừng {{username}} đã chốt đơn {{content}}. Vui lòng chuyển khoản cọc 50% qua STK: ********** - Ngân hàng ABC. Xin cảm ơn!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Chúc mừng chốt đơn (Khách mới)',
        customer_type: 'regular',
        template_type: 'normal'
      },
      {
        name: 'thank_you',
        template: 'Cảm ơn {{username}} đã quan tâm "{{content}}". Để đặt hàng, vui lòng chuyển khoản cọc qua STK: ********** - Ngân hàng ABC. Shop sẽ liên hệ bạn sớm nhất!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Cảm ơn quan tâm (Khách mới)',
        customer_type: 'regular',
        template_type: 'normal'
      },

      // Templates for VIP customers (vip) - without banking info
      {
        name: 'print_notification',
        template: 'Chào {{username}}! Cảm ơn bạn đã quan tâm: "{{content}}". Chúng tôi sẽ liên hệ với bạn sớm nhất để xác nhận đơn hàng!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Tin nhắn gửi khi in bình luận (Khách cũ)',
        customer_type: 'vip',
        template_type: 'normal'
      },
      {
        name: 'order_confirmation',
        template: 'Chúc mừng {{username}} đã chốt đơn {{content}}. Chúng tôi sẽ chuẩn bị hàng và liên hệ với bạn sớm nhất. Xin cảm ơn!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Chúc mừng chốt đơn (Khách cũ)',
        customer_type: 'vip',
        template_type: 'normal'
      },
      {
        name: 'thank_you',
        template: 'Cảm ơn {{username}} đã quan tâm "{{content}}". Chúng tôi sẽ liên hệ với bạn sớm nhất để tư vấn chi tiết!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Cảm ơn quan tâm (Khách cũ)',
        customer_type: 'vip',
        template_type: 'normal'
      },

      // Backup templates for new customers (regular)
      {
        name: 'print_notification',
        template: 'Hi {{username}}! Shop đã ghi nhận yêu cầu "{{content}}" của bạn vào danh sách dự bị. Nếu có hàng, shop sẽ liên hệ bạn ngay. Cảm ơn bạn đã quan tâm!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Tin nhắn dự bị cho khách mới',
        customer_type: 'regular',
        template_type: 'backup'
      },

      // Backup templates for VIP customers
      {
        name: 'print_notification',
        template: 'Chào {{username}}! Shop đã ghi nhận yêu cầu "{{content}}" của bạn vào danh sách dự bị. Sẽ ưu tiên liên hệ bạn khi có hàng. Cảm ơn bạn!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Tin nhắn dự bị cho khách cũ',
        customer_type: 'vip',
        template_type: 'backup'
      },

      // Common templates
      {
        name: 'inquiry_response',
        template: 'Hi {{username}}! Shop đã nhận được yêu cầu "{{content}}" của bạn. Vui lòng inbox để được tư vấn chi tiết nhé!',
        variables: JSON.stringify(['username', 'content']),
        description: 'Phản hồi câu hỏi',
        customer_type: 'regular',
        template_type: 'normal'
      },
      {
        name: 'welcome',
        template: 'Chào mừng {{username}} đến với shop! Cảm ơn bạn đã quan tâm đến sản phẩm của chúng tôi.',
        variables: JSON.stringify(['username']),
        description: 'Chào mừng khách hàng',
        customer_type: 'regular',
        template_type: 'normal'
      }
    ];

    let insertedCount = 0;
    for (const template of defaultTemplates) {
      try {
        const result = await this.runQuery(
          'INSERT OR IGNORE INTO message_templates (name, template, variables, description, customer_type, template_type) VALUES (?, ?, ?, ?, ?, ?)',
          [template.name, template.template, template.variables, template.description, template.customer_type, template.template_type || 'normal']
        );
        if (result.changes > 0) {
          insertedCount++;
        }
      } catch (error) {
        this.logger.warn(`Failed to insert default template ${template.name}:`, error);
      }
    }

    this.logger.info(`Default templates creation completed: ${insertedCount} new templates inserted`);
  }

  async insertDefaultPriceMappings() {
    const defaultMappings = [
      {
        prefix: 't',
        price: '79k',
        description: 'Mã chốt đơn bắt đầu bằng "t" (ví dụ: t91, t25, t13)'
      },
      {
        prefix: 'k',
        price: '99k',
        description: 'Mã chốt đơn bắt đầu bằng "k" (ví dụ: k80, k15, k14)'
      }
    ];

    let insertedCount = 0;
    for (const mapping of defaultMappings) {
      try {
        const result = await this.runQuery(
          'INSERT OR IGNORE INTO price_mappings (prefix, price, description) VALUES (?, ?, ?)',
          [mapping.prefix, mapping.price, mapping.description]
        );
        if (result.changes > 0) {
          insertedCount++;
        }
      } catch (error) {
        this.logger.warn(`Failed to insert default price mapping ${mapping.prefix}:`, error);
      }
    }

    this.logger.info(`Default price mappings creation completed: ${insertedCount} new mappings inserted`);
  }

  // Method to force recreate default templates (for admin use)
  async recreateDefaultTemplates() {
    try {
      this.logger.info('Force recreating default templates...');

      // Delete all existing templates
      await this.runQuery('DELETE FROM message_templates');

      // Insert default templates
      await this.insertDefaultTemplates();

      // Mark as initialized
      await this.saveSetting('default_templates_initialized', 'true');

      this.logger.info('Default templates recreated successfully');
      return { success: true, message: 'Default templates recreated successfully' };
    } catch (error) {
      this.logger.error('Failed to recreate default templates:', error);
      throw error;
    }
  }

  // Method to reset initialization flag (allows auto-creation again)
  async resetDefaultTemplatesFlag() {
    try {
      await this.saveSetting('default_templates_initialized', 'false');
      this.logger.info('Default templates initialization flag reset');
      return { success: true, message: 'Default templates flag reset' };
    } catch (error) {
      this.logger.error('Failed to reset default templates flag:', error);
      throw error;
    }
  }

  runQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function (error) {
        if (error) {
          reject(error);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  getQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (error, row) => {
        if (error) {
          reject(error);
        } else {
          resolve(row);
        }
      });
    });
  }

  allQuery(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (error, rows) => {
        if (error) {
          reject(error);
        } else {
          resolve(rows);
        }
      });
    });
  }

  // REMOVED: saveComment and getComment methods - comments table no longer exists

  // REMOVED: getComments, markCommentPrinted, saveMessage methods - tables no longer exist

  // REMOVED: updateMessageStatus method - messages table no longer exists

  async saveFailedMessage(failedMessageData) {
    try {
      const result = await this.runQuery(
        `INSERT INTO failed_messages
         (username, original_comment, customer_type, status, error_message, failed_at,
          retry_count, max_retries, comment_timestamp, search_attempted, verification_failed)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          failedMessageData.username,
          failedMessageData.original_comment,
          failedMessageData.customer_type,
          failedMessageData.status,
          failedMessageData.error_message,
          failedMessageData.failed_at,
          failedMessageData.retry_count,
          failedMessageData.max_retries,
          failedMessageData.comment_timestamp,
          failedMessageData.search_attempted,
          failedMessageData.verification_failed ? 1 : 0
        ]
      );

      this.logger.info('Failed message saved with ID:', result.lastID);
      return result.lastID;
    } catch (error) {
      this.logger.error('Error saving failed message:', error);
      throw error;
    }
  }

  async getFailedMessages(limit = 50, offset = 0) {
    try {
      const query = `
        SELECT * FROM failed_messages
        WHERE resolved = FALSE
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
      `;

      const rows = await this.allQuery(query, [limit, offset]);
      return rows || [];
    } catch (error) {
      this.logger.error('Error getting failed messages:', error);
      return [];
    }
  }

  async markFailedMessageResolved(id, resolvedBy = null, notes = null) {
    try {
      await this.runQuery(
        `UPDATE failed_messages
         SET resolved = TRUE, resolved_at = CURRENT_TIMESTAMP, resolved_by = ?, notes = ?
         WHERE id = ?`,
        [resolvedBy, notes, id]
      );

      this.logger.info('Failed message marked as resolved:', id);
      return true;
    } catch (error) {
      this.logger.error('Error marking failed message as resolved:', error);
      throw error;
    }
  }

  async deleteFailedMessage(id) {
    try {
      await this.runQuery('DELETE FROM failed_messages WHERE id = ?', [id]);
      this.logger.info('Failed message deleted:', id);
      return true;
    } catch (error) {
      this.logger.error('Error deleting failed message:', error);
      throw error;
    }
  }

  async getFailedMessageById(id) {
    try {
      const query = 'SELECT * FROM failed_messages WHERE id = ?';
      const result = await this.getQuery(query, [id]);
      return result;
    } catch (error) {
      this.logger.error('Error getting failed message by ID:', error);
      throw error;
    }
  }

  async deleteAllFailedMessages() {
    try {
      // Get count before deletion
      const countResult = await this.getQuery('SELECT COUNT(*) as count FROM failed_messages WHERE resolved = FALSE');
      const count = countResult.count;

      // Delete all unresolved failed messages
      await this.runQuery('DELETE FROM failed_messages WHERE resolved = FALSE');

      this.logger.info(`Deleted ${count} failed messages`);
      return count;
    } catch (error) {
      this.logger.error('Error deleting all failed messages:', error);
      throw error;
    }
  }

  async getStats() {
    try {
      const queries = [
        'SELECT COUNT(*) as printed_count FROM printed_history',
        'SELECT COUNT(*) as queued_messages FROM message_queue WHERE status = "pending"',
        'SELECT COUNT(*) as completed_messages FROM message_queue WHERE status = "completed"',
        'SELECT COUNT(*) as failed_messages FROM message_queue WHERE status = "failed"',
        'SELECT COUNT(*) as regular_customers FROM regular_customers WHERE is_deleted = 0',
        'SELECT COUNT(*) as send_once_records FROM send_once_history'
      ];

      const results = await Promise.all(
        queries.map(query => this.getQuery(query))
      );

      return {
        printedCount: results[0].printed_count || 0,
        queuedMessages: results[1].queued_messages || 0,
        completedMessages: results[2].completed_messages || 0,
        failedMessages: results[3].failed_messages || 0,
        regularCustomers: results[4].regular_customers || 0,
        sendOnceRecords: results[5].send_once_records || 0,
        // Legacy fields for compatibility (set to 0)
        totalComments: 0,
        processedComments: 0,
        printedComments: results[0].printed_count || 0,
        totalMessages: (results[1].queued_messages || 0) + (results[2].completed_messages || 0) + (results[3].failed_messages || 0),
        sentMessages: results[2].completed_messages || 0,
        totalOrders: 0,
        totalRevenue: 0
      };
    } catch (error) {
      this.logger.error('Failed to get stats:', error);
      throw error;
    }
  }

  // Message Templates methods
  async getMessageTemplates(customerType = null, templateType = null) {
    try {
      let query = 'SELECT * FROM message_templates';
      let params = [];
      let conditions = [];

      if (customerType) {
        conditions.push('customer_type = ?');
        params.push(customerType);
      }

      if (templateType) {
        conditions.push('template_type = ?');
        params.push(templateType);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' ORDER BY template_type, customer_type, name';

      const templates = await this.allQuery(query, params);
      return templates;
    } catch (error) {
      this.logger.error('Failed to get message templates:', error);
      throw error;
    }
  }

  async getMessageTemplate(templateName, customerType = 'regular', templateType = 'normal') {
    try {
      const template = await this.getQuery(
        'SELECT * FROM message_templates WHERE name = ? AND customer_type = ? AND template_type = ? AND is_active = 1',
        [templateName, customerType, templateType]
      );
      return template;
    } catch (error) {
      this.logger.error('Failed to get message template:', error);
      return null;
    }
  }

  async getActiveMessageTemplates(customerType = null) {
    try {
      let query = 'SELECT * FROM message_templates WHERE is_active = 1';
      let params = [];

      if (customerType) {
        query += ' AND customer_type = ?';
        params.push(customerType);
      }

      query += ' ORDER BY customer_type, name';

      const templates = await this.allQuery(query, params);
      return templates;
    } catch (error) {
      this.logger.error('Failed to get active message templates:', error);
      throw error;
    }
  }

  // Alias method for InstagramMessenger compatibility
  async getActiveTemplatesByCustomerType(customerType) {
    return this.getActiveMessageTemplates(customerType);
  }

  async saveMessageTemplate(templateData) {
    try {
      const { id, name, template, variables, description, customer_type, template_type, is_active, send_once } = templateData;

      if (id) {
        // Update existing template
        await this.runQuery(
          `UPDATE message_templates
           SET name = ?, template = ?, variables = ?, description = ?, customer_type = ?, template_type = ?, is_active = ?, send_once = ?, updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [name, template, variables, description, customer_type, template_type || 'normal', is_active, send_once || false, id]
        );
      } else {
        // Insert new template
        await this.runQuery(
          `INSERT INTO message_templates (name, template, variables, description, customer_type, template_type, is_active, send_once)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
          [name, template, variables, description, customer_type, template_type || 'normal', is_active, send_once || false]
        );
      }

      this.logger.info(`Message template saved: ${name} (${customer_type}) [${template_type || 'normal'}]`);
    } catch (error) {
      this.logger.error('Failed to save message template:', error);
      throw error;
    }
  }

  async deleteMessageTemplate(templateId) {
    try {
      await this.runQuery('DELETE FROM message_templates WHERE id = ?', [templateId]);
      this.logger.info(`Message template deleted: ${templateId}`);
    } catch (error) {
      this.logger.error('Failed to delete message template:', error);
      throw error;
    }
  }

  async close() {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((error) => {
          if (error) {
            this.logger.error('Error closing database:', error);
          } else {
            this.logger.info('Database connection closed');
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  // REMOVED: Auto Messages methods - not used, replaced by message_templates system

  // Print Settings methods (unified)
  async getPrintSettings() {
    try {
      const query = 'SELECT * FROM print_settings ORDER BY id DESC LIMIT 1';
      const result = await this.allQuery(query);

      if (result.length > 0) {
        return JSON.parse(result[0].settings);
      }

      // Return default settings
      return {
        printer: {
          selectedPrinter: '',
          connectionType: 'system',
          printMode: 'bitmap',
          cutType: 'partial'
        },
        singleComment: {
          username: {
            fontSize: 32, fontWeight: 'bold', fontStyle: 'normal',
            fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 6
          },
          timestamp: {
            fontSize: 20, fontWeight: 'normal', fontStyle: 'normal',
            fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 8
          },
          content: {
            fontSize: 28, fontWeight: 'normal', fontStyle: 'normal',
            fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 10
          },
          padding: 12, lineSpacing: 4, showHeader: false, headerText: '', showFooter: false, footerText: ''
        },
        userHistory: {
          header: {
            fontSize: 16, fontWeight: 'bold', fontStyle: 'normal',
            fontFamily: 'Arial, sans-serif', textAlign: 'center', marginBottom: 8
          },
          username: {
            fontSize: 18, fontWeight: 'bold', fontStyle: 'normal',
            fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 6
          },
          commentItem: {
            fontSize: 14, fontWeight: 'normal', fontStyle: 'normal',
            fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 4
          },
          timestamp: {
            fontSize: 10, fontWeight: 'normal', fontStyle: 'normal',
            fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 6
          },
          separator: { show: true, style: '---' },
          padding: 8, lineSpacing: 1, maxCommentsPerPage: 10, showSummary: true
        }
      };
    } catch (error) {
      this.logger.error('Failed to get print settings:', error);
      throw error;
    }
  }

  async savePrintSettings(settings) {
    try {
      // Delete existing settings
      await this.runQuery('DELETE FROM print_settings');

      // Insert new settings
      const query = `
        INSERT INTO print_settings (settings, created_at, updated_at)
        VALUES (?, datetime('now'), datetime('now'))
      `;

      await this.runQuery(query, [JSON.stringify(settings)]);

      this.logger.info('Print settings saved successfully');
    } catch (error) {
      this.logger.error('Failed to save print settings:', error);
      throw error;
    }
  }

  // REMOVED: updateAutoMessageStats method - relied on deleted auto_messages table

  // Cookie management methods
  async saveCookies(username, cookies, accountType = 'scraper') {
    try {
      // Clear existing cookies for this user and account type first
      await this.runQuery('DELETE FROM saved_cookies WHERE username = ? AND account_type = ?', [username, accountType]);

      // Insert new cookies
      const result = await this.runQuery(
        'INSERT INTO saved_cookies (username, cookies, account_type) VALUES (?, ?, ?)',
        [username, JSON.stringify(cookies), accountType]
      );

      this.logger.info(`Cookies saved successfully for user: ${username} (${accountType})`);
      return result;
    } catch (error) {
      this.logger.error('Failed to save cookies:', error);
      throw error;
    }
  }

  async getSavedCookies(username = null, accountType = 'scraper') {
    try {
      let query, params;
      if (username) {
        query = 'SELECT username, cookies, account_type FROM saved_cookies WHERE username = ? AND account_type = ? ORDER BY created_at DESC LIMIT 1';
        params = [username, accountType];
      } else {
        query = 'SELECT username, cookies, account_type FROM saved_cookies WHERE account_type = ? ORDER BY created_at DESC LIMIT 1';
        params = [accountType];
      }

      const result = await this.getQuery(query, params);
      if (result) {
        return {
          username: result.username,
          cookies: JSON.parse(result.cookies),
          accountType: result.account_type
        };
      }
      return null;
    } catch (error) {
      this.logger.error('Failed to get saved cookies:', error);
      throw error;
    }
  }

  async clearCookies(username = null, accountType = null) {
    try {
      if (username && accountType) {
        await this.runQuery('DELETE FROM saved_cookies WHERE username = ? AND account_type = ?', [username, accountType]);
        this.logger.info(`Cookies cleared for user: ${username} (${accountType})`);
      } else if (username) {
        await this.runQuery('DELETE FROM saved_cookies WHERE username = ?', [username]);
        this.logger.info(`All cookies cleared for user: ${username}`);
      } else if (accountType) {
        await this.runQuery('DELETE FROM saved_cookies WHERE account_type = ?', [accountType]);
        this.logger.info(`All cookies cleared for account type: ${accountType}`);
      } else {
        await this.runQuery('DELETE FROM saved_cookies');
        this.logger.info('All cookies cleared');
      }
    } catch (error) {
      this.logger.error('Failed to clear cookies:', error);
      throw error;
    }
  }

  // Messenger-specific cookie methods
  async getMessengerSavedCookies() {
    return this.getSavedCookies(null, 'messenger');
  }

  async saveMessengerCookies(username, cookies) {
    return this.saveCookies(username, cookies, 'messenger');
  }

  async clearMessengerSavedCookies() {
    try {
      this.logger.info('=== CLEARING MESSENGER COOKIES ===');

      // Check what exists before clearing
      const beforeClear = await this.allQuery('SELECT username, account_type FROM saved_cookies WHERE account_type = ?', ['messenger']);
      this.logger.info('Cookies before clear:', beforeClear.length > 0 ? beforeClear.map(c => `${c.username} (${c.account_type})`).join(', ') : 'None');

      // Clear cookies
      const result = await this.clearCookies(null, 'messenger');

      // Verify clearing worked
      const afterClear = await this.allQuery('SELECT username, account_type FROM saved_cookies WHERE account_type = ?', ['messenger']);
      this.logger.info('Cookies after clear:', afterClear.length > 0 ? afterClear.map(c => `${c.username} (${c.account_type})`).join(', ') : 'None');

      if (afterClear.length > 0) {
        this.logger.error('WARNING: Messenger cookies still exist after clearing!');
      } else {
        this.logger.info('Messenger cookies successfully cleared');
      }

      return result;
    } catch (error) {
      this.logger.error('Failed to clear messenger cookies:', error);
      throw error;
    }
  }

  async checkColumnExists(tableName, columnName) {
    try {
      const result = await this.allQuery(`PRAGMA table_info(${tableName})`);
      return result.some(column => column.name === columnName);
    } catch (error) {
      this.logger.error(`Failed to check column ${columnName} in table ${tableName}:`, error);
      return false;
    }
  }

  async checkTableExists(tableName) {
    try {
      const result = await this.allQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name=?",
        [tableName]
      );
      return result.length > 0;
    } catch (error) {
      this.logger.error(`Failed to check table ${tableName}:`, error);
      return false;
    }
  }

  // Regular customers methods
  async getRegularCustomers() {
    try {
      const customers = await this.allQuery(
        'SELECT username, marked_at, notes, updated_at, synced_at, device_id FROM regular_customers WHERE is_deleted = 0 ORDER BY marked_at DESC'
      );
      return customers;
    } catch (error) {
      this.logger.error('Failed to get regular customers:', error);
      throw error;
    }
  }

  // Get all customers including sync metadata for MongoDB sync
  async getRegularCustomersForSync() {
    try {
      const customers = await this.allQuery(
        'SELECT username, marked_at, notes, updated_at, synced_at, device_id, is_deleted FROM regular_customers ORDER BY updated_at DESC'
      );
      return customers;
    } catch (error) {
      this.logger.error('Failed to get regular customers for sync:', error);
      throw error;
    }
  }

  async addRegularCustomer(username, notes = '', deviceId = null) {
    try {
      const currentDeviceId = deviceId || await this.getDeviceId();
      await this.runQuery(
        'INSERT OR REPLACE INTO regular_customers (username, notes, marked_at, updated_at, device_id, is_deleted) VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?, 0)',
        [username, notes, currentDeviceId]
      );
      this.logger.info(`Added regular customer: ${username}`);

      // Auto-sync to MongoDB if connected
      await this.autoSyncToMongoDB('add', { username, notes, device_id: currentDeviceId });
    } catch (error) {
      this.logger.error('Failed to add regular customer:', error);
      throw error;
    }
  }

  async removeRegularCustomer(username) {
    try {
      const currentDeviceId = await this.getDeviceId();
      // Soft delete instead of hard delete for sync purposes
      await this.runQuery(
        'UPDATE regular_customers SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP, device_id = ? WHERE username = ?',
        [currentDeviceId, username]
      );
      this.logger.info(`Removed regular customer: ${username}`);

      // Auto-sync to MongoDB if connected
      await this.autoSyncToMongoDB('remove', { username, device_id: currentDeviceId });
    } catch (error) {
      this.logger.error('Failed to remove regular customer:', error);
      throw error;
    }
  }

  async isRegularCustomer(username) {
    try {
      const result = await this.allQuery(
        'SELECT 1 FROM regular_customers WHERE username = ? AND is_deleted = 0',
        [username]
      );
      return result.length > 0;
    } catch (error) {
      this.logger.error('Failed to check regular customer:', error);
      return false;
    }
  }

  // Device ID management
  async getDeviceId() {
    try {
      let deviceId = await this.getSetting('device_id');
      if (!deviceId) {
        // Generate unique device ID
        deviceId = `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        await this.saveSetting('device_id', deviceId);
        this.logger.info(`Generated new device ID: ${deviceId}`);
      }
      return deviceId;
    } catch (error) {
      this.logger.error('Failed to get device ID:', error);
      return `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
  }

  // Auto-sync to MongoDB when changes occur
  async autoSyncToMongoDB(action, customerData) {
    try {
      // Check if MongoDB service is available and connected
      if (global.mongoDBService && global.mongoDBService.isConnected) {
        this.logger.info(`Auto-syncing ${action} to MongoDB for customer: ${customerData.username}`);

        if (action === 'add') {
          await global.mongoDBService.syncSingleCustomerToMongo(customerData);
        } else if (action === 'remove') {
          await global.mongoDBService.markCustomerAsDeleted(customerData.username, customerData.device_id);
        }

        // Update synced_at timestamp
        await this.runQuery(
          'UPDATE regular_customers SET synced_at = CURRENT_TIMESTAMP WHERE username = ?',
          [customerData.username]
        );
      }
    } catch (error) {
      this.logger.warn('Auto-sync to MongoDB failed:', error.message);
      // Don't throw error - sync failure shouldn't break local operations
    }
  }

  // Auto-sync printed_history to MongoDB when changes occur
  async autoSyncPrintedHistoryToMongoDB(action, printedHistoryData) {
    try {
      // Check if MongoDB service is available and connected
      if (global.mongoDBService && global.mongoDBService.isConnected) {
        this.logger.info(`Auto-syncing ${action} printed history to MongoDB for user: ${printedHistoryData.username}`);

        if (action === 'add') {
          await global.mongoDBService.syncSinglePrintedHistoryToMongo(printedHistoryData);
        } else if (action === 'delete') {
          if (printedHistoryData.bulk_delete) {
            // Bulk delete by username
            await global.mongoDBService.markUserPrintedHistoryAsDeleted(printedHistoryData.username, printedHistoryData.device_id);
          } else {
            // Single record delete
            await global.mongoDBService.markPrintedHistoryAsDeleted(printedHistoryData.id, printedHistoryData.device_id);
          }
        } else if (action === 'restore') {
          await global.mongoDBService.restorePrintedHistoryInMongo(printedHistoryData.id, printedHistoryData.device_id);
        }

        // Update synced_at timestamp (only for single record operations)
        if (printedHistoryData.id && !printedHistoryData.bulk_delete && !printedHistoryData.bulk_restore) {
          await this.runQuery(
            'UPDATE printed_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
            [printedHistoryData.id]
          );
        }
      }
    } catch (error) {
      this.logger.warn('Auto-sync printed history to MongoDB failed:', error.message);
      // Don't throw error - sync failure shouldn't break local operations
    }
  }

  // Auto-sync send_once_history to MongoDB when changes occur
  async autoSyncSendOnceHistoryToMongoDB(action, sendOnceHistoryData) {
    try {
      // Check if MongoDB service is available and connected
      if (global.mongoDBService && global.mongoDBService.isConnected) {
        this.logger.info(`Auto-syncing ${action} send_once history to MongoDB for user: ${sendOnceHistoryData.username}`);

        if (action === 'add') {
          await global.mongoDBService.syncSingleSendOnceHistoryToMongo(sendOnceHistoryData);
        } else if (action === 'delete') {
          await global.mongoDBService.markSendOnceHistoryAsDeleted(sendOnceHistoryData.id, sendOnceHistoryData.device_id);
        }

        // Update synced_at timestamp
        await this.runQuery(
          'UPDATE send_once_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
          [sendOnceHistoryData.id]
        );
      }
    } catch (error) {
      this.logger.warn('Auto-sync send_once history to MongoDB failed:', error.message);
      // Don't throw error - sync failure shouldn't break local operations
    }
  }

  // Smart bidirectional sync
  async smartSyncWithMongoDB(mongoCustomers) {
    try {
      const localCustomers = await this.getRegularCustomersForSync();
      const currentDeviceId = await this.getDeviceId();

      this.logger.info(`Starting smart sync: ${localCustomers.length} local, ${mongoCustomers.length} from MongoDB`);

      let addedToLocal = 0;
      let updatedLocal = 0;
      let addedToMongo = 0;
      let updatedMongo = 0;

      // Create maps for efficient lookup
      const localMap = new Map(localCustomers.map(c => [c.username, c]));
      const mongoMap = new Map(mongoCustomers.map(c => [c.username, c]));

      // Process MongoDB customers
      for (const mongoCustomer of mongoCustomers) {
        const localCustomer = localMap.get(mongoCustomer.username);

        if (!localCustomer) {
          // Customer exists in MongoDB but not locally - add to local
          if (!mongoCustomer.is_deleted) {
            await this.runQuery(
              'INSERT INTO regular_customers (username, notes, marked_at, updated_at, synced_at, device_id, is_deleted) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 0)',
              [mongoCustomer.username, mongoCustomer.notes || '', mongoCustomer.marked_at, mongoCustomer.updated_at, mongoCustomer.device_id]
            );
            addedToLocal++;
            this.logger.info(`Added from MongoDB: ${mongoCustomer.username}`);
          }
        } else {
          // Customer exists in both - check which is newer
          const mongoUpdated = new Date(mongoCustomer.updated_at);
          const localUpdated = new Date(localCustomer.updated_at);

          if (mongoUpdated > localUpdated) {
            // MongoDB version is newer - update local
            await this.runQuery(
              'UPDATE regular_customers SET notes = ?, marked_at = ?, updated_at = ?, synced_at = CURRENT_TIMESTAMP, is_deleted = ? WHERE username = ?',
              [mongoCustomer.notes || '', mongoCustomer.marked_at, mongoCustomer.updated_at, mongoCustomer.is_deleted ? 1 : 0, mongoCustomer.username]
            );
            updatedLocal++;
            this.logger.info(`Updated from MongoDB: ${mongoCustomer.username}`);
          } else if (localUpdated > mongoUpdated) {
            // Local version is newer - sync to MongoDB
            if (global.mongoDBService && global.mongoDBService.isConnected) {
              await global.mongoDBService.syncSingleCustomerToMongo({
                username: localCustomer.username,
                notes: localCustomer.notes || '',
                marked_at: localCustomer.marked_at,
                updated_at: localCustomer.updated_at,
                device_id: localCustomer.device_id || currentDeviceId,
                is_deleted: Boolean(localCustomer.is_deleted) // Convert SQLite integer to boolean
              });

              // Update synced_at
              await this.runQuery(
                'UPDATE regular_customers SET synced_at = CURRENT_TIMESTAMP WHERE username = ?',
                [localCustomer.username]
              );

              updatedMongo++;
              this.logger.info(`Updated MongoDB from local: ${localCustomer.username}`);
            }
          }
        }
      }

      // Process local customers that don't exist in MongoDB
      for (const localCustomer of localCustomers) {
        if (!mongoMap.has(localCustomer.username)) {
          // Customer exists locally but not in MongoDB - add to MongoDB
          if (global.mongoDBService && global.mongoDBService.isConnected) {
            await global.mongoDBService.syncSingleCustomerToMongo({
              username: localCustomer.username,
              notes: localCustomer.notes || '',
              marked_at: localCustomer.marked_at,
              updated_at: localCustomer.updated_at,
              device_id: localCustomer.device_id || currentDeviceId,
              is_deleted: Boolean(localCustomer.is_deleted) // Convert SQLite integer to boolean
            });

            // Update synced_at
            await this.runQuery(
              'UPDATE regular_customers SET synced_at = CURRENT_TIMESTAMP WHERE username = ?',
              [localCustomer.username]
            );

            addedToMongo++;
            this.logger.info(`Added to MongoDB: ${localCustomer.username}`);
          }
        }
      }

      const result = {
        addedToLocal,
        updatedLocal,
        addedToMongo,
        updatedMongo,
        totalProcessed: mongoCustomers.length + localCustomers.length
      };

      this.logger.info(`Smart sync completed:`, result);
      return result;

    } catch (error) {
      this.logger.error('Smart sync failed:', error);
      throw error;
    }
  }

  async isFirstTimeUser(username) {
    try {
      // Check if user has ever been sent a message before
      const messageHistory = await this.allQuery(
        'SELECT 1 FROM messages WHERE username = ? AND status = "sent" LIMIT 1',
        [username]
      );

      // Also check if user has been printed before
      const printHistory = await this.allQuery(
        'SELECT 1 FROM comments WHERE username = ? AND printed = TRUE LIMIT 1',
        [username]
      );

      // User is first-time if they have no message history AND no print history
      return messageHistory.length === 0 && printHistory.length === 0;
    } catch (error) {
      this.logger.error('Failed to check first-time user:', error);
      return false;
    }
  }

  // Send_once tracking methods - lightweight replacement for message history
  async hasReceivedSendOnceTemplate(username, templateName, customerType = 'regular', templateType = 'normal') {
    try {
      this.logger.info(`🔍 Checking send_once template history for @${username} with template: "${templateName}" (${customerType}/${templateType})`);

      // Check if user has received this send_once template before (only non-deleted records)
      const sentRecord = await this.getQuery(
        'SELECT 1 FROM send_once_history WHERE username = ? AND template_name = ? AND customer_type = ? AND template_type = ? AND is_deleted = 0 LIMIT 1',
        [username, templateName, customerType, templateType]
      );

      const hasReceived = !!sentRecord;
      this.logger.info(`📊 Send_once query result: ${hasReceived ? 'FOUND' : 'NOT_FOUND'} for @${username} + "${templateName}" (${customerType}/${templateType})`);

      return hasReceived;
    } catch (error) {
      this.logger.error('Failed to check send_once template:', error);
      return false;
    }
  }

  async recordSendOnceTemplate(username, templateName, customerType = 'regular', templateType = 'normal') {
    try {
      // Get current device ID
      const currentDeviceId = await this.getDeviceId();

      // Check if updated_at column exists
      const tableInfo = await this.allQuery("PRAGMA table_info(send_once_history)");
      const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

      // Use INSERT OR REPLACE to handle duplicates and get the record ID
      let result;
      if (hasUpdatedAt) {
        result = await this.runQuery(
          'INSERT OR REPLACE INTO send_once_history (username, template_name, customer_type, template_type, device_id, updated_at) VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)',
          [username, templateName, customerType, templateType, currentDeviceId]
        );
      } else {
        result = await this.runQuery(
          'INSERT OR REPLACE INTO send_once_history (username, template_name, customer_type, template_type, device_id) VALUES (?, ?, ?, ?, ?)',
          [username, templateName, customerType, templateType, currentDeviceId]
        );
      }

      // Get the record for sync
      const record = await this.getQuery(
        'SELECT * FROM send_once_history WHERE username = ? AND template_name = ? AND customer_type = ? AND template_type = ?',
        [username, templateName, customerType, templateType]
      );

      if (record) {
        // Auto-sync to MongoDB if connected
        await this.autoSyncSendOnceHistoryToMongoDB('add', {
          id: record.id,
          username: record.username,
          template_name: record.template_name,
          customer_type: record.customer_type,
          template_type: record.template_type,
          sent_at: record.sent_at,
          created_at: record.created_at,
          device_id: record.device_id,
          is_deleted: false
        });
      }

      this.logger.info(`💾 Recorded send_once template: @${username} + "${templateName}" (${customerType}/${templateType})`);
    } catch (error) {
      this.logger.error('Failed to record send_once template:', error);
    }
  }

  async getSendOnceHistory(username = null, limit = 100) {
    try {
      let query = 'SELECT * FROM send_once_history';
      let params = [];

      if (username) {
        query += ' WHERE username = ?';
        params.push(username);
      }

      query += ' ORDER BY sent_at DESC LIMIT ?';
      params.push(limit);

      return await this.allQuery(query, params);
    } catch (error) {
      this.logger.error('Failed to get send_once history:', error);
      return [];
    }
  }

  async cleanupOldSendOnceHistory(olderThanDays = 2) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffISO = cutoffDate.toISOString();

      // Get records to be deleted for sync
      const recordsToDelete = await this.allQuery(
        'SELECT * FROM send_once_history WHERE sent_at < ? AND is_deleted = 0',
        [cutoffISO]
      );

      // Check if updated_at column exists
      const tableInfo = await this.allQuery("PRAGMA table_info(send_once_history)");
      const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

      // Mark as deleted instead of hard delete for sync purposes
      let result;
      if (hasUpdatedAt) {
        result = await this.runQuery(
          'UPDATE send_once_history SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE sent_at < ? AND is_deleted = 0',
          [cutoffISO]
        );
      } else {
        result = await this.runQuery(
          'UPDATE send_once_history SET is_deleted = 1 WHERE sent_at < ? AND is_deleted = 0',
          [cutoffISO]
        );
      }

      // Auto-sync deletions to MongoDB
      if (result.changes > 0) {
        const currentDeviceId = await this.getDeviceId();

        // Sync each deleted record
        for (const record of recordsToDelete) {
          await this.autoSyncSendOnceHistoryToMongoDB('delete', {
            id: record.id,
            username: record.username,
            template_name: record.template_name,
            customer_type: record.customer_type,
            template_type: record.template_type,
            device_id: record.device_id || currentDeviceId
          });
        }

        this.logger.info(`🗑️ Cleaned up ${result.changes} send_once history records older than ${olderThanDays} days`);
      }

      return result.changes;
    } catch (error) {
      this.logger.error('Failed to cleanup old send_once history:', error);
      throw error;
    }
  }

  // Auto Message Settings methods
  async getAutoMessageSettings() {
    try {
      const query = 'SELECT * FROM auto_message_settings ORDER BY id DESC LIMIT 1';
      const result = await this.allQuery(query);

      if (result.length > 0) {
        return JSON.parse(result[0].settings);
      }

      // Return default settings
      return {
        enabled: false,
        delayBetweenMessages: 2000,
        maxRetries: 3,
        requireMessengerLogin: true,
        maxMessagesBeforeRestart: 2000,
        maxMemoryUsageMB: 3000
      };
    } catch (error) {
      this.logger.error('Failed to get auto message settings:', error);
      throw error;
    }
  }

  async saveAutoMessageSettings(settings) {
    try {
      // Delete existing settings
      await this.runQuery('DELETE FROM auto_message_settings');

      // Insert new settings
      const query = `
        INSERT INTO auto_message_settings (settings, created_at, updated_at)
        VALUES (?, datetime('now'), datetime('now'))
      `;

      await this.runQuery(query, [JSON.stringify(settings)]);

      this.logger.info('Auto message settings saved successfully');
    } catch (error) {
      this.logger.error('Failed to save auto message settings:', error);
      throw error;
    }
  }

  async getAutoMessageStats() {
    try {
      const query = 'SELECT * FROM auto_message_stats ORDER BY id DESC LIMIT 1';
      const result = await this.allQuery(query);

      if (result.length > 0) {
        const stats = result[0];
        return {
          totalSent: stats.total_sent,
          successRate: stats.total_sent > 0 ? Math.round((stats.total_success / stats.total_sent) * 100) : 0,
          lastSent: stats.last_sent
        };
      }

      return {
        totalSent: 0,
        successRate: 0,
        lastSent: null
      };
    } catch (error) {
      this.logger.error('Failed to get auto message stats:', error);
      throw error;
    }
  }

  async updateAutoMessageStats(success = true) {
    try {
      // Get current stats
      const currentStats = await this.allQuery('SELECT * FROM auto_message_stats ORDER BY id DESC LIMIT 1');

      if (currentStats.length > 0) {
        // Update existing stats
        const stats = currentStats[0];
        const query = `
          UPDATE auto_message_stats
          SET total_sent = ?,
              total_success = ?,
              total_failed = ?,
              last_sent = datetime('now'),
              updated_at = datetime('now')
          WHERE id = ?
        `;

        await this.runQuery(query, [
          stats.total_sent + 1,
          stats.total_success + (success ? 1 : 0),
          stats.total_failed + (success ? 0 : 1),
          stats.id
        ]);
      } else {
        // Create new stats record
        const query = `
          INSERT INTO auto_message_stats (total_sent, total_success, total_failed, last_sent, created_at, updated_at)
          VALUES (1, ?, ?, datetime('now'), datetime('now'), datetime('now'))
        `;

        await this.runQuery(query, [
          success ? 1 : 0,
          success ? 0 : 1
        ]);
      }

      this.logger.info(`Auto message stats updated: ${success ? 'success' : 'failed'}`);
    } catch (error) {
      this.logger.error('Failed to update auto message stats:', error);
      throw error;
    }
  }

  // Print history methods - simplified to only save to printed_history
  async markCommentAsPrinted(commentId, username, commentText, printType = 'comment') {
    try {
      // Use Vietnam timezone for printed_at timestamp
      const vietnamTime = new Date().toLocaleString('sv-SE', {
        timeZone: 'Asia/Ho_Chi_Minh'
      });

      // Get current device ID
      const currentDeviceId = await this.getDeviceId();

      // Add to printed history (each print creates a new record)
      const result = await this.runQuery(
        'INSERT INTO printed_history (comment_id, username, comment_text, print_type, printed_at, device_id) VALUES (?, ?, ?, ?, ?, ?)',
        [commentId, username, commentText, printType, vietnamTime, currentDeviceId]
      );

      const printedHistoryId = result.lastID;

      // Auto-sync to MongoDB if connected
      const printedHistoryData = {
        id: printedHistoryId,
        comment_id: commentId,
        username: username,
        comment_text: commentText,
        print_type: printType,
        printed_at: vietnamTime,
        device_id: currentDeviceId,
        is_deleted: false
      };

      await this.autoSyncPrintedHistoryToMongoDB('add', printedHistoryData);

      this.logger.info(`Comment marked as printed: ${commentId}, type: ${printType}, user: @${username}`);
    } catch (error) {
      this.logger.error('Failed to mark comment as printed:', error);
      throw error;
    }
  }

  async getPrintedHistory(options = {}) {
    try {
      const {
        page = 1,
        limit = 50,
        search = '',
        dateFilter = 'all',
        startDate,
        endDate,
        startTime,
        endTime,
        filterType,
        applyTimeToAllDays
      } = options;
      const offset = (page - 1) * limit;

      let whereClause = 'WHERE 1=1';
      let params = [];

      // Add search filter
      if (search) {
        whereClause += ' AND (username LIKE ? OR comment_text LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }

      // Add basic date filters
      if (dateFilter !== 'all') {
        if (dateFilter === 'today') {
          whereClause += ' AND DATE(printed_at) = DATE("now", "localtime")';
        } else if (dateFilter === 'yesterday') {
          whereClause += ' AND DATE(printed_at) = DATE("now", "-1 day", "localtime")';
        } else if (dateFilter === 'week') {
          whereClause += ' AND DATE(printed_at) >= DATE("now", "-7 days", "localtime")';
        } else if (dateFilter === 'month') {
          whereClause += ' AND DATE(printed_at) >= DATE("now", "-30 days", "localtime")';
        }
      }

      // Add advanced filters based on filterType
      if (filterType && (startDate || startTime)) {
        switch (filterType) {
          case 'date':
            // Filter by date range only
            if (startDate) {
              whereClause += ' AND DATE(printed_at) >= ?';
              params.push(startDate);
            }
            if (endDate) {
              whereClause += ' AND DATE(printed_at) <= ?';
              params.push(endDate);
            }
            break;

          case 'time':
            // Filter by time range (can apply to all days or just today)
            if (startTime && endTime) {
              if (applyTimeToAllDays) {
                // Apply time filter to all days
                whereClause += ' AND TIME(printed_at) >= ? AND TIME(printed_at) <= ?';
                params.push(startTime, endTime);
              } else {
                // Apply time filter to today only
                whereClause += ' AND DATE(printed_at) = DATE("now", "localtime") AND TIME(printed_at) >= ? AND TIME(printed_at) <= ?';
                params.push(startTime, endTime);
              }
            }
            break;

          case 'datetime':
            // Filter by time range within date range
            if (startDate && endDate && startTime && endTime) {
              whereClause += ' AND DATE(printed_at) >= ? AND DATE(printed_at) <= ? AND TIME(printed_at) >= ? AND TIME(printed_at) <= ?';
              params.push(startDate, endDate, startTime, endTime);
            } else if (startDate && startTime) {
              // Single day with time range
              whereClause += ' AND DATE(printed_at) = ? AND TIME(printed_at) >= ?';
              params.push(startDate, startTime);
              if (endTime) {
                whereClause += ' AND TIME(printed_at) <= ?';
                params.push(endTime);
              }
            }
            break;
        }
      }

      // Add is_deleted = 0 filter for normal view (only show non-deleted records)
      whereClause += ' AND is_deleted = 0';

      const countQuery = `SELECT COUNT(*) as total FROM printed_history ${whereClause}`;
      const dataQuery = `
        SELECT id, comment_id, username, comment_text, print_type, printed_at, created_at,
               synced_at, device_id, is_deleted
        FROM printed_history ${whereClause} ORDER BY printed_at DESC LIMIT ? OFFSET ?
      `;

      const [totalResult, history] = await Promise.all([
        this.getQuery(countQuery, params),
        this.allQuery(dataQuery, [...params, limit, offset])
      ]);

      return {
        history,
        total: totalResult.total,
        page,
        limit,
        totalPages: Math.ceil(totalResult.total / limit)
      };
    } catch (error) {
      this.logger.error('Failed to get printed history:', error);
      throw error;
    }
  }

  // Get deleted printed history with pagination and filtering
  async getDeletedPrintedHistory(options = {}) {
    try {
      const {
        page = 1,
        limit = 50,
        search = '',
        dateFilter = 'all',
        startDate,
        endDate,
        startTime,
        endTime,
        filterType,
        applyTimeToAllDays
      } = options;
      const offset = (page - 1) * limit;

      let whereClause = 'WHERE is_deleted = 1';
      let params = [];

      // Add search filter
      if (search) {
        whereClause += ' AND (username LIKE ? OR comment_text LIKE ?)';
        params.push(`%${search}%`, `%${search}%`);
      }

      // Add basic date filters (based on printed_at, not deletion time)
      if (dateFilter !== 'all') {
        if (dateFilter === 'today') {
          whereClause += ' AND DATE(printed_at) = DATE("now", "localtime")';
        } else if (dateFilter === 'yesterday') {
          whereClause += ' AND DATE(printed_at) = DATE("now", "-1 day", "localtime")';
        } else if (dateFilter === 'week') {
          whereClause += ' AND DATE(printed_at) >= DATE("now", "-7 days", "localtime")';
        } else if (dateFilter === 'month') {
          whereClause += ' AND DATE(printed_at) >= DATE("now", "-30 days", "localtime")';
        }
      }

      // Add advanced filters based on filterType
      if (filterType && (startDate || startTime)) {
        switch (filterType) {
          case 'date':
            if (startDate) {
              whereClause += ' AND DATE(printed_at) >= ?';
              params.push(startDate);
            }
            if (endDate) {
              whereClause += ' AND DATE(printed_at) <= ?';
              params.push(endDate);
            }
            break;

          case 'time':
            if (startTime && endTime) {
              if (applyTimeToAllDays) {
                whereClause += ' AND TIME(printed_at) >= ? AND TIME(printed_at) <= ?';
                params.push(startTime, endTime);
              } else {
                whereClause += ' AND DATE(printed_at) = DATE("now", "localtime") AND TIME(printed_at) >= ? AND TIME(printed_at) <= ?';
                params.push(startTime, endTime);
              }
            }
            break;

          case 'datetime':
            if (startDate && endDate && startTime && endTime) {
              whereClause += ' AND DATE(printed_at) >= ? AND DATE(printed_at) <= ? AND TIME(printed_at) >= ? AND TIME(printed_at) <= ?';
              params.push(startDate, endDate, startTime, endTime);
            } else if (startDate && startTime) {
              whereClause += ' AND DATE(printed_at) = ? AND TIME(printed_at) >= ?';
              params.push(startDate, startTime);
              if (endTime) {
                whereClause += ' AND TIME(printed_at) <= ?';
                params.push(endTime);
              }
            }
            break;
        }
      }

      const countQuery = `SELECT COUNT(*) as total FROM printed_history ${whereClause}`;
      const dataQuery = `
        SELECT id, comment_id, username, comment_text, print_type, printed_at, created_at,
               synced_at, device_id, is_deleted
        FROM printed_history ${whereClause} ORDER BY printed_at DESC LIMIT ? OFFSET ?
      `;

      const [totalResult, history] = await Promise.all([
        this.getQuery(countQuery, params),
        this.allQuery(dataQuery, [...params, limit, offset])
      ]);

      return {
        history,
        total: totalResult.total,
        page,
        limit,
        totalPages: Math.ceil(totalResult.total / limit)
      };
    } catch (error) {
      this.logger.error('Failed to get deleted printed history:', error);
      throw error;
    }
  }

  // REMOVED: getPrintedComments, deleteComment, deleteUserComments, cleanupOldComments methods - comments table no longer exists
  // Use printed_history table methods instead

  // Message Queue methods
  async addToMessageQueue(messageData) {
    try {
      const result = await this.runQuery(
        `INSERT INTO message_queue
         (id, comment_id, username, original_comment, customer_type, template_name, template_type,
          status, retries, max_retries, created_at, updated_at)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
        [
          messageData.id,
          messageData.comment_id || null,
          messageData.username,
          messageData.original_comment,
          messageData.customer_type,
          messageData.template_name || null,
          messageData.template_type || 'normal',
          messageData.status || 'pending',
          messageData.retries || 0,
          messageData.max_retries || 3
        ]
      );

      this.logger.info(`📝 Message added to FIFO queue: ${messageData.id} for @${messageData.username} with template_type: ${messageData.template_type || 'normal'}`);
      return result;
    } catch (error) {
      this.logger.error('Failed to add message to queue:', error);
      throw error;
    }
  }

  async getMessageQueue(status = 'pending', limit = 50) {
    try {
      // FIFO order: oldest messages first (created_at ASC only)
      const query = status === 'all'
        ? 'SELECT * FROM message_queue ORDER BY created_at ASC LIMIT ?'
        : 'SELECT * FROM message_queue WHERE status = ? ORDER BY created_at ASC LIMIT ?';

      const params = status === 'all' ? [limit] : [status, limit];

      return await this.allQuery(query, params);
    } catch (error) {
      this.logger.error('Failed to get message queue:', error);
      throw error;
    }
  }

  async updateMessageStatus(messageId, status, errorMessage = null) {
    try {
      const updateFields = ['status = ?', 'updated_at = CURRENT_TIMESTAMP'];
      const params = [status];

      if (status === 'processing') {
        updateFields.push('processed_at = CURRENT_TIMESTAMP');
      }

      if (errorMessage) {
        updateFields.push('error_message = ?');
        params.push(errorMessage);
      }

      params.push(messageId);

      const result = await this.runQuery(
        `UPDATE message_queue SET ${updateFields.join(', ')} WHERE id = ?`,
        params
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to update message status:', error);
      throw error;
    }
  }

  async incrementMessageRetries(messageId) {
    try {
      const result = await this.runQuery(
        'UPDATE message_queue SET retries = retries + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [messageId]
      );

      return result;
    } catch (error) {
      this.logger.error('Failed to increment message retries:', error);
      throw error;
    }
  }

  async removeFromMessageQueue(messageId) {
    try {
      const result = await this.runQuery('DELETE FROM message_queue WHERE id = ?', [messageId]);
      this.logger.info(`Message removed from queue: ${messageId}`);
      return result.changes;
    } catch (error) {
      this.logger.error('Failed to remove message from queue:', error);
      throw error;
    }
  }

  async resetProcessingMessages() {
    try {
      const result = await this.runQuery(
        'UPDATE message_queue SET status = "pending", updated_at = CURRENT_TIMESTAMP WHERE status = "processing"'
      );

      if (result.changes > 0) {
        this.logger.info(`Reset ${result.changes} stuck processing messages back to pending`);
      }

      return result.changes;
    } catch (error) {
      this.logger.error('Failed to reset processing messages:', error);
      throw error;
    }
  }

  async getMessageQueueStats() {
    try {
      const stats = await this.allQuery(`
        SELECT
          status,
          COUNT(*) as count
        FROM message_queue
        GROUP BY status
      `);

      const result = {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        total: 0
      };

      stats.forEach(stat => {
        result[stat.status] = stat.count;
        result.total += stat.count;
      });

      return result;
    } catch (error) {
      this.logger.error('Failed to get message queue stats:', error);
      throw error;
    }
  }

  async cleanupOldQueueMessages(olderThanDays = 7) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffISO = cutoffDate.toISOString();

      const result = await this.runQuery(
        `DELETE FROM message_queue
         WHERE status IN ('completed', 'failed')
         AND updated_at < ?`,
        [cutoffISO]
      );

      if (result.changes > 0) {
        this.logger.info(`Cleaned up ${result.changes} old queue messages`);
      }

      return result.changes;
    } catch (error) {
      this.logger.error('Failed to cleanup old queue messages:', error);
      throw error;
    }
  }

  // Instagram Threads methods for caching direct message thread IDs with MongoDB sync
  async saveInstagramThread(username, threadId) {
    try {
      const currentDeviceId = await this.getDeviceId();
      const result = await this.runQuery(
        `INSERT OR REPLACE INTO instagram_threads
         (username, thread_id, last_used, updated_at, device_id, is_deleted)
         VALUES (?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, ?, 0)`,
        [username, threadId, currentDeviceId]
      );

      this.logger.info(`💾 Saved Instagram thread ID for @${username}: ${threadId}`);

      // Auto-sync to MongoDB if connected
      await this.autoSyncThreadToMongoDB('save', { username, thread_id: threadId, device_id: currentDeviceId });

      return result;
    } catch (error) {
      this.logger.error('Failed to save Instagram thread:', error);
      throw error;
    }
  }

  async getInstagramThread(username) {
    try {
      const thread = await this.getQuery(
        'SELECT thread_id, last_used FROM instagram_threads WHERE username = ?',
        [username]
      );

      if (thread) {
        // Update last_used timestamp
        await this.runQuery(
          'UPDATE instagram_threads SET last_used = CURRENT_TIMESTAMP WHERE username = ?',
          [username]
        );

        this.logger.info(`🔍 Found cached thread ID for @${username}: ${thread.thread_id}`);
        return thread.thread_id;
      }

      this.logger.info(`❌ No cached thread ID found for @${username}`);
      return null;
    } catch (error) {
      this.logger.error('Failed to get Instagram thread:', error);
      return null;
    }
  }

  async getAllInstagramThreads() {
    try {
      const threads = await this.allQuery(
        'SELECT username, thread_id, last_used, updated_at, synced_at, device_id FROM instagram_threads WHERE is_deleted = 0 ORDER BY last_used DESC'
      );
      return threads;
    } catch (error) {
      this.logger.error('Failed to get all Instagram threads:', error);
      return [];
    }
  }

  // Get all threads including sync metadata for MongoDB sync
  async getInstagramThreadsForSync() {
    try {
      const threads = await this.allQuery(
        'SELECT username, thread_id, last_used, updated_at, synced_at, device_id, is_deleted FROM instagram_threads ORDER BY updated_at DESC'
      );
      return threads;
    } catch (error) {
      this.logger.error('Failed to get Instagram threads for sync:', error);
      throw error;
    }
  }

  async deleteInstagramThread(username) {
    try {
      const currentDeviceId = await this.getDeviceId();
      // Soft delete instead of hard delete for sync purposes
      const result = await this.runQuery(
        'UPDATE instagram_threads SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP, device_id = ? WHERE username = ?',
        [currentDeviceId, username]
      );

      this.logger.info(`🗑️ Deleted Instagram thread for @${username}`);

      // Auto-sync to MongoDB if connected
      await this.autoSyncThreadToMongoDB('delete', { username, device_id: currentDeviceId });

      return result.changes;
    } catch (error) {
      this.logger.error('Failed to delete Instagram thread:', error);
      throw error;
    }
  }

  // Auto-sync Instagram threads to MongoDB when changes occur
  async autoSyncThreadToMongoDB(action, threadData) {
    try {
      // Check if MongoDB service is available and connected
      if (global.mongoDBService && global.mongoDBService.isConnected) {
        this.logger.info(`Auto-syncing ${action} thread to MongoDB for user: ${threadData.username}`);

        if (action === 'save') {
          await global.mongoDBService.syncSingleThreadToMongo(threadData);
        } else if (action === 'delete') {
          await global.mongoDBService.markThreadAsDeleted(threadData.username, threadData.device_id);
        }

        // Update synced_at timestamp
        await this.runQuery(
          'UPDATE instagram_threads SET synced_at = CURRENT_TIMESTAMP WHERE username = ?',
          [threadData.username]
        );
      }
    } catch (error) {
      this.logger.error('Failed to auto-sync thread to MongoDB:', error);
      // Don't throw error - sync failure shouldn't break the main operation
    }
  }

  // Smart sync Instagram threads with MongoDB (bidirectional)
  async smartSyncThreadsWithMongoDB(mongoThreads) {
    try {
      this.logger.info('🔄 Starting smart sync for Instagram threads with MongoDB...');

      const localThreads = await this.getInstagramThreadsForSync();
      const currentDeviceId = await this.getDeviceId();

      let addedToLocal = 0;
      let addedToMongo = 0;
      let updatedLocal = 0;
      let updatedMongo = 0;

      // Create maps for efficient lookup
      const localMap = new Map(localThreads.map(thread => [thread.username, thread]));
      const mongoMap = new Map(mongoThreads.map(thread => [thread.username, thread]));

      // Process MongoDB threads that don't exist locally or need updates
      for (const mongoThread of mongoThreads) {
        const localThread = localMap.get(mongoThread.username);

        if (!localThread) {
          // Thread exists in MongoDB but not locally - add to local
          await this.runQuery(
            `INSERT INTO instagram_threads
             (username, thread_id, last_used, updated_at, synced_at, device_id, is_deleted)
             VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, ?, ?)`,
            [
              mongoThread.username,
              mongoThread.thread_id,
              mongoThread.last_used,
              mongoThread.updated_at,
              mongoThread.device_id || currentDeviceId,
              mongoThread.is_deleted ? 1 : 0
            ]
          );
          addedToLocal++;
          this.logger.info(`📥 Added thread from MongoDB: @${mongoThread.username}`);
        } else {
          // Thread exists in both - check if MongoDB version is newer
          const mongoUpdated = new Date(mongoThread.updated_at);
          const localUpdated = new Date(localThread.updated_at);

          if (mongoUpdated > localUpdated) {
            // MongoDB version is newer - update local
            await this.runQuery(
              `UPDATE instagram_threads
               SET thread_id = ?, last_used = ?, updated_at = ?, synced_at = CURRENT_TIMESTAMP,
                   device_id = ?, is_deleted = ?
               WHERE username = ?`,
              [
                mongoThread.thread_id,
                mongoThread.last_used,
                mongoThread.updated_at,
                mongoThread.device_id || currentDeviceId,
                mongoThread.is_deleted ? 1 : 0,
                mongoThread.username
              ]
            );
            updatedLocal++;
            this.logger.info(`🔄 Updated local thread from MongoDB: @${mongoThread.username}`);
          } else if (localUpdated > mongoUpdated) {
            // Local version is newer - update MongoDB
            if (global.mongoDBService && global.mongoDBService.isConnected) {
              await global.mongoDBService.syncSingleThreadToMongo({
                username: localThread.username,
                thread_id: localThread.thread_id,
                last_used: localThread.last_used,
                updated_at: localThread.updated_at,
                device_id: localThread.device_id || currentDeviceId,
                is_deleted: Boolean(localThread.is_deleted) // Convert SQLite integer to boolean
              });

              // Update synced_at
              await this.runQuery(
                'UPDATE instagram_threads SET synced_at = CURRENT_TIMESTAMP WHERE username = ?',
                [localThread.username]
              );
              updatedMongo++;
              this.logger.info(`📤 Updated MongoDB thread from local: @${localThread.username}`);
            }
          }
        }
      }

      // Process local threads that don't exist in MongoDB
      for (const localThread of localThreads) {
        if (!mongoMap.has(localThread.username)) {
          // Thread exists locally but not in MongoDB - add to MongoDB
          if (global.mongoDBService && global.mongoDBService.isConnected) {
            await global.mongoDBService.syncSingleThreadToMongo({
              username: localThread.username,
              thread_id: localThread.thread_id,
              last_used: localThread.last_used,
              updated_at: localThread.updated_at,
              device_id: localThread.device_id || currentDeviceId,
              is_deleted: Boolean(localThread.is_deleted) // Convert SQLite integer to boolean
            });

            // Update synced_at
            await this.runQuery(
              'UPDATE instagram_threads SET synced_at = CURRENT_TIMESTAMP WHERE username = ?',
              [localThread.username]
            );
            addedToMongo++;
            this.logger.info(`📤 Added thread to MongoDB: @${localThread.username}`);
          }
        }
      }

      const result = {
        addedToLocal,
        addedToMongo,
        updatedLocal,
        updatedMongo,
        totalLocal: localThreads.length,
        totalMongo: mongoThreads.length
      };

      this.logger.info(`✅ Smart sync completed for threads: +${addedToLocal} local, +${addedToMongo} MongoDB, ~${updatedLocal + updatedMongo} updated`);
      return result;

    } catch (error) {
      this.logger.error('Failed to smart sync threads with MongoDB:', error);
      throw error;
    }
  }

  async cleanupOldInstagramThreads(olderThanDays = 30) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffISO = cutoffDate.toISOString();

      const result = await this.runQuery(
        'DELETE FROM instagram_threads WHERE last_used < ?',
        [cutoffISO]
      );

      if (result.changes > 0) {
        this.logger.info(`Cleaned up ${result.changes} old Instagram threads`);
      }

      return result.changes;
    } catch (error) {
      this.logger.error('Failed to cleanup old Instagram threads:', error);
      throw error;
    }
  }

  // Delete from printed_history table
  async deletePrintedHistoryRecord(historyId) {
    try {
      // Get record data before deletion for sync
      const record = await this.getQuery('SELECT * FROM printed_history WHERE id = ?', [historyId]);

      if (record) {
        // Check if updated_at column exists
        const tableInfo = await this.allQuery("PRAGMA table_info(printed_history)");
        const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

        // Mark as deleted instead of hard delete for sync purposes
        if (hasUpdatedAt) {
          await this.runQuery(
            'UPDATE printed_history SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [historyId]
          );
        } else {
          await this.runQuery(
            'UPDATE printed_history SET is_deleted = 1 WHERE id = ?',
            [historyId]
          );
        }

        // Auto-sync deletion to MongoDB
        await this.autoSyncPrintedHistoryToMongoDB('delete', {
          id: historyId,
          username: record.username,
          device_id: record.device_id || await this.getDeviceId()
        });

        this.logger.info(`Printed history record marked as deleted: ${historyId}`);
        return 1;
      }

      return 0;
    } catch (error) {
      this.logger.error('Failed to delete printed history record:', error);
      throw error;
    }
  }

  async deleteUserPrintedHistory(username) {
    try {
      // Get current device ID
      const currentDeviceId = await this.getDeviceId();

      // Check if updated_at column exists
      const tableInfo = await this.allQuery("PRAGMA table_info(printed_history)");
      const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

      // Mark ALL user records as deleted (including already deleted ones for consistency)
      let result;
      if (hasUpdatedAt) {
        result = await this.runQuery(
          'UPDATE printed_history SET is_deleted = 1, updated_at = CURRENT_TIMESTAMP WHERE username = ?',
          [username]
        );
      } else {
        result = await this.runQuery(
          'UPDATE printed_history SET is_deleted = 1 WHERE username = ?',
          [username]
        );
      }

      // Auto-sync deletion to MongoDB for each record
      if (result.changes > 0) {
        await this.autoSyncPrintedHistoryToMongoDB('delete', {
          username: username,
          device_id: currentDeviceId,
          bulk_delete: true
        });
      }

      this.logger.info(`Marked ${result.changes} printed history records as deleted for user: ${username}`);
      return result.changes;
    } catch (error) {
      this.logger.error('Failed to delete user printed history:', error);
      throw error;
    }
  }

  // Restore deleted printed history record
  async restorePrintedHistoryRecord(historyId) {
    try {
      // Get record data before restoration
      const record = await this.getQuery('SELECT * FROM printed_history WHERE id = ? AND is_deleted = 1', [historyId]);

      if (record) {
        // Check if updated_at column exists
        const tableInfo = await this.allQuery("PRAGMA table_info(printed_history)");
        const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

        // Restore record (mark as not deleted)
        if (hasUpdatedAt) {
          await this.runQuery(
            'UPDATE printed_history SET is_deleted = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
            [historyId]
          );
        } else {
          await this.runQuery(
            'UPDATE printed_history SET is_deleted = 0 WHERE id = ?',
            [historyId]
          );
        }

        // Auto-sync restoration to MongoDB
        await this.autoSyncPrintedHistoryToMongoDB('restore', {
          id: historyId,
          username: record.username,
          device_id: record.device_id || await this.getDeviceId()
        });

        this.logger.info(`Restored printed history record: ${historyId}`);
        return 1;
      }

      return 0;
    } catch (error) {
      this.logger.error('Failed to restore printed history record:', error);
      throw error;
    }
  }

  // Restore all deleted printed history records from a user
  async restoreUserPrintedHistory(username) {
    try {
      // Get current device ID
      const currentDeviceId = await this.getDeviceId();

      // Check if updated_at column exists
      const tableInfo = await this.allQuery("PRAGMA table_info(printed_history)");
      const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

      // Restore all user records (mark as not deleted)
      let result;
      if (hasUpdatedAt) {
        result = await this.runQuery(
          'UPDATE printed_history SET is_deleted = 0, updated_at = CURRENT_TIMESTAMP WHERE username = ? AND is_deleted = 1',
          [username]
        );
      } else {
        result = await this.runQuery(
          'UPDATE printed_history SET is_deleted = 0 WHERE username = ? AND is_deleted = 1',
          [username]
        );
      }

      // Auto-sync restoration to MongoDB for each record
      if (result.changes > 0) {
        await this.autoSyncPrintedHistoryToMongoDB('restore', {
          username: username,
          device_id: currentDeviceId,
          bulk_restore: true
        });
      }

      this.logger.info(`Restored ${result.changes} printed history records for user: ${username}`);
      return result.changes;
    } catch (error) {
      this.logger.error('Failed to restore user printed history:', error);
      throw error;
    }
  }

  // Get printed_history for sync (including sync metadata)
  async getPrintedHistoryForSync() {
    try {
      const query = `
        SELECT id, comment_id, username, comment_text, print_type, printed_at, created_at,
               synced_at, device_id, is_deleted
        FROM printed_history
        ORDER BY created_at ASC
      `;

      const records = await this.allQuery(query);
      return records || [];
    } catch (error) {
      this.logger.error('Failed to get printed history for sync:', error);
      throw error;
    }
  }



  // OLD Smart bidirectional sync for printed_history (keeping for compatibility)
  async smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory) {
    try {
      const localPrintedHistory = await this.getPrintedHistoryForSync();
      const currentDeviceId = await this.getDeviceId();

      this.logger.info(`Starting printed history smart sync: ${localPrintedHistory.length} local, ${mongoPrintedHistory.length} from MongoDB`);

      let addedToLocal = 0;
      let updatedLocal = 0;
      let addedToMongo = 0;
      let updatedMongo = 0;

      // Create maps for efficient lookup (using comment_id + username as key for uniqueness)
      const localMap = new Map(localPrintedHistory.map(record => [`${record.comment_id}_${record.username}`, record]));
      const mongoMap = new Map(mongoPrintedHistory.map(record => [`${record.comment_id}_${record.username}`, record]));

      // Process MongoDB records
      for (const mongoRecord of mongoPrintedHistory) {
        const key = `${mongoRecord.comment_id}_${mongoRecord.username}`;
        const localRecord = localMap.get(key);

        if (!localRecord) {
          // Record exists in MongoDB but not locally - add to local if not deleted
          if (!mongoRecord.is_deleted) {
            await this.runQuery(
              `INSERT INTO printed_history
               (comment_id, username, comment_text, print_type, printed_at, created_at, synced_at, device_id, is_deleted)
               VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 0)`,
              [
                mongoRecord.comment_id,
                mongoRecord.username,
                mongoRecord.comment_text,
                mongoRecord.print_type,
                mongoRecord.printed_at,
                mongoRecord.created_at,
                mongoRecord.device_id
              ]
            );
            addedToLocal++;
            this.logger.info(`📥 Added printed history from MongoDB: ${mongoRecord.username} - ${mongoRecord.comment_id}`);
          }
        } else {
          // Record exists in both - check if sync is needed

          // Create content hash to detect real changes (normalize boolean values)
          const localDeleted = Boolean(localRecord.is_deleted); // Convert SQLite integer to boolean
          const mongoDeleted = Boolean(mongoRecord.is_deleted); // Ensure MongoDB boolean
          const localHash = `${localRecord.comment_text}_${localRecord.print_type}_${localDeleted}`;
          const mongoHash = `${mongoRecord.comment_text}_${mongoRecord.print_type}_${mongoDeleted}`;

          // Debug first few records (only if needed)
          if (updatedLocal + updatedMongo < 2 && localHash !== mongoHash) {
            this.logger.info(`🔍 Content differs for ${localRecord.username}: Local(${localDeleted}) vs Mongo(${mongoDeleted})`);
          }

          // If content is identical, just update synced_at and skip
          if (localHash === mongoHash) {
            await this.runQuery(
              'UPDATE printed_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
              [localRecord.id]
            );
            // Only log first few to avoid spam
            if (updatedLocal + updatedMongo < 3) {
              this.logger.info(`✓ Skipped identical record: ${localRecord.username} - ${localRecord.comment_id}`);
            }
            continue;
          }

          // Normalize timestamps for comparison (remove milliseconds)
          const localUpdated = new Date(localRecord.created_at || localRecord.printed_at);
          const mongoUpdated = new Date(mongoRecord.created_at || mongoRecord.printed_at);

          // Round to seconds to avoid precision issues
          localUpdated.setMilliseconds(0);
          mongoUpdated.setMilliseconds(0);

          const timeDiff = Math.abs(localUpdated.getTime() - mongoUpdated.getTime());

          // Only sync if timestamps differ by more than 1 second
          if (timeDiff > 1000) {
            if (mongoUpdated > localUpdated) {
              // MongoDB version is newer - update local
              await this.runQuery(
                `UPDATE printed_history
                 SET comment_text = ?, print_type = ?, printed_at = ?, is_deleted = ?, synced_at = CURRENT_TIMESTAMP
                 WHERE id = ?`,
                [
                  mongoRecord.comment_text,
                  mongoRecord.print_type,
                  mongoRecord.printed_at,
                  mongoRecord.is_deleted ? 1 : 0,
                  localRecord.id
                ]
              );
              updatedLocal++;
              this.logger.info(`🔄 Updated local printed history from MongoDB: ${mongoRecord.username} - ${mongoRecord.comment_id}`);
            } else if (localUpdated > mongoUpdated) {
              // Local version is newer - update MongoDB
              if (global.mongoDBService && global.mongoDBService.isConnected) {
                await global.mongoDBService.syncSinglePrintedHistoryToMongo({
                  id: localRecord.id,
                  comment_id: localRecord.comment_id,
                  username: localRecord.username,
                  comment_text: localRecord.comment_text,
                  print_type: localRecord.print_type,
                  printed_at: localRecord.printed_at,
                  created_at: localRecord.created_at,
                  device_id: localRecord.device_id || currentDeviceId,
                  is_deleted: Boolean(localRecord.is_deleted) // Convert SQLite integer to boolean
                });

                // Update synced_at
                await this.runQuery(
                  'UPDATE printed_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
                  [localRecord.id]
                );
                updatedMongo++;
                this.logger.info(`🔄 Updated MongoDB printed history from local: ${localRecord.username} - ${localRecord.comment_id}`);
              }
            }
          } else {
            // Timestamps are essentially the same, just update synced_at to mark as synced
            await this.runQuery(
              'UPDATE printed_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
              [localRecord.id]
            );
          }
        }
      }

      // Process local records that don't exist in MongoDB
      for (const localRecord of localPrintedHistory) {
        const key = `${localRecord.comment_id}_${localRecord.username}`;
        if (!mongoMap.has(key)) {
          // Record exists locally but not in MongoDB - add to MongoDB
          if (global.mongoDBService && global.mongoDBService.isConnected) {
            await global.mongoDBService.syncSinglePrintedHistoryToMongo({
              id: localRecord.id,
              comment_id: localRecord.comment_id,
              username: localRecord.username,
              comment_text: localRecord.comment_text,
              print_type: localRecord.print_type,
              printed_at: localRecord.printed_at,
              created_at: localRecord.created_at,
              device_id: localRecord.device_id || currentDeviceId,
              is_deleted: Boolean(localRecord.is_deleted) // Convert SQLite integer to boolean
            });

            // Update synced_at
            await this.runQuery(
              'UPDATE printed_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
              [localRecord.id]
            );
            addedToMongo++;
            this.logger.info(`📤 Added printed history to MongoDB: ${localRecord.username} - ${localRecord.comment_id}`);
          }
        }
      }

      const totalChanges = addedToLocal + updatedLocal + addedToMongo + updatedMongo;

      if (totalChanges > 0) {
        this.logger.info(`Printed history smart sync completed: +${addedToLocal} local, +${addedToMongo} MongoDB, ~${updatedLocal + updatedMongo} updated`);
      } else {
        this.logger.info(`Printed history smart sync: No changes needed (all records identical)`);
      }

      return {
        addedToLocal,
        updatedLocal,
        addedToMongo,
        updatedMongo,
        totalChanges
      };
    } catch (error) {
      this.logger.error('Failed to smart sync printed history with MongoDB:', error);
      throw error;
    }
  }

  // Cleanup old printed_history records (hard delete after retention period)
  async cleanupOldPrintedHistory(olderThanDays = 90) {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
      const cutoffISO = cutoffDate.toISOString();

      // Get records to be hard deleted (already soft deleted and old enough)
      const recordsToDelete = await this.allQuery(
        'SELECT * FROM printed_history WHERE is_deleted = 1 AND created_at < ?',
        [cutoffISO]
      );

      // Hard delete old soft-deleted records
      const result = await this.runQuery(
        'DELETE FROM printed_history WHERE is_deleted = 1 AND created_at < ?',
        [cutoffISO]
      );

      if (result.changes > 0) {
        this.logger.info(`🗑️ Hard deleted ${result.changes} old printed history records older than ${olderThanDays} days`);

        // Emit cleanup event for monitoring
        if (global.io) {
          global.io.emit('printed-history-cleanup-completed', {
            deletedCount: result.changes,
            cleanupDays: olderThanDays,
            timestamp: new Date().toISOString()
          });
        }
      }

      return result.changes;
    } catch (error) {
      this.logger.error('Failed to cleanup old printed history:', error);
      throw error;
    }
  }

  // Get send_once_history for sync (including sync metadata)
  async getSendOnceHistoryForSync() {
    try {
      // Check if updated_at column exists
      const tableInfo = await this.allQuery("PRAGMA table_info(send_once_history)");
      const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

      let query;
      if (hasUpdatedAt) {
        query = `
          SELECT id, username, template_name, customer_type, template_type, sent_at, created_at,
                 synced_at, device_id, is_deleted, updated_at
          FROM send_once_history
          ORDER BY created_at ASC
        `;
      } else {
        query = `
          SELECT id, username, template_name, customer_type, template_type, sent_at, created_at,
                 synced_at, device_id, is_deleted, created_at as updated_at
          FROM send_once_history
          ORDER BY created_at ASC
        `;
      }

      const records = await this.allQuery(query);
      return records || [];
    } catch (error) {
      this.logger.error('Failed to get send_once history for sync:', error);
      throw error;
    }
  }

  // Smart bidirectional sync for send_once_history
  async smartSyncSendOnceHistoryWithMongoDB(mongoSendOnceHistory) {
    try {
      const localSendOnceHistory = await this.getSendOnceHistoryForSync();
      const currentDeviceId = await this.getDeviceId();

      this.logger.info(`Starting send_once history smart sync: ${localSendOnceHistory.length} local, ${mongoSendOnceHistory.length} from MongoDB`);

      let addedToLocal = 0;
      let updatedLocal = 0;
      let addedToMongo = 0;
      let updatedMongo = 0;

      // Create maps for efficient lookup (using username + template_name + customer_type + template_type as key)
      const localMap = new Map(localSendOnceHistory.map(record =>
        [`${record.username}_${record.template_name}_${record.customer_type}_${record.template_type}`, record]
      ));
      const mongoMap = new Map(mongoSendOnceHistory.map(record =>
        [`${record.username}_${record.template_name}_${record.customer_type}_${record.template_type}`, record]
      ));

      // Process MongoDB records
      for (const mongoRecord of mongoSendOnceHistory) {
        const key = `${mongoRecord.username}_${mongoRecord.template_name}_${mongoRecord.customer_type}_${mongoRecord.template_type}`;
        const localRecord = localMap.get(key);

        if (!localRecord) {
          // Record exists in MongoDB but not locally - add to local if not deleted
          if (!mongoRecord.is_deleted) {
            // Check if updated_at column exists
            const tableInfo = await this.allQuery("PRAGMA table_info(send_once_history)");
            const hasUpdatedAt = tableInfo.some(col => col.name === 'updated_at');

            if (hasUpdatedAt) {
              await this.runQuery(
                `INSERT INTO send_once_history
                 (username, template_name, customer_type, template_type, sent_at, created_at, synced_at, device_id, is_deleted, updated_at)
                 VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 0, ?)`,
                [
                  mongoRecord.username,
                  mongoRecord.template_name,
                  mongoRecord.customer_type,
                  mongoRecord.template_type,
                  mongoRecord.sent_at,
                  mongoRecord.created_at,
                  mongoRecord.device_id,
                  mongoRecord.updated_at || mongoRecord.created_at
                ]
              );
            } else {
              await this.runQuery(
                `INSERT INTO send_once_history
                 (username, template_name, customer_type, template_type, sent_at, created_at, synced_at, device_id, is_deleted)
                 VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, ?, 0)`,
                [
                  mongoRecord.username,
                  mongoRecord.template_name,
                  mongoRecord.customer_type,
                  mongoRecord.template_type,
                  mongoRecord.sent_at,
                  mongoRecord.created_at,
                  mongoRecord.device_id
                ]
              );
            }
            addedToLocal++;
            this.logger.info(`📥 Added send_once history from MongoDB: ${mongoRecord.username} - ${mongoRecord.template_name}`);
          }
        } else {
          // Record exists in both - check if sync is needed

          // Create content hash to detect real changes (normalize boolean values)
          const localDeleted = Boolean(localRecord.is_deleted); // Convert SQLite integer to boolean
          const mongoDeleted = Boolean(mongoRecord.is_deleted); // Ensure MongoDB boolean
          const localHash = `${localRecord.username}_${localRecord.template_name}_${localRecord.customer_type}_${localRecord.template_type}_${localDeleted}`;
          const mongoHash = `${mongoRecord.username}_${mongoRecord.template_name}_${mongoRecord.customer_type}_${mongoRecord.template_type}_${mongoDeleted}`;

          // If content is identical, just update synced_at and skip
          if (localHash === mongoHash) {
            await this.runQuery(
              'UPDATE send_once_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
              [localRecord.id]
            );
            continue;
          }

          // Normalize timestamps for comparison (remove milliseconds)
          const localUpdated = new Date(localRecord.updated_at || localRecord.created_at);
          const mongoUpdated = new Date(mongoRecord.updated_at || mongoRecord.created_at);

          // Round to seconds to avoid precision issues
          localUpdated.setMilliseconds(0);
          mongoUpdated.setMilliseconds(0);

          const timeDiff = Math.abs(localUpdated.getTime() - mongoUpdated.getTime());

          // Only sync if timestamps differ by more than 1 second
          if (timeDiff > 1000) {
            if (mongoUpdated > localUpdated) {
              // MongoDB version is newer - update local
              await this.runQuery(
                `UPDATE send_once_history
                 SET sent_at = ?, is_deleted = ?, synced_at = CURRENT_TIMESTAMP, updated_at = ?
                 WHERE id = ?`,
                [
                  mongoRecord.sent_at,
                  mongoRecord.is_deleted ? 1 : 0,
                  mongoRecord.updated_at || mongoRecord.created_at,
                  localRecord.id
                ]
              );
              updatedLocal++;
              this.logger.info(`🔄 Updated local send_once history from MongoDB: ${mongoRecord.username} - ${mongoRecord.template_name}`);
            } else if (localUpdated > mongoUpdated) {
              // Local version is newer - update MongoDB
              if (global.mongoDBService && global.mongoDBService.isConnected) {
                await global.mongoDBService.syncSingleSendOnceHistoryToMongo({
                  id: localRecord.id,
                  username: localRecord.username,
                  template_name: localRecord.template_name,
                  customer_type: localRecord.customer_type,
                  template_type: localRecord.template_type,
                  sent_at: localRecord.sent_at,
                  created_at: localRecord.created_at,
                  device_id: localRecord.device_id || currentDeviceId,
                  is_deleted: Boolean(localRecord.is_deleted), // Convert SQLite integer to boolean
                  updated_at: localRecord.updated_at
                });

                // Update synced_at
                await this.runQuery(
                  'UPDATE send_once_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
                  [localRecord.id]
                );
                updatedMongo++;
                this.logger.info(`🔄 Updated MongoDB send_once history from local: ${localRecord.username} - ${localRecord.template_name}`);
              }
            }
          } else {
            // Timestamps are essentially the same, just update synced_at to mark as synced
            await this.runQuery(
              'UPDATE send_once_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
              [localRecord.id]
            );
          }
        }
      }

      // Process local records that don't exist in MongoDB
      for (const localRecord of localSendOnceHistory) {
        const key = `${localRecord.username}_${localRecord.template_name}_${localRecord.customer_type}_${localRecord.template_type}`;
        if (!mongoMap.has(key)) {
          // Record exists locally but not in MongoDB - add to MongoDB
          if (global.mongoDBService && global.mongoDBService.isConnected) {
            await global.mongoDBService.syncSingleSendOnceHistoryToMongo({
              id: localRecord.id,
              username: localRecord.username,
              template_name: localRecord.template_name,
              customer_type: localRecord.customer_type,
              template_type: localRecord.template_type,
              sent_at: localRecord.sent_at,
              created_at: localRecord.created_at,
              device_id: localRecord.device_id || currentDeviceId,
              is_deleted: Boolean(localRecord.is_deleted), // Convert SQLite integer to boolean
              updated_at: localRecord.updated_at
            });

            // Update synced_at
            await this.runQuery(
              'UPDATE send_once_history SET synced_at = CURRENT_TIMESTAMP WHERE id = ?',
              [localRecord.id]
            );
            addedToMongo++;
            this.logger.info(`📤 Added send_once history to MongoDB: ${localRecord.username} - ${localRecord.template_name}`);
          }
        }
      }

      const totalChanges = addedToLocal + updatedLocal + addedToMongo + updatedMongo;

      if (totalChanges > 0) {
        this.logger.info(`Send_once history smart sync completed: +${addedToLocal} local, +${addedToMongo} MongoDB, ~${updatedLocal + updatedMongo} updated`);
      }

      return {
        addedToLocal,
        updatedLocal,
        addedToMongo,
        updatedMongo,
        totalChanges
      };
    } catch (error) {
      this.logger.error('Failed to smart sync send_once history with MongoDB:', error);
      throw error;
    }
  }

  // Settings methods for MongoDB integration
  async getSetting(key) {
    try {
      const result = await this.getQuery('SELECT value FROM settings WHERE key = ?', [key]);
      return result ? result.value : null;
    } catch (error) {
      this.logger.error('Failed to get setting:', error);
      return null;
    }
  }

  async saveSetting(key, value, type = 'string', description = '') {
    try {
      await this.runQuery(
        'INSERT OR REPLACE INTO settings (key, value, type, description, updated_at) VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)',
        [key, value, type, description]
      );
      this.logger.info(`Setting saved: ${key}`);
    } catch (error) {
      this.logger.error('Failed to save setting:', error);
      throw error;
    }
  }

  // Price Mappings methods for template price conversion
  async getAllPriceMappings() {
    try {
      const mappings = await this.allQuery(
        'SELECT * FROM price_mappings ORDER BY prefix ASC'
      );
      return mappings;
    } catch (error) {
      this.logger.error('Failed to get price mappings:', error);
      return [];
    }
  }

  async getActivePriceMappings() {
    try {
      const mappings = await this.allQuery(
        'SELECT * FROM price_mappings WHERE is_active = TRUE ORDER BY prefix ASC'
      );
      return mappings;
    } catch (error) {
      this.logger.error('Failed to get active price mappings:', error);
      return [];
    }
  }

  async savePriceMapping(mappingData) {
    try {
      const { id, prefix, price, description, is_active } = mappingData;

      if (id) {
        // Update existing mapping
        await this.runQuery(
          `UPDATE price_mappings
           SET prefix = ?, price = ?, description = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
           WHERE id = ?`,
          [prefix, price, description || '', is_active, id]
        );
      } else {
        // Insert new mapping
        await this.runQuery(
          `INSERT INTO price_mappings (prefix, price, description, is_active)
           VALUES (?, ?, ?, ?)`,
          [prefix, price, description || '', is_active]
        );
      }

      this.logger.info(`Price mapping saved: ${prefix} → ${price}`);
    } catch (error) {
      this.logger.error('Failed to save price mapping:', error);
      throw error;
    }
  }

  async deletePriceMapping(id) {
    try {
      const result = await this.runQuery(
        'DELETE FROM price_mappings WHERE id = ?',
        [id]
      );

      this.logger.info(`Price mapping deleted: ${id}`);
      return result.changes;
    } catch (error) {
      this.logger.error('Failed to delete price mapping:', error);
      throw error;
    }
  }

  async getPriceMappingByPrefix(prefix) {
    try {
      const mapping = await this.getQuery(
        'SELECT * FROM price_mappings WHERE prefix = ? AND is_active = TRUE',
        [prefix.toLowerCase()]
      );
      return mapping;
    } catch (error) {
      this.logger.error('Failed to get price mapping by prefix:', error);
      return null;
    }
  }
}

module.exports = Database;
