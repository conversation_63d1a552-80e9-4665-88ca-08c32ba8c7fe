import React, { useState, useEffect, useRef } from 'react';
import { Printer, Eye, Save, RotateCcw, Type, Settings, FileText, History } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import toast from 'react-hot-toast';

const PrintSettings = () => {
  const { socket, isConnected } = useSocket();
  const [availablePrinters, setAvailablePrinters] = useState([]);
  const [activeTab, setActiveTab] = useState('printer'); // printer, format-single, format-history

  const [settings, setSettings] = useState({
    // Printer Settings
    printer: {
      selectedPrinter: '',
      connectionType: 'system', // system, usb, network, file
      networkSettings: {
        ipAddress: '',
        port: 9100
      },
      usbSettings: {
        vendorId: '',
        productId: ''
      },
      fileSettings: {
        outputPath: '',
        fileNameFormat: 'timestamp' // timestamp, username, sequential
      },
      printMode: 'bitmap', // bitmap, text
      cutType: 'partial' // full, partial, none
    },

    // Single Comment Format
    singleComment: {
      username: {
        show: true,
        fontSize: 32,
        fontWeight: 'bold',
        fontStyle: 'normal',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'left',
        marginBottom: 6
      },
      timestamp: {
        show: true,
        fontSize: 20,
        fontWeight: 'normal',
        fontStyle: 'normal',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'left',
        marginBottom: 8
      },
      content: {
        show: true,
        fontSize: 28,
        fontWeight: 'normal',
        fontStyle: 'normal',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'left',
        marginBottom: 10
      },
      padding: 12,
      lineSpacing: 4,
      showHeader: false,
      headerText: '',
      showFooter: false,
      footerText: ''
    },

    // User History Format
    userHistory: {
      header: {
        show: true,
        text: 'LỊCH SỬ BÌNH LUẬN',
        fontSize: 16,
        fontWeight: 'bold',
        fontStyle: 'normal',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'center',
        marginBottom: 8
      },
      username: {
        show: true,
        fontSize: 18,
        fontWeight: 'bold',
        fontStyle: 'normal',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'left',
        marginBottom: 6
      },
      commentItem: {
        show: true,
        fontSize: 14,
        fontWeight: 'normal',
        fontStyle: 'normal',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'left',
        marginBottom: 4
      },
      timestamp: {
        show: true,
        fontSize: 10,
        fontWeight: 'normal',
        fontStyle: 'normal',
        fontFamily: 'Arial, sans-serif',
        textAlign: 'left',
        marginBottom: 6
      },
      separator: {
        show: true,
        style: '---'
      },
      padding: 8,
      lineSpacing: 1,
      maxCommentsPerPage: 10,
      showSummary: true
    }
  });

  const [previewData, setPreviewData] = useState(() => {
    const now = new Date();
    const vietnamTime = now.toLocaleString('vi-VN', {
      timeZone: 'Asia/Ho_Chi_Minh',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    return {
      single: {
        username: 'Ha Tram Nguyen',
        timestamp: vietnamTime,
        text: 'Chào shop, mình muốn đặt 2 áo size M màu đen. Có sẵn không ạ?'
      },
      history: {
        username: 'Ha Tram Nguyen',
        comments: [
          { timestamp: vietnamTime, text: 'Chào shop, mình muốn đặt 2 áo size M màu đen.' },
          {
            timestamp: new Date(now.getTime() + 2 * 60000).toLocaleString('vi-VN', {
              timeZone: 'Asia/Ho_Chi_Minh',
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            }), text: 'Có thêm màu trắng không ạ?'
          },
          {
            timestamp: new Date(now.getTime() + 4 * 60000).toLocaleString('vi-VN', {
              timeZone: 'Asia/Ho_Chi_Minh',
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            }), text: 'Giá bao nhiêu vậy shop?'
          }
        ],
        totalComments: 3
      }
    };
  });

  const fontFamilyOptions = [
    { value: 'Arial, sans-serif', label: 'Arial' },
    { value: 'Times New Roman, serif', label: 'Times New Roman' },
    { value: 'Verdana, sans-serif', label: 'Verdana' },
    { value: 'Courier New, monospace', label: 'Courier New' }
  ];

  const fontSizeOptions = [
    { value: 10, label: '10px - Rất nhỏ' },
    { value: 12, label: '12px - Nhỏ' },
    { value: 14, label: '14px - Vừa' },
    { value: 16, label: '16px - Trung bình' },
    { value: 18, label: '18px - Lớn' },
    { value: 20, label: '20px - Rất lớn' },
    { value: 24, label: '24px - Cực lớn' },
    { value: 28, label: '28px - Siêu lớn' },
    { value: 32, label: '32px - Khổng lồ' },
    { value: 36, label: '36px - Cực khổng lồ' },
    { value: 40, label: '40px - Tối đa' }
  ];

  useEffect(() => {
    loadPrintSettings();
    loadAvailablePrinters();
  }, []);

  const loadPrintSettings = async () => {
    try {
      const response = await fetch('/api/print-settings');
      const data = await response.json();
      if (data.success && data.settings) {
        setSettings(prev => {
          const newSettings = { ...prev, ...data.settings };

          // Ensure networkSettings exists
          if (!newSettings.printer.networkSettings) {
            newSettings.printer.networkSettings = {
              ipAddress: '',
              port: 9100
            };
          }

          // Ensure fileSettings exists
          if (!newSettings.printer.fileSettings) {
            newSettings.printer.fileSettings = {
              outputPath: '',
              fileNameFormat: 'timestamp'
            };
          }

          console.log('Loaded settings with ensured structure:', newSettings);
          return newSettings;
        });
      }
    } catch (error) {
      console.error('Failed to load print settings:', error);
    }
  };

  const loadAvailablePrinters = async () => {
    try {
      const response = await fetch('/api/printers');
      const data = await response.json();
      if (data.success) {
        setAvailablePrinters(data.printers || []);
      }
    } catch (error) {
      console.error('Failed to load printers:', error);
    }
  };

  const savePrintSettings = async () => {
    try {
      const response = await fetch('/api/print-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings }),
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Cài đặt in đã được lưu!');
      } else {
        throw new Error(data.error || 'Failed to save settings');
      }
    } catch (error) {
      toast.error('Lỗi khi lưu cài đặt: ' + error.message);
    }
  };

  const testNetworkConnection = async () => {
    try {
      const response = await fetch('/api/test-network-connection', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          connectionType: 'network',
          networkSettings: settings.printer.networkSettings
        }),
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Kết nối thành công! ' + (data.message || ''));
      } else {
        toast.error('Kết nối thất bại: ' + (data.error || 'Unknown error'));
      }
    } catch (error) {
      toast.error('Lỗi khi test kết nối: ' + error.message);
    }
  };

  const resetToDefault = () => {
    setSettings({
      printer: {
        selectedPrinter: '',
        connectionType: 'system',
        networkSettings: {
          ipAddress: '',
          port: 9100
        },
        usbSettings: {
          vendorId: '',
          productId: ''
        },
        fileSettings: {
          outputPath: '',
          fileNameFormat: 'timestamp'
        },
        printMode: 'bitmap',
        cutType: 'partial'
      },
      singleComment: {
        username: {
          show: true,
          fontSize: 32, fontWeight: 'bold', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 6
        },
        timestamp: {
          show: true,
          fontSize: 20, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 8
        },
        content: {
          show: true,
          fontSize: 28, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 10
        },
        padding: 12, lineSpacing: 4, showHeader: false, headerText: '', showFooter: false, footerText: ''
      },
      userHistory: {
        header: {
          show: true, text: 'LỊCH SỬ BÌNH LUẬN',
          fontSize: 16, fontWeight: 'bold', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'center', marginBottom: 8
        },
        username: {
          show: true,
          fontSize: 18, fontWeight: 'bold', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 6
        },
        commentItem: {
          show: true,
          fontSize: 14, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 4
        },
        timestamp: {
          show: true,
          fontSize: 10, fontWeight: 'normal', fontStyle: 'normal',
          fontFamily: 'Arial, sans-serif', textAlign: 'left', marginBottom: 6
        },
        separator: { show: true, style: '---' },
        padding: 8, lineSpacing: 1, maxCommentsPerPage: 10, showSummary: true
      }
    });
    toast.info('Đã khôi phục cài đặt mặc định');
  };

  const updateSetting = (category, element, property, value) => {
    console.log('updateSetting called:', { category, element, property, value });

    setSettings(prev => {
      const newSettings = { ...prev };

      // Handle nested object updates properly
      if (property === null) {
        // Direct assignment to element
        newSettings[category] = {
          ...newSettings[category],
          [element]: value
        };
      } else {
        // Nested property update
        newSettings[category] = {
          ...newSettings[category],
          [element]: {
            ...newSettings[category][element],
            [property]: value
          }
        };
      }

      // Ensure fileSettings exists when connectionType is file
      if (category === 'printer' && element === 'connectionType' && value === 'file') {
        if (!newSettings.printer.fileSettings) {
          newSettings.printer.fileSettings = {
            outputPath: '',
            fileNameFormat: 'timestamp'
          };
        }
      }

      // Ensure networkSettings exists when connectionType is network
      if (category === 'printer' && element === 'connectionType' && value === 'network') {
        if (!newSettings.printer.networkSettings) {
          newSettings.printer.networkSettings = {
            ipAddress: '',
            port: 9100
          };
        }
      }

      console.log('New settings:', newSettings);
      return newSettings;
    });
  };

  const handleTestPrint = async () => {
    try {
      // Save current settings first
      await savePrintSettings();

      // Call test print API
      const response = await fetch('/api/test-print', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();
      if (data.success) {
        if (settings.printer.connectionType === 'file') {
          toast.success(`In thử nghiệm thành công! File đã lưu: ${data.result.filename}`);
        } else {
          toast.success('In thử nghiệm thành công!');
        }
      } else {
        throw new Error(data.error || 'Test print failed');
      }
    } catch (error) {
      console.error('Test print failed:', error);
      toast.error('Lỗi khi in thử nghiệm: ' + error.message);
    }
  };

  const renderPreview = (type) => {
    const paperWidth = 80; // Fixed 80mm paper width
    const pixelWidth = (paperWidth / 25.4) * 203; // Convert mm to pixels at 203 DPI

    if (type === 'single') {
      const format = settings.singleComment;
      return (
        <div
          className="bg-white border border-gray-300 mx-auto"
          style={{
            width: `${pixelWidth}px`,
            padding: `${format.padding}px`,
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            lineHeight: `${1 + (format.lineSpacing / 10)}`
          }}
        >
          {format.showHeader && format.headerText && (
            <div className="text-center mb-2 text-xs border-b pb-2">
              {format.headerText}
            </div>
          )}

          {format.username.show && (
            <div style={{
              fontSize: `${format.username.fontSize}px`,
              fontWeight: format.username.fontWeight,
              fontStyle: format.username.fontStyle,
              fontFamily: format.username.fontFamily,
              textAlign: format.username.textAlign,
              marginBottom: `${format.username.marginBottom}px`
            }}>
              @{previewData.single.username}
            </div>
          )}

          {format.timestamp.show && (
            <div style={{
              fontSize: `${format.timestamp.fontSize}px`,
              fontWeight: format.timestamp.fontWeight,
              fontStyle: format.timestamp.fontStyle,
              fontFamily: format.timestamp.fontFamily,
              textAlign: format.timestamp.textAlign,
              marginBottom: `${format.timestamp.marginBottom}px`,
              color: '#666'
            }}>
              {previewData.single.timestamp}
            </div>
          )}

          {format.content.show && (
            <div style={{
              fontSize: `${format.content.fontSize}px`,
              fontWeight: format.content.fontWeight,
              fontStyle: format.content.fontStyle,
              fontFamily: format.content.fontFamily,
              textAlign: format.content.textAlign,
              marginBottom: `${format.content.marginBottom}px`
            }}>
              {previewData.single.text}
            </div>
          )}

          {format.showFooter && format.footerText && (
            <div className="text-center mt-2 text-xs border-t pt-2">
              {format.footerText}
            </div>
          )}
        </div>
      );
    } else {
      const format = settings.userHistory;
      return (
        <div
          className="bg-white border border-gray-300 mx-auto"
          style={{
            width: `${pixelWidth}px`,
            padding: `${format.padding}px`,
            fontFamily: 'Arial, sans-serif',
            fontSize: '14px',
            lineHeight: `${1 + (format.lineSpacing / 10)}`
          }}
        >
          {format.header.show && (
            <div style={{
              fontSize: `${format.header.fontSize}px`,
              fontWeight: format.header.fontWeight,
              fontStyle: format.header.fontStyle,
              fontFamily: format.header.fontFamily,
              textAlign: format.header.textAlign,
              marginBottom: `${format.header.marginBottom}px`
            }}>
              {format.header.text}
            </div>
          )}

          {format.username.show && (
            <div style={{
              fontSize: `${format.username.fontSize}px`,
              fontWeight: format.username.fontWeight,
              fontStyle: format.username.fontStyle,
              fontFamily: format.username.fontFamily,
              textAlign: format.username.textAlign,
              marginBottom: `${format.username.marginBottom}px`
            }}>
              @{previewData.history.username}
            </div>
          )}

          {format.separator.show && (
            <div className="text-center mb-2" style={{ fontSize: '12px' }}>
              {format.separator.style}
            </div>
          )}

          {previewData.history.comments.slice(0, format.maxCommentsPerPage).map((comment, index) => (
            <div key={index} className="mb-2">
              {format.timestamp.show && (
                <div style={{
                  fontSize: `${format.timestamp.fontSize}px`,
                  fontWeight: format.timestamp.fontWeight,
                  fontStyle: format.timestamp.fontStyle,
                  fontFamily: format.timestamp.fontFamily,
                  textAlign: format.timestamp.textAlign,
                  marginBottom: `${format.timestamp.marginBottom}px`,
                  color: '#666'
                }}>
                  {comment.timestamp}
                </div>
              )}

              {format.commentItem.show && (
                <div style={{
                  fontSize: `${format.commentItem.fontSize}px`,
                  fontWeight: format.commentItem.fontWeight,
                  fontStyle: format.commentItem.fontStyle,
                  fontFamily: format.commentItem.fontFamily,
                  textAlign: format.commentItem.textAlign,
                  marginBottom: `${format.commentItem.marginBottom}px`
                }}>
                  {comment.text}
                </div>
              )}

              {index < previewData.history.comments.length - 1 && format.separator.show && (
                <div className="text-center my-1" style={{ fontSize: '10px', color: '#ccc' }}>
                  {format.separator.style}
                </div>
              )}
            </div>
          ))}

          {format.showSummary && (
            <div className="text-center mt-4 pt-2 border-t" style={{ fontSize: '12px', color: '#666' }}>
              Tổng: {previewData.history.totalComments} bình luận
            </div>
          )}
        </div>
      );
    }
  };

  const tabs = [
    { id: 'printer', name: 'Máy in', icon: Printer },
    { id: 'format-single', name: 'In comment đơn', icon: FileText },
    { id: 'format-history', name: 'In lịch sử user', icon: History }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Printer className="h-6 w-6 text-blue-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Cài đặt In</h3>
            <p className="text-sm text-gray-500">Cấu hình máy in và định dạng bill</p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button onClick={resetToDefault} className="btn-secondary">
            <RotateCcw className="h-4 w-4 mr-2" />
            Khôi phục mặc định
          </button>

          <button onClick={savePrintSettings} disabled={!isConnected} className="btn-primary">
            <Save className="h-4 w-4 mr-2" />
            Lưu cài đặt
          </button>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'printer' && (
        <div className="space-y-6">
          {/* Connection Type Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Loại kết nối
            </label>
            <select
              value={settings.printer.connectionType}
              onChange={(e) => {
                console.log('Connection type changed to:', e.target.value);
                updateSetting('printer', 'connectionType', null, e.target.value);
              }}
              className="input max-w-xs"
            >
              <option value="system">Máy in hệ thống</option>
              <option value="usb">USB trực tiếp</option>
              <option value="network">Mạng (TCP/IP)</option>
              <option value="file">In vào file text (Test)</option>
            </select>
          </div>

          {/* System Printer Selection */}
          {settings.printer.connectionType === 'system' && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chọn máy in
              </label>
              <select
                value={settings.printer.selectedPrinter}
                onChange={(e) => updateSetting('printer', 'selectedPrinter', null, e.target.value)}
                className="input"
              >
                <option value="">Chọn máy in...</option>
                {availablePrinters.map(printer => (
                  <option key={printer.name} value={printer.name}>
                    {printer.name} {printer.status === 'ready' ? '✓' : '⚠️'}
                  </option>
                ))}
              </select>
            </div>
          )}

          {/* File Settings */}
          {settings.printer.connectionType === 'file' && (
            <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="text-md font-medium text-green-900 mb-3">Cài đặt in vào file</h4>
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Thư mục lưu file
                  </label>
                  <input
                    type="text"
                    value={settings.printer.fileSettings?.outputPath || ''}
                    onChange={(e) => {
                      console.log('Updating outputPath:', e.target.value);
                      console.log('Current settings:', settings);
                      updateSetting('printer', 'fileSettings', 'outputPath', e.target.value);
                    }}
                    placeholder="C:\\temp\\prints"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    disabled={false}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Để trống sẽ lưu vào thư mục mặc định: prints/
                  </p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Định dạng tên file
                  </label>
                  <select
                    value={settings.printer.fileSettings?.fileNameFormat || 'timestamp'}
                    onChange={(e) => updateSetting('printer', 'fileSettings', 'fileNameFormat', e.target.value)}
                    className="input"
                  >
                    <option value="timestamp">Theo thời gian (print_20250607_150230.txt)</option>
                    <option value="username">Theo username (print_username_timestamp.txt)</option>
                    <option value="sequential">Theo số thứ tự (print_001.txt)</option>
                  </select>
                </div>
              </div>
              <p className="text-xs text-green-600 mt-2">
                💡 Chế độ này sẽ lưu nội dung in vào file text thay vì in ra máy in thật
              </p>
            </div>
          )}

          {/* Network Settings */}
          {settings.printer.connectionType === 'network' && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="text-md font-medium text-blue-900 mb-3">Cài đặt mạng TCP/IP</h4>
              {/* Debug info */}
              <div className="text-xs text-gray-500 mb-2 flex items-center justify-between">
                <span>Debug: IP = "{settings.printer.networkSettings?.ipAddress || 'undefined'}", Port = {settings.printer.networkSettings?.port || 'undefined'}</span>
                <button
                  onClick={() => {
                    console.log('Force refresh network settings');
                    setSettings(prev => ({
                      ...prev,
                      printer: {
                        ...prev.printer,
                        networkSettings: {
                          ipAddress: '',
                          port: 9100
                        }
                      }
                    }));
                  }}
                  className="text-xs bg-gray-200 px-2 py-1 rounded"
                >
                  Reset Network
                </button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Địa chỉ IP máy in
                  </label>
                  <input
                    type="text"
                    value={settings.printer.networkSettings?.ipAddress || ''}
                    onChange={(e) => {
                      console.log('IP Address input changed:', e.target.value);
                      updateSetting('printer', 'networkSettings', 'ipAddress', e.target.value);
                    }}
                    placeholder="*************"
                    className="input"
                    disabled={false}
                    readOnly={false}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Port
                  </label>
                  <input
                    type="number"
                    value={settings.printer.networkSettings?.port || 9100}
                    onChange={(e) => updateSetting('printer', 'networkSettings', 'port', parseInt(e.target.value))}
                    placeholder="9100"
                    className="input"
                  />
                </div>
              </div>
              <div className="mt-4 flex items-center space-x-3">
                <button
                  className="btn-secondary btn-sm"
                  onClick={() => testNetworkConnection()}
                  disabled={!settings.printer.networkSettings?.ipAddress}
                >
                  🔗 Test kết nối
                </button>
                <p className="text-xs text-blue-600">
                  💡 Port mặc định cho máy in nhiệt thường là 9100
                </p>
              </div>
            </div>
          )}

          {/* USB Settings */}
          {settings.printer.connectionType === 'usb' && (
            <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 className="text-md font-medium text-green-900 mb-3">Cài đặt USB trực tiếp</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Vendor ID
                  </label>
                  <input
                    type="text"
                    value={settings.printer.usbSettings?.vendorId || ''}
                    onChange={(e) => updateSetting('printer', 'usbSettings', 'vendorId', e.target.value)}
                    placeholder="0x04b8"
                    className="input"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Product ID
                  </label>
                  <input
                    type="text"
                    value={settings.printer.usbSettings?.productId || ''}
                    onChange={(e) => updateSetting('printer', 'usbSettings', 'productId', e.target.value)}
                    placeholder="0x0202"
                    className="input"
                  />
                </div>
              </div>
              <p className="text-xs text-green-600 mt-2">
                💡 Có thể tìm thấy thông tin này trong Device Manager của Windows
              </p>
            </div>
          )}

          {/* Print Settings */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chế độ in
              </label>
              <select
                value={settings.printer.printMode}
                onChange={(e) => updateSetting('printer', 'printMode', null, e.target.value)}
                className="input"
              >
                <option value="bitmap">Bitmap (Tiếng Việt)</option>
                <option value="text">Text (ASCII only)</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cắt giấy
              </label>
              <select
                value={settings.printer.cutType}
                onChange={(e) => updateSetting('printer', 'cutType', null, e.target.value)}
                className="input"
              >
                <option value="partial">Cắt một phần</option>
                <option value="full">Cắt hoàn toàn</option>
                <option value="none">Không cắt</option>
              </select>
            </div>
          </div>

          {/* Paper Info & Test Print */}
          <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
            <h4 className="text-md font-medium text-blue-900 mb-3">Thông tin giấy in</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
              <div>
                <span className="text-sm text-blue-700 font-medium">Độ rộng giấy:</span>
                <span className="text-sm text-blue-900 ml-2">80mm (cố định)</span>
              </div>
              <div>
                <span className="text-sm text-blue-700 font-medium">Chiều dài:</span>
                <span className="text-sm text-blue-900 ml-2">Tự động theo nội dung</span>
              </div>
            </div>
            <button
              className="btn-primary"
              onClick={handleTestPrint}
              disabled={!isConnected}
            >
              <Printer className="h-4 w-4 mr-2" />
              In thử nghiệm
            </button>
          </div>
        </div>
      )}

      {activeTab === 'format-single' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Settings Panel */}
          <div className="space-y-6">
            {/* Username Settings */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900 flex items-center">
                  <Type className="h-4 w-4 mr-2" />
                  Tên người dùng
                </h4>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showSingleUsername"
                    checked={settings.singleComment.username.show}
                    onChange={(e) => updateSetting('singleComment', 'username', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showSingleUsername" className="text-sm font-medium text-gray-700">
                    Hiển thị
                  </label>
                </div>
              </div>

              {settings.singleComment.username.show && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Kích thước</label>
                    <select
                      value={settings.singleComment.username.fontSize}
                      onChange={(e) => updateSetting('singleComment', 'username', 'fontSize', parseInt(e.target.value))}
                      className="input"
                    >
                      {fontSizeOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Font</label>
                    <select
                      value={settings.singleComment.username.fontFamily}
                      onChange={(e) => updateSetting('singleComment', 'username', 'fontFamily', e.target.value)}
                      className="input"
                    >
                      {fontFamilyOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Độ đậm</label>
                    <select
                      value={settings.singleComment.username.fontWeight}
                      onChange={(e) => updateSetting('singleComment', 'username', 'fontWeight', e.target.value)}
                      className="input"
                    >
                      <option value="normal">Bình thường</option>
                      <option value="bold">Đậm</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Căn lề</label>
                    <select
                      value={settings.singleComment.username.textAlign}
                      onChange={(e) => updateSetting('singleComment', 'username', 'textAlign', e.target.value)}
                      className="input"
                    >
                      <option value="left">Trái</option>
                      <option value="center">Giữa</option>
                      <option value="right">Phải</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Timestamp Settings */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">Thời gian</h4>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showSingleTimestamp"
                    checked={settings.singleComment.timestamp.show}
                    onChange={(e) => updateSetting('singleComment', 'timestamp', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showSingleTimestamp" className="text-sm font-medium text-gray-700">
                    Hiển thị
                  </label>
                </div>
              </div>

              {settings.singleComment.timestamp.show && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Kích thước</label>
                    <select
                      value={settings.singleComment.timestamp.fontSize}
                      onChange={(e) => updateSetting('singleComment', 'timestamp', 'fontSize', parseInt(e.target.value))}
                      className="input"
                    >
                      {fontSizeOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Font</label>
                    <select
                      value={settings.singleComment.timestamp.fontFamily}
                      onChange={(e) => updateSetting('singleComment', 'timestamp', 'fontFamily', e.target.value)}
                      className="input"
                    >
                      {fontFamilyOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Độ đậm</label>
                    <select
                      value={settings.singleComment.timestamp.fontWeight}
                      onChange={(e) => updateSetting('singleComment', 'timestamp', 'fontWeight', e.target.value)}
                      className="input"
                    >
                      <option value="normal">Bình thường</option>
                      <option value="bold">Đậm</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Căn lề</label>
                    <select
                      value={settings.singleComment.timestamp.textAlign}
                      onChange={(e) => updateSetting('singleComment', 'timestamp', 'textAlign', e.target.value)}
                      className="input"
                    >
                      <option value="left">Trái</option>
                      <option value="center">Giữa</option>
                      <option value="right">Phải</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Content Settings */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">Nội dung bình luận</h4>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showSingleContent"
                    checked={settings.singleComment.content.show}
                    onChange={(e) => updateSetting('singleComment', 'content', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showSingleContent" className="text-sm font-medium text-gray-700">
                    Hiển thị
                  </label>
                </div>
              </div>

              {settings.singleComment.content.show && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Kích thước</label>
                    <select
                      value={settings.singleComment.content.fontSize}
                      onChange={(e) => updateSetting('singleComment', 'content', 'fontSize', parseInt(e.target.value))}
                      className="input"
                    >
                      {fontSizeOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Font</label>
                    <select
                      value={settings.singleComment.content.fontFamily}
                      onChange={(e) => updateSetting('singleComment', 'content', 'fontFamily', e.target.value)}
                      className="input"
                    >
                      {fontFamilyOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Độ đậm</label>
                    <select
                      value={settings.singleComment.content.fontWeight}
                      onChange={(e) => updateSetting('singleComment', 'content', 'fontWeight', e.target.value)}
                      className="input"
                    >
                      <option value="normal">Bình thường</option>
                      <option value="bold">Đậm</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Căn lề</label>
                    <select
                      value={settings.singleComment.content.textAlign}
                      onChange={(e) => updateSetting('singleComment', 'content', 'textAlign', e.target.value)}
                      className="input"
                    >
                      <option value="left">Trái</option>
                      <option value="center">Giữa</option>
                      <option value="right">Phải</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Layout Settings */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="text-md font-medium text-blue-900 mb-3">Layout</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Padding (px)</label>
                  <input
                    type="number"
                    min="0"
                    max="20"
                    value={settings.singleComment.padding}
                    onChange={(e) => updateSetting('singleComment', 'padding', null, parseInt(e.target.value))}
                    className="input"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Khoảng cách dòng</label>
                  <input
                    type="number"
                    min="0"
                    max="10"
                    value={settings.singleComment.lineSpacing}
                    onChange={(e) => updateSetting('singleComment', 'lineSpacing', null, parseInt(e.target.value))}
                    className="input"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Preview Panel */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Eye className="h-5 w-5 text-green-600" />
              <h4 className="text-md font-medium text-gray-900">Xem trước - Comment đơn</h4>
            </div>

            <div className="bg-gray-100 p-4 rounded-lg border border-gray-300 overflow-x-auto">
              <div className="text-center mb-4">
                <span className="text-xs text-gray-500">
                  Độ rộng: 80mm (cố định)
                </span>
              </div>
              {renderPreview('single')}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'format-history' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Settings Panel */}
          <div className="space-y-6">
            {/* Header Settings */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">Tiêu đề</h4>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showHeader"
                    checked={settings.userHistory.header.show}
                    onChange={(e) => updateSetting('userHistory', 'header', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showHeader" className="text-sm font-medium text-gray-700">
                    Hiển thị
                  </label>
                </div>
              </div>

              {settings.userHistory.header.show && (
                <>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Nội dung tiêu đề</label>
                    <input
                      type="text"
                      value={settings.userHistory.header.text}
                      onChange={(e) => updateSetting('userHistory', 'header', 'text', e.target.value)}
                      placeholder="LỊCH SỬ BÌNH LUẬN"
                      className="input"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Kích thước</label>
                      <select
                        value={settings.userHistory.header.fontSize}
                        onChange={(e) => updateSetting('userHistory', 'header', 'fontSize', parseInt(e.target.value))}
                        className="input"
                      >
                        {fontSizeOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Font</label>
                      <select
                        value={settings.userHistory.header.fontFamily}
                        onChange={(e) => updateSetting('userHistory', 'header', 'fontFamily', e.target.value)}
                        className="input"
                      >
                        {fontFamilyOptions.map(option => (
                          <option key={option.value} value={option.value}>{option.label}</option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Độ đậm</label>
                      <select
                        value={settings.userHistory.header.fontWeight}
                        onChange={(e) => updateSetting('userHistory', 'header', 'fontWeight', e.target.value)}
                        className="input"
                      >
                        <option value="normal">Bình thường</option>
                        <option value="bold">Đậm</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Căn lề</label>
                      <select
                        value={settings.userHistory.header.textAlign}
                        onChange={(e) => updateSetting('userHistory', 'header', 'textAlign', e.target.value)}
                        className="input"
                      >
                        <option value="left">Trái</option>
                        <option value="center">Giữa</option>
                        <option value="right">Phải</option>
                      </select>
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Username Settings */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">Tên người dùng</h4>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showUsername"
                    checked={settings.userHistory.username.show}
                    onChange={(e) => updateSetting('userHistory', 'username', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showUsername" className="text-sm font-medium text-gray-700">
                    Hiển thị
                  </label>
                </div>
              </div>

              {settings.userHistory.username.show && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Kích thước</label>
                    <select
                      value={settings.userHistory.username.fontSize}
                      onChange={(e) => updateSetting('userHistory', 'username', 'fontSize', parseInt(e.target.value))}
                      className="input"
                    >
                      {fontSizeOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Font</label>
                    <select
                      value={settings.userHistory.username.fontFamily}
                      onChange={(e) => updateSetting('userHistory', 'username', 'fontFamily', e.target.value)}
                      className="input"
                    >
                      {fontFamilyOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Độ đậm</label>
                    <select
                      value={settings.userHistory.username.fontWeight}
                      onChange={(e) => updateSetting('userHistory', 'username', 'fontWeight', e.target.value)}
                      className="input"
                    >
                      <option value="normal">Bình thường</option>
                      <option value="bold">Đậm</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Căn lề</label>
                    <select
                      value={settings.userHistory.username.textAlign}
                      onChange={(e) => updateSetting('userHistory', 'username', 'textAlign', e.target.value)}
                      className="input"
                    >
                      <option value="left">Trái</option>
                      <option value="center">Giữa</option>
                      <option value="right">Phải</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Timestamp Settings for History */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">Thời gian</h4>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showTimestamp"
                    checked={settings.userHistory.timestamp.show}
                    onChange={(e) => updateSetting('userHistory', 'timestamp', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showTimestamp" className="text-sm font-medium text-gray-700">
                    Hiển thị
                  </label>
                </div>
              </div>

              {settings.userHistory.timestamp.show && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Kích thước</label>
                    <select
                      value={settings.userHistory.timestamp.fontSize}
                      onChange={(e) => updateSetting('userHistory', 'timestamp', 'fontSize', parseInt(e.target.value))}
                      className="input"
                    >
                      {fontSizeOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Font</label>
                    <select
                      value={settings.userHistory.timestamp.fontFamily}
                      onChange={(e) => updateSetting('userHistory', 'timestamp', 'fontFamily', e.target.value)}
                      className="input"
                    >
                      {fontFamilyOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Độ đậm</label>
                    <select
                      value={settings.userHistory.timestamp.fontWeight}
                      onChange={(e) => updateSetting('userHistory', 'timestamp', 'fontWeight', e.target.value)}
                      className="input"
                    >
                      <option value="normal">Bình thường</option>
                      <option value="bold">Đậm</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Căn lề</label>
                    <select
                      value={settings.userHistory.timestamp.textAlign}
                      onChange={(e) => updateSetting('userHistory', 'timestamp', 'textAlign', e.target.value)}
                      className="input"
                    >
                      <option value="left">Trái</option>
                      <option value="center">Giữa</option>
                      <option value="right">Phải</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Comment Item Settings */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-md font-medium text-gray-900">Nội dung comment</h4>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showCommentItem"
                    checked={settings.userHistory.commentItem.show}
                    onChange={(e) => updateSetting('userHistory', 'commentItem', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showCommentItem" className="text-sm font-medium text-gray-700">
                    Hiển thị
                  </label>
                </div>
              </div>

              {settings.userHistory.commentItem.show && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Kích thước</label>
                    <select
                      value={settings.userHistory.commentItem.fontSize}
                      onChange={(e) => updateSetting('userHistory', 'commentItem', 'fontSize', parseInt(e.target.value))}
                      className="input"
                    >
                      {fontSizeOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Font</label>
                    <select
                      value={settings.userHistory.commentItem.fontFamily}
                      onChange={(e) => updateSetting('userHistory', 'commentItem', 'fontFamily', e.target.value)}
                      className="input"
                    >
                      {fontFamilyOptions.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Độ đậm</label>
                    <select
                      value={settings.userHistory.commentItem.fontWeight}
                      onChange={(e) => updateSetting('userHistory', 'commentItem', 'fontWeight', e.target.value)}
                      className="input"
                    >
                      <option value="normal">Bình thường</option>
                      <option value="bold">Đậm</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Căn lề</label>
                    <select
                      value={settings.userHistory.commentItem.textAlign}
                      onChange={(e) => updateSetting('userHistory', 'commentItem', 'textAlign', e.target.value)}
                      className="input"
                    >
                      <option value="left">Trái</option>
                      <option value="center">Giữa</option>
                      <option value="right">Phải</option>
                    </select>
                  </div>
                </div>
              )}
            </div>

            {/* Options */}
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
              <h4 className="text-md font-medium text-blue-900 mb-3">Tùy chọn</h4>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Số comment tối đa mỗi trang (Không giới hạn)
                  </label>
                  <input
                    type="number"
                    min="5"
                    value={settings.userHistory.maxCommentsPerPage}
                    onChange={(e) => updateSetting('userHistory', 'maxCommentsPerPage', null, parseInt(e.target.value))}
                    className="input w-32"
                  />
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showSeparator"
                    checked={settings.userHistory.separator.show}
                    onChange={(e) => updateSetting('userHistory', 'separator', 'show', e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showSeparator" className="text-sm font-medium text-gray-700">
                    Hiển thị đường phân cách
                  </label>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="showSummary"
                    checked={settings.userHistory.showSummary}
                    onChange={(e) => updateSetting('userHistory', 'showSummary', null, e.target.checked)}
                    className="mr-2"
                  />
                  <label htmlFor="showSummary" className="text-sm font-medium text-gray-700">
                    Hiển thị tổng kết
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Preview Panel */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2 mb-4">
              <Eye className="h-5 w-5 text-green-600" />
              <h4 className="text-md font-medium text-gray-900">Xem trước - Lịch sử user</h4>
            </div>

            <div className="bg-gray-100 p-4 rounded-lg border border-gray-300 overflow-x-auto">
              <div className="text-center mb-4">
                <span className="text-xs text-gray-500">
                  Độ rộng: 80mm (cố định)
                </span>
              </div>
              {renderPreview('history')}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PrintSettings;
