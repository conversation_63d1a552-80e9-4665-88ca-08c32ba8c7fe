# 🔄 MongoDB Atlas Sync Status Indicator

## 🎯 Tổng quan

Tính năng hiển thị trạng thái đồng bộ với MongoDB Atlas trên giao diện người dùng, gi<PERSON>p theo dõi tình trạng sync của dữ liệu real-time.

## 📍 Vị trí hiển thị

### **1. <PERSON><PERSON> Lịch sử in (History)**
- **Header**: Sync status indicator lớn với text
- **Footer**: Compact indicator chỉ có icon
- **Comment items**: Micro indicator cho từng comment đã sync

### **2. Trang Quản lý khách hàng (CustomerManagement)**
- **Header**: Sync status indicator với text
- **Customer items**: Badge hiển thị trạng thái sync của từng khách hàng

## 🎨 Component Design

### **SyncStatusIndicator Component**
```jsx
<SyncStatusIndicator 
  type="general|customers|history" 
  size="xs|sm|md|lg"
  showText={true|false}
  className="custom-classes"
/>
```

### **Size Configurations:**
- **xs**: Icon 3x3, text xs, minimal spacing
- **sm**: Icon 4x4, text sm, compact spacing  
- **md**: Icon 5x5, text base, normal spacing
- **lg**: Icon 6x6, text lg, generous spacing

## 🔄 Trạng thái Sync

### **1. Mất kết nối Server**
- **Icon**: `WifiOff` (gray)
- **Color**: `text-gray-400`
- **Background**: `bg-gray-100`
- **Text**: "Mất kết nối"
- **Tooltip**: "Mất kết nối với server"

### **2. MongoDB chưa kết nối**
- **Icon**: `CloudOff` (red)
- **Color**: `text-red-500`
- **Background**: `bg-red-50`
- **Text**: "Chưa sync"
- **Tooltip**: "MongoDB Atlas chưa kết nối"

### **3. Đang đồng bộ**
- **Icon**: `Loader` (blue, spinning)
- **Color**: `text-blue-500`
- **Background**: `bg-blue-50`
- **Text**: "Đang sync..."
- **Tooltip**: "Đang đồng bộ dữ liệu"
- **Animation**: `animate-spin`

### **4. Lỗi đồng bộ**
- **Icon**: `AlertCircle` (orange)
- **Color**: `text-orange-500`
- **Background**: `bg-orange-50`
- **Text**: "Lỗi sync"
- **Tooltip**: Error message chi tiết

### **5. Đã đồng bộ (gần đây)**
- **Icon**: `CheckCircle` (green)
- **Color**: `text-green-500`
- **Background**: `bg-green-50`
- **Text**: "Đã sync"
- **Tooltip**: "Lần sync cuối: [timestamp]"
- **Condition**: < 10 phút

### **6. Đã đồng bộ (lâu rồi)**
- **Icon**: `Cloud` (blue)
- **Color**: `text-blue-500`
- **Background**: `bg-blue-50`
- **Text**: "Đã sync"
- **Tooltip**: "Lần sync cuối: [timestamp]"
- **Condition**: > 10 phút

### **7. Sẵn sàng**
- **Icon**: `Cloud` (blue)
- **Color**: `text-blue-500`
- **Background**: `bg-blue-50`
- **Text**: "Sẵn sàng"
- **Tooltip**: "MongoDB Atlas đã kết nối"

## ⚡ Real-time Updates

### **Socket Events:**
```javascript
// Sync completed
socket.on('mongodb-sync-completed', (data) => {
  // Update sync status
  // Show last sync time
});

// Sync started
socket.on('mongodb-sync-started', () => {
  // Show syncing state
});

// Sync error
socket.on('mongodb-sync-error', (error) => {
  // Show error state
});
```

### **Automatic Checks:**
- **Interval**: Mỗi 30 giây check MongoDB status
- **API**: `GET /api/mongodb/status`
- **Response**: Connection status, last sync time, auto-sync config

## 🔧 Implementation Details

### **1. History Page Integration:**
```jsx
// Header - Full indicator
<div className="flex items-center space-x-2 px-3 py-1 bg-gray-50 rounded-lg border">
  <SyncStatusIndicator 
    type="history" 
    size="sm" 
    showText={true}
  />
</div>

// Footer - Compact indicator
<div className="flex items-center">
  <span className="mr-2">Atlas:</span>
  <SyncStatusIndicator 
    type="history" 
    size="xs" 
    showText={false}
  />
</div>

// Comment item - Micro indicator
{comment.synced_at && (
  <div className="flex items-center space-x-1 text-green-600">
    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
    <span>Atlas</span>
  </div>
)}
```

### **2. Customer Management Integration:**
```jsx
// Header - Full indicator
<div className="flex items-center space-x-2 px-3 py-2 bg-gray-50 rounded-lg border">
  <SyncStatusIndicator 
    type="customers" 
    size="sm" 
    showText={true}
  />
</div>

// Customer item - Sync badge
{customer.synced_at && (
  <div className="flex items-center space-x-1 px-2 py-1 bg-green-50 rounded-full">
    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
    <span className="text-xs text-green-700">Đã sync</span>
  </div>
)}
```

### **3. Manual Sync Button:**
```jsx
// Only show for larger sizes and when connected
{size !== 'xs' && syncStatus.isConnected && !syncStatus.syncing && (
  <button
    onClick={handleManualSync}
    className="ml-2 p-1 text-gray-400 hover:text-blue-500 transition-colors"
    title="Đồng bộ thủ công"
  >
    <RefreshCw className="h-3 w-3" />
  </button>
)}
```

## 📊 Data Flow

### **1. Component State:**
```javascript
const [syncStatus, setSyncStatus] = useState({
  isConnected: false,
  lastSync: null,
  syncing: false,
  error: null
});
```

### **2. API Integration:**
```javascript
// Check MongoDB status
const response = await fetch('/api/mongodb/status');
const data = await response.json();

// Manual sync trigger
const response = await fetch('/api/mongodb/smart-sync', { method: 'POST' });
```

### **3. Time Formatting:**
```javascript
const formatLastSync = () => {
  const diff = now - lastSyncTime;
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  if (days > 0) return `${days} ngày trước`;
  if (hours > 0) return `${hours} giờ trước`;
  if (minutes > 0) return `${minutes} phút trước`;
  return 'Vừa xong';
};
```

## 🎯 User Experience

### **Visual Hierarchy:**
1. **Primary**: Header indicators (most prominent)
2. **Secondary**: Footer indicators (subtle)
3. **Tertiary**: Item-level indicators (minimal)

### **Color Coding:**
- **Green**: Success, synced, healthy
- **Blue**: Ready, connected, normal operation
- **Orange**: Warning, errors, attention needed
- **Red**: Critical, disconnected, failed
- **Gray**: Neutral, disabled, unavailable

### **Progressive Disclosure:**
- **Icon only**: Quick status at a glance
- **Icon + Text**: Clear status with context
- **Icon + Text + Time**: Full status with details
- **Tooltip**: Additional information on hover

## 🔍 Monitoring & Debugging

### **Console Logs:**
```javascript
console.log('Sync status updated:', syncStatus);
console.log('Last sync time:', lastSyncTime);
console.log('Manual sync triggered');
```

### **Network Requests:**
- Monitor `/api/mongodb/status` calls
- Check `/api/mongodb/smart-sync` responses
- Verify socket event reception

### **Visual Debugging:**
- Check icon rendering and animations
- Verify color changes based on status
- Test tooltip content and positioning

## 🚀 Future Enhancements

### **Advanced Features:**
1. **Sync Progress**: Show percentage during sync
2. **Conflict Indicators**: Highlight sync conflicts
3. **Batch Operations**: Bulk sync controls
4. **Sync History**: Log of recent sync activities
5. **Performance Metrics**: Sync speed and efficiency

### **Accessibility:**
1. **Screen Reader**: Proper ARIA labels
2. **Keyboard Navigation**: Focus management
3. **High Contrast**: Color accessibility
4. **Reduced Motion**: Respect user preferences
