<!DOCTYPE html>
<html lang="vi">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%PUBLIC_URL%/favicon.png" />
  <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="theme-color" content="#0ea5e9" />
  <meta name="description" content="CommiLive - Instagram Live Comment Management System - Mobile Interface" />

  <!-- PWA Meta Tags -->
  <meta name="apple-mobile-web-app-capable" content="yes" />
  <meta name="apple-mobile-web-app-status-bar-style" content="default" />
  <meta name="apple-mobile-web-app-title" content="CommiLive" />
  <meta name="mobile-web-app-capable" content="yes" />

  <!-- Apple Touch Icons -->
  <link rel="apple-touch-icon" sizes="180x180" href="%PUBLIC_URL%/favicon.png" />
  <link rel="icon" type="image/png" sizes="192x192" href="%PUBLIC_URL%/logo192.png" />
  <link rel="icon" type="image/png" sizes="512x512" href="%PUBLIC_URL%/logo512.png" />

  <!-- Manifest -->
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />

  <!-- Preconnect to improve performance -->
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />

  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />

  <title>CommiLive - Mobile</title>

  <style>
    body {
      font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      margin: 0;
      padding: 0;
      background-color: #f8fafc;
    }

    code {
      font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
        monospace;
    }

    /* Loading spinner */
    .loading-spinner {
      border: 3px solid #f3f3f3;
      border-top: 3px solid #8B5CF6;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      animation: spin 1s linear infinite;
      margin: 20px auto;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
      width: 6px;
    }

    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }

    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #a1a1a1;
    }

    /* Prevent zoom on input focus (iOS) */
    input[type="text"],
    input[type="email"],
    input[type="password"],
    input[type="number"],
    input[type="search"],
    input[type="tel"],
    input[type="url"],
    select,
    textarea {
      font-size: 16px;
    }

    /* Hide scrollbar for mobile */
    @media (max-width: 768px) {
      ::-webkit-scrollbar {
        display: none;
      }

      * {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }
    }

    /* Safe area for notched devices */
    .safe-area-top {
      padding-top: env(safe-area-inset-top);
    }

    .safe-area-bottom {
      padding-bottom: env(safe-area-inset-bottom);
    }

    /* Prevent text selection on buttons */
    button {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
    }

    /* Smooth transitions */
    * {
      transition: all 0.2s ease-in-out;
    }

    /* Focus styles for accessibility */
    button:focus,
    input:focus,
    textarea:focus,
    select:focus {
      outline: 2px solid #8B5CF6;
      outline-offset: 2px;
    }
  </style>
</head>

<body>
  <noscript>Bạn cần bật JavaScript để sử dụng ứng dụng này.</noscript>
  <div id="root">
    <div class="loading-spinner"></div>
    <p style="text-align: center; margin-top: 20px; color: #666;">Đang tải ứng dụng...</p>
  </div>
  <script type="module" src="/src/index.js"></script>

  <!-- Service Worker Registration -->
  <script>
    if ('serviceWorker' in navigator) {
      window.addEventListener('load', function () {
        navigator.serviceWorker.register('%PUBLIC_URL%/sw.js')
          .then(function (registration) {
            console.log('SW registered: ', registration);
          })
          .catch(function (registrationError) {
            console.log('SW registration failed: ', registrationError);
          });
      });
    }
  </script>
</body>

</html>