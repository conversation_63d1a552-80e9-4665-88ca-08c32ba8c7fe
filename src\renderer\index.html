<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instagram Live Comment System</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online {
            background-color: #10b981;
        }

        .status-offline {
            background-color: #ef4444;
        }

        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .log-container {
            background: #1f2937;
            color: #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }

        .log-error {
            color: #fca5a5;
        }

        .log-warning {
            color: #fcd34d;
        }

        .log-info {
            color: #93c5fd;
        }

        .log-success {
            color: #86efac;
        }
    </style>
</head>

<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="px-6 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="fab fa-instagram text-2xl text-purple-600 mr-3"></i>
                        <h1 class="text-xl font-semibold text-gray-900">Instagram Live Comment System</h1>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center">
                            <span class="status-indicator" id="serverStatus"></span>
                            <span id="serverStatusText">Đang kiểm tra...</span>
                        </div>
                        <button id="settingsBtn" class="p-2 text-gray-500 hover:text-gray-700">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="px-6 py-6">
            <!-- Control Panel -->
            <div class="card">
                <h2 class="text-lg font-semibold mb-4">Bảng Điều Khiển</h2>

                <!-- Action Buttons -->
                <div class="flex flex-wrap gap-3 mb-4">
                    <button id="serverStartBtn"
                        class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md flex items-center text-lg">
                        <i class="fas fa-server mr-2"></i>
                        Khởi động Server
                    </button>
                    <button id="serverStopBtn"
                        class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-md flex items-center text-lg"
                        disabled>
                        <i class="fas fa-stop mr-2"></i>
                        Dừng Server
                    </button>
                    <button id="openWebBtn"
                        class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md flex items-center text-lg">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Mở Web Interface
                    </button>
                </div>
            </div>

            <!-- System Logs -->
            <div class="card">
                <h3 class="text-lg font-semibold mb-4">Nhật ký hệ thống</h3>
                <div id="systemLogs" class="log-container">
                    <div class="log-entry log-info">[INFO] Hệ thống đã sẵn sàng</div>
                </div>
                <div class="mt-4 text-sm text-gray-600">
                    <p><strong>Hướng dẫn:</strong></p>
                    <ul class="list-disc list-inside mt-2 space-y-1">
                        <li>Nhấn "Khởi động Server" để bắt đầu hệ thống</li>
                        <li>Nhấn "Mở Web Interface" để truy cập giao diện web đầy đủ</li>
                        <li>Tất cả tính năng quản lý comment và in ấn có trong web interface</li>
                    </ul>
                </div>
            </div>

            <!-- Mobile Access Card -->
            <div id="mobileAccessCard" class="bg-white rounded-lg shadow-md p-6 hidden">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-mobile-alt mr-2 text-purple-600"></i>
                    📱 Truy cập từ điện thoại
                </h3>
                <div class="flex flex-col md:flex-row items-center space-y-4 md:space-y-0 md:space-x-6">
                    <div class="flex-shrink-0">
                        <img id="qrCode" src="" alt="QR Code" class="w-32 h-32 border rounded-lg hidden">
                    </div>
                    <div class="flex-1 text-center md:text-left">
                        <p class="text-sm text-gray-600 mb-2">Quét mã QR hoặc truy cập URL:</p>
                        <div class="bg-gray-100 p-3 rounded-md mb-3">
                            <code id="mobileUrl" class="text-sm text-blue-600">Đang tải...</code>
                        </div>
                        <button id="copyUrlBtn"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md text-sm">
                            <i class="fas fa-copy mr-1"></i>
                            Sao chép URL
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Settings Modal -->
    <div id="settingsModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Cài đặt</h3>
                    <button id="closeSettingsBtn" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="space-y-4">
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="autoStartServer" class="mr-2">
                            <span>Tự động khởi động server</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="autoStartWeb" class="mr-2">
                            <span>Tự động mở web interface</span>
                        </label>
                    </div>
                    <div>
                        <label class="flex items-center">
                            <input type="checkbox" id="enableNotifications" class="mr-2">
                            <span>Bật thông báo desktop</span>
                        </label>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Cổng server</label>
                        <input type="number" id="serverPort" class="w-full px-3 py-2 border border-gray-300 rounded-md"
                            value="3001">
                    </div>
                </div>
                <div class="mt-6 flex justify-end space-x-3">
                    <button id="cancelSettingsBtn" class="px-4 py-2 text-gray-600 hover:text-gray-800">Hủy</button>
                    <button id="saveSettingsBtn"
                        class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md">Lưu</button>
                </div>
            </div>
        </div>
    </div>



    <script src="renderer.js"></script>
</body>

</html>