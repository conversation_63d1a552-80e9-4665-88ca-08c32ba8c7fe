# 🗑️ Printed History Auto Cleanup System

## 🎯 Tổng quan

Tính năng tự động dọn dẹp `printed_history` records đã bị soft delete để tránh database bloat và duy trì performance tối ưu.

## 🔄 Cleanup Strategy

### **Two-Phase Deletion:**
1. **Phase 1 - Soft Delete**: User xóa → `is_deleted = 1` (immediate)
2. **Phase 2 - Hard Delete**: Auto cleanup → `DELETE FROM` (after retention period)

### **Retention Policy:**
- **Soft Delete**: Immediate khi user xóa
- **Hard Delete**: Sau **90 ngày** từ khi tạo record
- **Frequency**: Chạy mỗi **24 giờ**

## 🕐 Automatic Cleanup Schedule

### **Daily Cleanup Task:**
```javascript
// Chạy mỗi 24 giờ lúc server start time
setInterval(async () => {
  const deletedCount = await database.cleanupOldPrintedHistory(90); // 90 days
  // Emit socket event về cleanup results
}, 24 * 60 * 60 * 1000);
```

### **Cleanup Logic:**
```sql
-- Hard delete records: soft deleted + older than 90 days
DELETE FROM printed_history 
WHERE is_deleted = 1 
AND created_at < DATE('now', '-90 days');
```

## 📊 Cleanup Method

### **Database Method:**
```javascript
async cleanupOldPrintedHistory(olderThanDays = 90) {
  // 1. Calculate cutoff date
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
  
  // 2. Get records to be deleted (for logging)
  const recordsToDelete = await this.allQuery(
    'SELECT * FROM printed_history WHERE is_deleted = 1 AND created_at < ?',
    [cutoffISO]
  );
  
  // 3. Hard delete old soft-deleted records
  const result = await this.runQuery(
    'DELETE FROM printed_history WHERE is_deleted = 1 AND created_at < ?',
    [cutoffISO]
  );
  
  // 4. Log and emit cleanup event
  if (result.changes > 0) {
    logger.info(`🗑️ Hard deleted ${result.changes} old printed history records`);
    io.emit('printed-history-cleanup-completed', { ... });
  }
  
  return result.changes;
}
```

## 🚀 API Endpoints

### **Manual Cleanup:**
```bash
POST /api/printed-history/cleanup
Content-Type: application/json

{
  "olderThanDays": 90  # Optional, default: 90
}
```

**Response:**
```json
{
  "success": true,
  "message": "Hard deleted 25 old printed history records older than 90 days",
  "deletedCount": 25,
  "cleanupDays": 90
}
```

### **Usage Examples:**
```bash
# Default cleanup (90 days)
curl -X POST http://localhost:3001/api/printed-history/cleanup

# Custom retention (30 days)
curl -X POST http://localhost:3001/api/printed-history/cleanup \
  -H "Content-Type: application/json" \
  -d '{"olderThanDays": 30}'

# Aggressive cleanup (7 days)
curl -X POST http://localhost:3001/api/printed-history/cleanup \
  -H "Content-Type: application/json" \
  -d '{"olderThanDays": 7}'
```

## 📡 Real-time Notifications

### **Socket Events:**
```javascript
// Client-side listening
socket.on('printed-history-cleanup-completed', (data) => {
  console.log(`Cleanup completed: ${data.deletedCount} records deleted`);
  // Update UI if needed
});
```

**Event Data:**
```json
{
  "deletedCount": 25,
  "cleanupDays": 90,
  "timestamp": "2025-06-25T08:00:00.000Z"
}
```

## 🔧 Configuration

### **Cleanup Settings:**
```javascript
const PRINTED_HISTORY_CLEANUP_CONFIG = {
  ENABLED: true,
  CLEANUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  RETENTION_DAYS: 90,
  AUTO_EMIT_EVENTS: true
};
```

### **Customizable Parameters:**
- **Retention Period**: 90 ngày (có thể thay đổi)
- **Cleanup Frequency**: 24 giờ (có thể thay đổi)
- **Manual Trigger**: API endpoint available
- **Event Emission**: Socket notifications

## 📈 Benefits

### **1. Database Performance:**
- **Reduced table size**: Ít records hơn → query nhanh hơn
- **Index efficiency**: Indexes không bị bloat
- **Storage optimization**: Tiết kiệm disk space

### **2. System Health:**
- **Memory usage**: Ít data load vào memory
- **Backup speed**: Database backup nhanh hơn
- **Maintenance**: Easier database maintenance

### **3. Compliance:**
- **Data retention**: Tuân thủ data retention policies
- **Privacy**: Xóa data cũ theo quy định
- **Audit trail**: Log cleanup activities

## 🔍 Monitoring

### **Cleanup Logs:**
```
[INFO] 🧹 Starting automatic printed history cleanup...
[INFO] ✅ Automatic cleanup: Hard deleted 25 old printed history records older than 90 days
[DEBUG] Automatic printed history cleanup: No old records found
[ERROR] Automatic printed history cleanup failed: [error details]
```

### **Metrics to Track:**
- **Records deleted per cleanup**
- **Cleanup execution time**
- **Database size reduction**
- **Error frequency**

## ⚠️ Important Notes

### **Data Loss Warning:**
- **Hard delete** là **PERMANENT** - không thể khôi phục
- **90 ngày** là thời gian đủ dài cho business needs
- **Manual cleanup** cần cẩn thận với `olderThanDays` parameter

### **Business Considerations:**
- **Legal requirements**: Check data retention laws
- **Business needs**: Có cần giữ data lâu hơn không?
- **Audit requirements**: Có cần audit trail không?

### **Technical Considerations:**
- **MongoDB sync**: Hard delete không sync lên MongoDB (local only)
- **Backup strategy**: Ensure backups before cleanup
- **Recovery plan**: Plan for accidental data loss

## 🔄 Comparison với Other Cleanups

| Table | Retention | Type | Frequency |
|-------|-----------|------|-----------|
| `send_once_history` | 2 days | Soft Delete | 24h |
| `printed_history` | 90 days | Hard Delete | 24h |
| `message_queue` | 7 days | Hard Delete | Manual |
| `instagram_threads` | 30 days | Hard Delete | Manual |

## 🎯 Future Enhancements

### **Potential Improvements:**
1. **Configurable retention**: UI setting cho retention period
2. **Selective cleanup**: Cleanup by user, date range, etc.
3. **Backup before delete**: Auto backup before hard delete
4. **Cleanup analytics**: Dashboard cho cleanup metrics
5. **Smart retention**: Dynamic retention based on usage patterns

### **Advanced Features:**
1. **Compression**: Compress old records thay vì delete
2. **Archival**: Move to archive table thay vì delete
3. **Tiered storage**: Move old data to cheaper storage
4. **ML-based cleanup**: Predict optimal retention periods
