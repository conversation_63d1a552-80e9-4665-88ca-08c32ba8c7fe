# 🔄 Deleted History & Restore Feature

## 🎯 Tổng quan

Tính năng xem và khôi phục comments đã xóa trong trang lịch sử in, cho phép user có thể:
- <PERSON><PERSON> tất cả comments đã bị soft delete
- <PERSON>h<PERSON><PERSON> phục từng comment riêng lẻ
- <PERSON><PERSON><PERSON><PERSON> phục tất cả comments của một user
- Chuyển đổi dễ dàng giữa view thường và deleted view

## 🔄 Two-Phase Delete System

### **Phase 1: Soft Delete (User Action)**
- User xóa comment → `is_deleted = 1`
- Comment biến mất khỏi lịch sử thường
- Vẫn có thể khôi phục được
- Auto-sync deletion status lên MongoDB

### **Phase 2: Hard Delete (Auto Cleanup)**
- Sau 90 ngày → `DELETE FROM printed_history`
- Xóa vĩnh viễn, không thể khôi phục
- Chỉ áp dụng cho records đã soft delete

## 🖥️ Frontend Features

### **Toggle View Button**
```jsx
// N<PERSON>t chuyển đổi view trong header
<button onClick={() => setShowDeleted(!showDeleted)}>
  {showDeleted ? (
    <>
      <Eye className="h-4 w-4 mr-1" />
      Xem thường
    </>
  ) : (
    <>
      <Trash2 className="h-4 w-4 mr-1" />
      Xem đã xóa
    </>
  )}
</button>
```

### **Conditional Action Buttons**
```jsx
// Comment level actions
{showDeleted ? (
  // Deleted view - show restore button
  <button onClick={() => handleRestoreComment(comment)}>
    <RotateCcw className="h-3 w-3 mr-1" />
    Khôi phục
  </button>
) : (
  // Normal view - show reprint and delete buttons
  <>
    <button onClick={() => handleReprintComment(comment)}>
      <Printer className="h-3 w-3 mr-1" />
      In lại
    </button>
    <button onClick={() => handleDeleteComment(comment.id)}>
      <Trash2 className="h-3 w-3 mr-1" />
      Xóa
    </button>
  </>
)}
```

### **User Level Actions**
```jsx
// User header actions
{showDeleted ? (
  <button onClick={() => handleRestoreUserHistory(username)}>
    <RotateCcw className="h-3 w-3 mr-1" />
    Khôi phục ({totalComments})
  </button>
) : (
  <>
    <button onClick={() => handleDeleteUserComments(username)}>
      <UserX className="h-3 w-3 mr-1" />
      Xóa ({totalComments})
    </button>
    <button onClick={() => handlePrintUserHistory(username)}>
      <FileText className="h-3 w-3 mr-1" />
      In lịch sử
    </button>
  </>
)}
```

## 🚀 API Endpoints

### **Get Deleted History**
```bash
GET /api/history/deleted?page=1&limit=50&search=username
```

**Response:**
```json
{
  "success": true,
  "history": [
    {
      "id": 123,
      "username": "user123",
      "comment_text": "Hello world",
      "printed_at": "2025-06-25T08:00:00.000Z",
      "is_deleted": 1
    }
  ],
  "total": 25,
  "page": 1,
  "limit": 50,
  "totalPages": 1
}
```

### **Restore Single Comment**
```bash
POST /api/comments/{id}/restore
```

**Response:**
```json
{
  "success": true,
  "message": "Comment restored successfully",
  "id": 123
}
```

### **Restore User History**
```bash
POST /api/comments/user/{username}/restore
```

**Response:**
```json
{
  "success": true,
  "message": "Restored 5 comments for user username",
  "restoredCount": 5
}
```

## 🗄️ Database Methods

### **Get Deleted History**
```javascript
async getDeletedPrintedHistory(options = {}) {
  // Similar to getPrintedHistory but with WHERE is_deleted = 1
  // Supports same filtering: search, date, time, pagination
}
```

### **Restore Single Record**
```javascript
async restorePrintedHistoryRecord(historyId) {
  // UPDATE printed_history SET is_deleted = 0 WHERE id = ?
  // Auto-sync restoration to MongoDB
}
```

### **Restore User Records**
```javascript
async restoreUserPrintedHistory(username) {
  // UPDATE printed_history SET is_deleted = 0 WHERE username = ? AND is_deleted = 1
  // Auto-sync restoration to MongoDB
}
```

## 🔄 MongoDB Sync

### **Restore Sync Method**
```javascript
async restorePrintedHistoryInMongo(localId, deviceId) {
  await collection.updateOne(
    { local_id: localId, device_id: deviceId },
    {
      $set: { is_deleted: false, synced_at: new Date() },
      $unset: { deleted_at: "" }
    }
  );
}
```

### **Auto-Sync Integration**
```javascript
// Enhanced autoSyncPrintedHistoryToMongoDB
if (action === 'restore') {
  await global.mongoDBService.restorePrintedHistoryInMongo(
    printedHistoryData.id, 
    printedHistoryData.device_id
  );
}
```

## 🎨 UI/UX Features

### **Visual Indicators**
- **Toggle Button**: Màu đỏ khi ở deleted view, xám khi ở normal view
- **Action Buttons**: Màu xanh cho restore, xanh lá cho reprint, đỏ cho delete
- **Empty State**: Message khác nhau cho deleted vs normal view
- **Footer Stats**: "X bình luận đã xóa" vs "X bình luận đã in"

### **State Management**
```javascript
const [showDeleted, setShowDeleted] = useState(false);

// Auto-reload when switching views
useEffect(() => {
  loadPrintedComments();
}, [showDeleted, searchTerm, dateFilter, pagination.page]);

// Different API endpoints
const endpoint = showDeleted 
  ? `/api/history/deleted?${params}` 
  : `/api/history?${params}`;
```

### **Filter Compatibility**
- **Search**: Hoạt động trên cả normal và deleted view
- **Date Filter**: Dựa trên `printed_at`, không phải deletion time
- **Advanced Filter**: Tất cả filters đều hoạt động cho deleted view
- **Pagination**: Reset về page 1 khi chuyển view

## 📊 User Experience

### **Workflow Example**
1. **User xóa comment** → Comment biến mất khỏi lịch sử
2. **User nhận ra xóa nhầm** → Click "Xem đã xóa"
3. **Tìm comment cần khôi phục** → Dùng search/filter
4. **Click "Khôi phục"** → Comment xuất hiện lại trong lịch sử thường
5. **Auto-sync** → Restoration được sync lên MongoDB

### **Bulk Operations**
- **Restore All User Comments**: Khôi phục tất cả comments đã xóa của một user
- **Individual Restore**: Khôi phục từng comment riêng lẻ
- **Smart Filtering**: Tìm comments cần khôi phục bằng search/date filter

## ⚠️ Important Notes

### **Data Retention**
- **Soft Delete**: Immediate, reversible
- **Hard Delete**: Sau 90 ngày, permanent
- **Grace Period**: 90 ngày để khôi phục

### **MongoDB Sync**
- **Restoration** được sync real-time lên MongoDB
- **Multi-device**: Restoration sync across devices
- **Conflict Resolution**: Timestamp-based như other operations

### **Performance**
- **Separate Endpoints**: `/api/history` vs `/api/history/deleted`
- **Same Filtering**: Reuse existing filter logic
- **Efficient Queries**: `WHERE is_deleted = 0/1` index optimization

## 🔮 Future Enhancements

### **Advanced Features**
1. **Bulk Restore**: Select multiple comments để restore cùng lúc
2. **Restore Preview**: Preview comment trước khi restore
3. **Deletion Reason**: Track lý do xóa (user delete, auto cleanup, etc.)
4. **Restore History**: Track ai restore gì khi nào
5. **Smart Suggestions**: Suggest comments có thể cần restore

### **Analytics**
1. **Deletion Patterns**: Analyze user deletion behavior
2. **Restore Rate**: Track % comments được restore
3. **Time to Restore**: Average time between delete và restore
4. **User Behavior**: Which users delete/restore most frequently

## 🎯 Benefits

### **User Experience**
- **Safety Net**: Không lo xóa nhầm
- **Flexibility**: Dễ dàng quản lý lịch sử
- **Transparency**: Thấy được tất cả actions

### **Business Value**
- **Data Recovery**: Giảm data loss
- **User Confidence**: Tăng confidence khi sử dụng
- **Support Reduction**: Ít tickets về "tôi xóa nhầm"

### **Technical Benefits**
- **Clean Architecture**: Separation of concerns
- **Scalable**: Reuse existing filter/pagination logic
- **Maintainable**: Consistent với existing patterns
