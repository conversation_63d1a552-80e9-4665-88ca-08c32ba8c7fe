# Tính năng Debug Comments

## 🎯 **<PERSON><PERSON><PERSON> đích:**
Thê<PERSON> tính năng debug comments để test hệ thống mà không cần Instagram Live thật. Giúp developer và user có thể:
- Test hệ thống in
- Test auto-messaging
- Test hiển thị comments
- Test performance vớ<PERSON> <PERSON>hi<PERSON> comments
- Debug các vấn đề mà không cần live stream

## 🛠️ **Cách hoạt động:**

### **Backend (InstagramScraper.js):**
```javascript
// Method thêm debug comments
addDebugComments(count = 1) {
  // Tạo random username và comment text
  // Emit comment events giống như comments thật
  // Mark với isDebug: true
  // Return danh sách comments đã thêm
}
```

### **API Endpoint (server.js):**
```javascript
POST /api/add-debug-comments
Body: { count: number (1-100) }
Response: { success: true, comments: [...] }
```

### **Frontend (Settings.js):**
- Input field để nhập số lượng comments (1-100)
- <PERSON><PERSON> "Thêm Comments" 
- Validation và error handling
- Status check (scraper phải đang chạy)

## 📱 **Giao diện người dùng:**

### **Vị trí:** 
- Trang Settings → Debug Comments section
- Sidebar → Cài đặt → Debug Comments

### **Components:**
1. **Input field:** Nhập số lượng comments (1-100)
2. **Button "Thêm Comments":** Trigger thêm debug comments
3. **Status indicators:** Hiển thị trạng thái scraper
4. **Instructions:** Hướng dẫn sử dụng

### **UI Features:**
- ✅ Validation số lượng (1-100)
- ✅ Disable button khi scraper không chạy
- ✅ Loading state khi đang thêm
- ✅ Toast notifications cho success/error
- ✅ Visual indicators và instructions

## 🧪 **Cách sử dụng:**

### **Bước 1: Khởi động Scraper**
1. Vào trang Settings → Thu thập bình luận
2. Đăng nhập Instagram (hoặc dùng saved cookies)
3. Nhập username Instagram Live (có thể fake)
4. Nhấn "Bắt đầu thu thập"

### **Bước 2: Thêm Debug Comments**
1. Vào Settings → Debug Comments
2. Nhập số lượng comments muốn thêm (1-100)
3. Nhấn "Thêm Comments"
4. Comments sẽ xuất hiện ngay trên trang Comments

### **Bước 3: Test các tính năng**
- **Test hiển thị:** Comments xuất hiện với [DEBUG] tag
- **Test in:** Nhấn nút Print để test máy in
- **Test auto-messaging:** Nhấn nút Print để test gửi tin nhắn
- **Test performance:** Thêm nhiều comments để test lag

## 📊 **Dữ liệu Debug Comments:**

### **Usernames mẫu:**
```javascript
[
  'user_test_1', 'user_test_2', 'debug_user_a', 'debug_user_b',
  'test_customer_1', 'fake_user_x', 'sample_user_1', ...
]
```

### **Comments mẫu:**
```javascript
[
  'Chào shop ạ!', 'Còn hàng không ạ?', 'Giá bao nhiêu vậy shop?',
  'Mình muốn mua 1 cái', 'Shop ship COD không?', 'Có màu khác không ạ?',
  'Size M còn không shop?', 'Inbox em với ạ', 'Đẹp quá shop ơi!', ...
]
```

### **Comment Structure:**
```javascript
{
  username: 'debug_user_a',
  text: 'Chào shop ạ!',
  timestamp: '2024-01-01T10:00:00.000Z',
  isDebug: true  // Đánh dấu là debug comment
}
```

## 🔧 **Technical Details:**

### **Validation:**
- Count phải từ 1-100
- Scraper phải đang chạy
- API rate limiting (nếu cần)

### **Error Handling:**
- Scraper not running
- Invalid count range
- Network errors
- Server errors

### **Performance:**
- Random selection để tránh duplicate
- Efficient comment generation
- Proper memory management

## 🎨 **UI/UX Features:**

### **Visual Indicators:**
- 🟢 Scraper đang chạy: Button enabled
- 🟡 Scraper chưa chạy: Warning message + disabled button
- 🔄 Đang thêm: Loading spinner
- ✅ Thành công: Success toast
- ❌ Lỗi: Error toast với message chi tiết

### **Instructions:**
- Cách sử dụng step-by-step
- Mục đích của tính năng
- Lưu ý về debug comments
- Link đến các trang liên quan

## 🚀 **Lợi ích:**

### **Cho Developer:**
- ✅ Test code mà không cần live stream
- ✅ Debug performance issues
- ✅ Test edge cases
- ✅ Validate new features

### **Cho User:**
- ✅ Test hệ thống trước khi live
- ✅ Học cách sử dụng các tính năng
- ✅ Kiểm tra máy in và auto-messaging
- ✅ Tự tin hơn khi sử dụng thật

## 📝 **Usage Examples:**

### **Test Performance:**
```
1. Thêm 50 debug comments
2. Kiểm tra lag trên trang Comments
3. Test scroll performance
4. Monitor memory usage
```

### **Test Printing:**
```
1. Thêm 5-10 debug comments
2. Nhấn Print button trên từng comment
3. Kiểm tra máy in hoạt động
4. Verify print format
```

### **Test Auto-Messaging:**
```
1. Setup auto-messaging templates
2. Thêm debug comments
3. Nhấn Print để trigger messaging
4. Check message queue và delivery
```

## 🔍 **Debugging:**

### **Log Messages:**
- `🧪 DEBUG COMMENT 1/5: debug_user_a: Chào shop ạ!`
- `✅ Added 5 debug comments successfully`

### **Identification:**
- Debug comments có `isDebug: true`
- Username bắt đầu với `test_`, `debug_`, `fake_`, `sample_`
- Có thể thêm [DEBUG] prefix trong UI

## 📈 **Future Enhancements:**

- Custom username/text input
- Batch operations
- Comment templates
- Performance metrics
- Auto-cleanup debug comments
- Integration với testing framework
