const puppeteer = require('puppeteer');
const EventEmitter = require('events');
const winston = require('winston');

class InstagramScraper extends EventEmitter {
  constructor() {
    super();
    this.browser = null;
    this.page = null;
    this.isRunning = false;
    // No duplicate tracking needed with API-only approach
    this.apiCommentsReceived = 0; // Track API comments received
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.simple(),
      transports: [new winston.transports.Console()]
    });
  }

  async start(liveUrl, credentials, savedCookies = null) {
    try {
      this.logger.info('Starting Instagram scraper...');

      // Reset API comment counter
      this.apiCommentsReceived = 0;

      // Removed periodic cleanup - system can handle unlimited processed comments cache

      // Launch browser
      this.browser = await puppeteer.launch({
        headless: false, // Set to true for production
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor',
          '--disable-blink-features=AutomationControlled',
          '--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
      });

      this.page = await this.browser.newPage();

      // Set user agent
      await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

      // Set viewport
      await this.page.setViewport({ width: 1366, height: 768 });

      // Set extra headers to avoid detection
      await this.page.setExtraHTTPHeaders({
        'Accept-Language': 'en-US,en;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8'
      });

      // Remove webdriver property
      await this.page.evaluateOnNewDocument(() => {
        Object.defineProperty(navigator, 'webdriver', {
          get: () => undefined,
        });
      });

      // Login to Instagram with cookies or credentials
      const loginResult = await this.login(credentials, savedCookies);

      if (!loginResult.success) {
        throw new Error('Login failed');
      }

      // Navigate to live stream
      await this.navigateToLive(liveUrl);

      // Set running state before starting monitoring
      this.isRunning = true;

      // Start monitoring comments
      await this.startCommentMonitoring();

      this.emit('connected');

      this.logger.info('Instagram scraper started successfully - Mode: API Interception Only');
    } catch (error) {
      this.logger.error('Failed to start Instagram scraper:', error);
      this.emit('error', error);
      throw error;
    }
  }

  async login(credentials, savedCookies = null) {
    try {
      this.logger.info('Logging into Instagram...');

      // Navigate to Instagram first
      await this.page.goto('https://www.instagram.com/', { waitUntil: 'networkidle2' });

      // Try to use saved cookies first
      if (savedCookies && savedCookies.length > 0) {
        this.logger.info('Attempting to login with saved cookies...');

        try {
          // Set cookies
          await this.page.setCookie(...savedCookies);

          // Refresh page to apply cookies
          await this.page.reload({ waitUntil: 'networkidle2' });

          // Check if we're logged in
          const isLoggedIn = await this.checkLoginStatus();

          if (isLoggedIn) {
            this.logger.info('Successfully logged in using saved cookies');
            return { success: true, method: 'cookies' };
          } else {
            this.logger.warn('Saved cookies are invalid, falling back to credential login');
          }
        } catch (cookieError) {
          this.logger.warn('Failed to use saved cookies:', cookieError);
        }
      }

      // Fallback to credential login
      if (!credentials || !credentials.username || !credentials.password) {
        throw new Error('No valid credentials provided and cookie login failed');
      }

      this.logger.info('Attempting credential login...');

      // Try login with retries
      const maxRetries = 3;
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          this.logger.info(`Login attempt ${attempt}/${maxRetries}`);
          const result = await this.attemptCredentialLogin(credentials);
          if (result.success) {
            return result;
          }
        } catch (error) {
          this.logger.warn(`Login attempt ${attempt} failed:`, error.message);
          if (attempt === maxRetries) {
            throw error;
          }
          // Wait before retry
          await this.page.waitForTimeout(3000);
        }
      }
    } catch (error) {
      this.logger.error('Login failed:', error);
      throw error;
    }
  }

  async attemptCredentialLogin(credentials) {
    try {
      // Navigate to login page
      await this.page.goto('https://www.instagram.com/accounts/login/', { waitUntil: 'networkidle2' });

      // Wait a bit for page to load
      await this.page.waitForTimeout(3000);

      // Try multiple selectors for username input
      let usernameSelector = null;
      const usernameSelectors = [
        'input[name="username"]',
        'input[aria-label="Phone number, username, or email"]',
        'input[placeholder*="username"]',
        'input[placeholder*="email"]',
        'input[type="text"]'
      ];

      for (const selector of usernameSelectors) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 });
          usernameSelector = selector;
          this.logger.info(`Found username input with selector: ${selector}`);
          break;
        } catch (error) {
          this.logger.warn(`Username selector ${selector} not found`);
        }
      }

      if (!usernameSelector) {
        // Take screenshot for debugging
        await this.page.screenshot({ path: 'login-page-debug.png' });
        throw new Error('Could not find username input field. Screenshot saved as login-page-debug.png');
      }

      // Find password input
      let passwordSelector = null;
      const passwordSelectors = [
        'input[name="password"]',
        'input[aria-label="Password"]',
        'input[type="password"]'
      ];

      for (const selector of passwordSelectors) {
        try {
          await this.page.waitForSelector(selector, { timeout: 2000 });
          passwordSelector = selector;
          this.logger.info(`Found password input with selector: ${selector}`);
          break;
        } catch (error) {
          this.logger.warn(`Password selector ${selector} not found`);
        }
      }

      if (!passwordSelector) {
        throw new Error('Could not find password input field');
      }

      // Clear and fill credentials
      await this.page.click(usernameSelector, { clickCount: 3 });
      await this.page.type(usernameSelector, credentials.username);

      await this.page.click(passwordSelector, { clickCount: 3 });
      await this.page.type(passwordSelector, credentials.password);

      // Wait a bit before clicking login
      await this.page.waitForTimeout(1000);

      // Find and click login button
      let loginButtonSelector = null;
      const loginButtonSelectors = [
        'button[type="submit"]',
        'button:contains("Log in")',
        'button:contains("Log In")',
        'div[role="button"]:contains("Log in")',
        'button._acan._acap._acas._aj1-'
      ];

      for (const selector of loginButtonSelectors) {
        try {
          const element = await this.page.$(selector);
          if (element) {
            loginButtonSelector = selector;
            this.logger.info(`Found login button with selector: ${selector}`);
            break;
          }
        } catch (error) {
          this.logger.warn(`Login button selector ${selector} not found`);
        }
      }

      if (!loginButtonSelector) {
        // Try to find button by text content
        const loginButton = await this.page.evaluateHandle(() => {
          const buttons = Array.from(document.querySelectorAll('button, div[role="button"]'));
          return buttons.find(btn =>
            btn.textContent.toLowerCase().includes('log in') ||
            btn.textContent.toLowerCase().includes('sign in')
          );
        });

        if (loginButton.asElement()) {
          await loginButton.asElement().click();
          this.logger.info('Found login button by text content');
        } else {
          throw new Error('Could not find login button');
        }
      } else {
        await this.page.click(loginButtonSelector);
      }

      // Wait for navigation or login response
      try {
        await Promise.race([
          this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 15000 }),
          this.page.waitForTimeout(10000)
        ]);
      } catch (error) {
        this.logger.warn('Navigation timeout, checking current state...');
      }

      // Wait a bit more for page to settle
      await this.page.waitForTimeout(3000);

      const currentUrl = this.page.url();
      this.logger.info(`Current URL after login attempt: ${currentUrl}`);

      // Handle two-factor authentication if needed
      if (currentUrl.includes('challenge') || currentUrl.includes('two_factor')) {
        this.logger.warn('Two-factor authentication required');
        this.emit('error', new Error('Two-factor authentication required. Please handle manually.'));
        return { success: false, error: '2FA required' };
      }

      // Check for login errors
      const errorElement = await this.page.$('#slfErrorAlert, .error, [role="alert"]');
      if (errorElement) {
        const errorText = await this.page.evaluate(el => el.textContent, errorElement);
        this.logger.error(`Login error detected: ${errorText}`);
        throw new Error(`Login failed: ${errorText}`);
      }

      // Check if still on login page
      if (currentUrl.includes('login') || currentUrl.includes('accounts/login')) {
        // Check if we're actually logged in despite being on login page
        const isLoggedIn = await this.checkLoginStatus();
        if (!isLoggedIn) {
          throw new Error('Login failed - still on login page. Please check credentials.');
        }
      }

      this.logger.info('Successfully logged into Instagram with credentials');

      // Save cookies for future use
      const cookies = await this.page.cookies();
      this.emit('cookies-available', { username: credentials.username, cookies });

      return { success: true, method: 'credentials' };
    } catch (error) {
      this.logger.error('Credential login attempt failed:', error);
      throw error;
    }
  }

  async checkLoginStatus() {
    try {
      // Wait a bit for page to load
      await this.page.waitForTimeout(2000);

      const loginStatus = await this.page.evaluate(() => {
        // Check for various indicators that we're logged in
        const indicators = {
          homeIcon: document.querySelector('svg[aria-label="Home"]') !== null,
          homeLink: document.querySelector('a[href="/"]') !== null,
          userAvatar: document.querySelector('[data-testid="user-avatar"]') !== null,
          navigation: document.querySelector('nav') !== null,
          searchBox: document.querySelector('input[placeholder*="Search"]') !== null,
          profileLink: document.querySelector('a[href*="/accounts/edit/"]') !== null,
          notOnLoginPage: !window.location.pathname.includes('/accounts/login'),
          isHomePage: window.location.pathname === '/',
          hasInstagramNav: document.querySelector('[role="navigation"]') !== null,
          hasMainContent: document.querySelector('main') !== null
        };

        console.log('Login status indicators:', indicators);

        // Return true if any strong indicator is present
        return indicators.homeIcon ||
          indicators.userAvatar ||
          indicators.searchBox ||
          indicators.profileLink ||
          (indicators.notOnLoginPage && (indicators.navigation || indicators.hasMainContent));
      });

      this.logger.info(`Login status check result: ${loginStatus}`);
      return loginStatus;
    } catch (error) {
      this.logger.error('Failed to check login status:', error);
      return false;
    }
  }

  async navigateToLive(liveUrl) {
    try {
      // Extract username from URL
      const username = liveUrl.match(/instagram\.com\/([^\/]+)/)?.[1];
      if (!username) {
        throw new Error('Invalid Instagram URL format - cannot extract username');
      }

      // Go directly to live URL
      const directLiveUrl = `https://www.instagram.com/${username}/live/`;
      this.logger.info(`Navigating directly to live URL: ${directLiveUrl}`);

      await this.page.goto(directLiveUrl, {
        waitUntil: 'networkidle2',
        timeout: 30000
      });

      // Wait for page to load
      await this.page.waitForTimeout(3000);

      // Check if user exists and page is available
      const pageContent = await this.page.content();
      if (pageContent.includes('Sorry, this page isn\'t available') ||
        pageContent.includes('User not found')) {
        throw new Error(`User "${username}" not found or page not available`);
      }

      // Look for "Tap to play" button and click it
      await this.handleTapToPlay();

      // Verify live stream is active
      await this.verifyLiveStream(username);

      this.logger.info('Successfully navigated to live stream');
    } catch (error) {
      this.logger.error('Failed to navigate to live stream:', error);
      throw error;
    }
  }

  async handleTapToPlay() {
    try {
      this.logger.info('Looking for "Tap to play" button...');

      // Multiple selectors for "Tap to play" button
      const tapToPlaySelectors = [
        'button[aria-label*="play" i]',
        'button[aria-label*="Play" i]',
        'div[role="button"][aria-label*="play" i]',
        'button:contains("Tap to play")',
        'button:contains("Play")',
        '[data-testid="play-button"]',
        '.play-button',
        'button[aria-label*="Phát" i]', // Vietnamese
        'div[role="button"]:contains("Tap to play")'
      ];

      let playButton = null;
      for (const selector of tapToPlaySelectors) {
        try {
          playButton = await this.page.$(selector);
          if (playButton) {
            this.logger.info(`Found play button: ${selector}`);
            break;
          }
        } catch (error) {
          // Continue to next selector
        }
      }

      // Also try to find by text content
      if (!playButton) {
        playButton = await this.page.evaluateHandle(() => {
          const buttons = Array.from(document.querySelectorAll('button, div[role="button"]'));
          return buttons.find(btn =>
            btn.textContent.toLowerCase().includes('tap to play') ||
            btn.textContent.toLowerCase().includes('play') ||
            btn.getAttribute('aria-label')?.toLowerCase().includes('play')
          );
        });
      }

      if (playButton && playButton.asElement) {
        this.logger.info('Clicking "Tap to play" button...');
        await playButton.asElement().click();
        await this.page.waitForTimeout(3000);
        this.logger.info('Successfully clicked play button');
      } else {
        this.logger.warn('No "Tap to play" button found - live stream might already be playing');
      }

    } catch (error) {
      this.logger.warn('Error handling tap to play:', error.message);
    }
  }

  async verifyLiveStream(username) {
    try {
      this.logger.info('Verifying live stream is active...');

      // Check for live stream indicators
      const liveSelectors = [
        'video',
        '[data-testid="live-video-player"]',
        '[data-testid="live-badge"]',
        '[aria-label*="live" i]',
        '.live-indicator'
      ];

      let hasLiveStream = false;
      for (const selector of liveSelectors) {
        const element = await this.page.$(selector);
        if (element) {
          hasLiveStream = true;
          this.logger.info(`Found live stream indicator: ${selector}`);
          break;
        }
      }

      if (!hasLiveStream) {
        throw new Error(`Không tìm thấy live stream đang hoạt động cho người dùng "${username}". Vui lòng đảm bảo họ đang live hoặc thử lại sau.`);
      }

      this.logger.info('Live stream verified successfully');
    } catch (error) {
      this.logger.error('Failed to verify live stream:', error);
      throw error;
    }
  }

  async startCommentMonitoring() {
    try {
      this.logger.info('Starting comment monitoring...');

      // Ensure page is ready before starting monitoring
      if (!this.page || this.page.isClosed()) {
        throw new Error('Page is not ready for monitoring');
      }

      // Wait for page to be fully loaded
      try {
        await this.page.waitForFunction(() => document.readyState === 'complete', { timeout: 10000 });
      } catch (error) {
        this.logger.warn('Page ready state timeout, continuing anyway...');
      }

      // Additional wait to ensure page is stable
      await this.page.waitForTimeout(2000);

      // Setup API interception (only method now)
      await this.setupApiInterception();
      this.logger.info('Instagram API interception enabled - API-only comment detection');

      this.logger.info('Comment monitoring started');
    } catch (error) {
      this.logger.error('Failed to start comment monitoring:', error);
      throw error;
    }
  }

  async setupApiInterception() {
    try {
      this.logger.info('Setting up Instagram API interception...');
      console.log('🎯 SETTING UP API INTERCEPTION');

      // Setup response listener to "nghe lén" Instagram API calls
      this.page.on('response', async (response) => {
        const url = response.url();

        // Detect Instagram Live comment API calls
        if (url.includes('/get_comment/') && url.includes('last_comment_ts')) {
          console.log('🔍 DETECTED COMMENT API CALL:', url);

          try {
            // Check if response is successful
            if (response.status() === 200) {
              // "Nghe lén" API response data
              const apiData = await response.json();
              console.log('📦 API RESPONSE INTERCEPTED:', {
                status: apiData.status,
                commentCount: apiData.comments?.length || 0,
                hasMoreComments: apiData.has_more_comments
              });

              // Process comments from API
              await this.handleApiComments(apiData);
            } else {
              console.log('⚠️ API call failed with status:', response.status());
            }
          } catch (error) {
            console.log('❌ Failed to parse API response:', error.message);
          }
        }
      });

      // Also setup heartbeat API monitoring for connection status
      this.page.on('response', async (response) => {
        const url = response.url();

        if (url.includes('/heartbeat_and_get_viewer_count/')) {
          console.log('💓 HEARTBEAT API DETECTED');
          // Could track viewer count or connection status here if needed
        }
      });

      this.logger.info('API interception setup completed');
      console.log('✅ API INTERCEPTION READY - Waiting for Instagram API calls...');

    } catch (error) {
      this.logger.error('Failed to setup API interception:', error);
      console.error('❌ API INTERCEPTION SETUP FAILED:', error);
      throw error; // Fail completely if API interception fails
    }
  }

  async scrollToComments() {
    try {
      this.logger.info('Preparing comments area for live stream...');

      // For live streams, comments are usually already visible
      // Just ensure the page is properly loaded and comments area is accessible
      await this.page.waitForTimeout(2000);

      // Try to scroll to ensure comments are in view
      await this.page.evaluate(() => {
        // Scroll to bottom to see latest comments
        window.scrollTo(0, document.body.scrollHeight);
      });

      await this.page.waitForTimeout(1000);

      // Try to find and focus comment input (optional for live streams)
      const commentInputSelectors = [
        'textarea[placeholder*="comment" i]',
        'input[placeholder*="comment" i]',
        'textarea[aria-label*="comment" i]',
        'input[aria-label*="comment" i]',
        'textarea[placeholder*="Add a comment" i]',
        'input[placeholder*="Add a comment" i]'
      ];

      for (const selector of commentInputSelectors) {
        const input = await this.page.$(selector);
        if (input) {
          this.logger.info(`Found comment input: ${selector}`);
          try {
            await input.focus();
          } catch (error) {
            // Focus might fail, but that's okay
          }
          break;
        }
      }

      this.logger.info('Comments area prepared for monitoring');

    } catch (error) {
      this.logger.warn('Could not prepare comments section:', error);
    }
  }

  async handleApiComments(apiResponse) {
    try {
      // Validate API response
      if (!apiResponse || apiResponse.status !== 'ok' || !apiResponse.comments) {
        console.log('⚠️ Invalid API response:', apiResponse?.status || 'no status');
        return;
      }

      const apiComments = apiResponse.comments;
      console.log(`🔍 Processing ${apiComments.length} comments from API`);

      if (apiComments.length > 0) {
        this.apiCommentsReceived += apiComments.length;

        // Process each comment (no duplicate detection needed - API ensures uniqueness)
        apiComments.forEach(comment => {
          // Convert API format to app format
          const processedComment = {
            id: comment.pk,                                    // Use API's unique pk
            username: comment.user.username,
            text: comment.text,
            timestamp: new Date(comment.created_at * 1000).toISOString(), // Convert Unix timestamp
            source: 'instagram_api',                           // Mark as API source
            user_id: comment.user_id,
            full_name: comment.user.full_name,
            profile_pic_url: comment.user.profile_pic_url,
            raw_api_data: comment                              // Keep full API data for debugging
          };

          // Emit comment event (no tracking needed - API ensures uniqueness)
          this.emit('comment', processedComment);

          console.log(`✅ API COMMENT: ${comment.user.username}: ${comment.text} (ID: ${comment.pk})`);
        });

        // Log session stats
        this.logger.info(`API Comments: Processed ${apiComments.length}, ${this.apiCommentsReceived} total received`);
        console.log(`📊 SESSION STATS: ${this.apiCommentsReceived} API comments received`);

      } else {
        console.log('ℹ️ No new comments from API (all already processed)');
      }

    } catch (error) {
      this.logger.error('Error processing API comments:', error);
      console.error('❌ API COMMENT PROCESSING ERROR:', error);
    }
  }

  // DOM monitoring methods removed - using API interception only

  // Polling method removed - using API interception only

  // detectNewComments method removed - using API interception only



  // runFallbackExtraction method removed - using API interception only




  // extractComments method removed - using API interception only

  // Get current scraping mode (API only now)
  getScrapingMode() {
    return {
      useApiInterception: true, // Always true now
      apiCommentsReceived: this.apiCommentsReceived,
      mode: 'API_ONLY'
    };
  }

  async stop() {
    try {
      this.logger.info('Stopping Instagram scraper...');

      this.isRunning = false;

      // Cleanup MutationObserver
      if (this.page) {
        try {
          await this.page.evaluate(() => {
            if (window.commentObserver) {
              window.commentObserver.disconnect();
              window.commentObserver = null;
            }
          });
        } catch (error) {
          // Page might be closed already
        }

        await this.page.close();
        this.page = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }

      this.emit('disconnected');
      this.logger.info('Instagram scraper stopped');
    } catch (error) {
      this.logger.error('Error stopping Instagram scraper:', error);
      throw error;
    }
  }

  async restart(liveUrl, credentials) {
    try {
      await this.stop();
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      await this.start(liveUrl, credentials);
    } catch (error) {
      this.logger.error('Failed to restart Instagram scraper:', error);
      throw error;
    }
  }

  // Debug function to simulate API comments for testing (API-only approach)
  async addDebugComments(count = 1) {
    if (!this.isRunning) {
      throw new Error('Scraper must be running to add debug comments');
    }

    const debugUsernames = [
      'user_test_1', 'user_test_2', 'user_test_3', 'user_test_4', 'user_test_5',
      'debug_user_a', 'debug_user_b', 'debug_user_c', 'debug_user_d', 'debug_user_e',
      'test_customer_1', 'test_customer_2', 'test_customer_3', 'test_customer_4', 'test_customer_5',
      'fake_user_x', 'fake_user_y', 'fake_user_z', 'sample_user_1', 'sample_user_2'
    ];

    const debugComments = [
      'Đây là comment test 1', 'Comment thử nghiệm số 2', 'Test comment 3 với tiếng Việt',
      'Bao nhiêu tiền vậy shop?', 'Còn hàng không ạ?', 'Em muốn mua 1 cái',
      'Giá bao nhiêu vậy?', 'Shop có giao hàng không?', 'Màu nào đẹp nhất?',
      'Chất lượng thế nào?', 'Có bảo hành không?', 'Khi nào có hàng?',
      'Em đặt 2 cái nhé', 'Inbox em giá', 'Có size nào khác không?',
      'Shop có khuyến mãi gì không?', 'Hàng về khi nào ạ?', 'Chất liệu gì vậy shop?', 'Có ship nhanh không?',
      'Em đặt 1 cái màu xanh', 'Shop inbox em số điện thoại', 'Giá này đã bao gồm ship chưa?'
    ];

    try {
      console.log(`🧪 Generating ${count} debug API comments...`);

      const injectedComments = [];

      for (let i = 0; i < count; i++) {
        const username = debugUsernames[Math.floor(Math.random() * debugUsernames.length)];
        const text = debugComments[Math.floor(Math.random() * debugComments.length)];

        // Generate fake Instagram API pk (similar to real format)
        const fakePk = `debug_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        // Create fake API comment structure
        const fakeApiComment = {
          pk: fakePk,
          user: {
            username: username,
            full_name: `${username} (Debug)`,
            profile_pic_url: 'https://example.com/default.jpg'
          },
          user_id: Math.floor(Math.random() * 1000000),
          text: text,
          created_at: Math.floor(Date.now() / 1000), // Unix timestamp
          type: 0
        };

        // Create and emit debug comment (no duplicate tracking needed)
        const processedComment = {
          id: fakePk,
          username: username,
          text: text,
          timestamp: new Date().toISOString(),
          source: 'debug_api',
          user_id: fakeApiComment.user_id,
          full_name: fakeApiComment.user.full_name,
          profile_pic_url: fakeApiComment.user.profile_pic_url,
          raw_api_data: fakeApiComment,
          isDebug: true
        };

        // Emit comment event
        this.emit('comment', processedComment);

        injectedComments.push(processedComment);
        console.log(`🧪 DEBUG API COMMENT ${i + 1}/${count}: ${username}: ${text} (ID: ${fakePk})`);
      }

      console.log(`✅ Successfully generated ${count} debug API comments`);
      console.log('🔍 Comments processed through normal API flow');

      return injectedComments;

    } catch (error) {
      console.error('❌ Failed to generate debug API comments:', error);
      throw error;
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      apiCommentsReceived: this.apiCommentsReceived,
      browserConnected: !!this.browser,
      pageConnected: !!this.page,
      detectionMethod: 'api_only' // Indicate API-only detection
    };
  }

  // Method to reset API comment counter (useful for testing or when switching products)
  resetApiCounter() {
    this.apiCommentsReceived = 0;
    this.logger.info('API comment counter reset - API-only detection');
  }

  // Removed sync cleanup method - no cleanup needed, processed comments cache kept forever

  // Debug method to test comment detection
  async debugCommentDetection() {
    if (!this.page) {
      this.logger.warn('No page available for debugging');
      return;
    }

    try {
      this.logger.info('=== DEBUG: Manual comment detection test ===');

      const debugInfo = await this.page.evaluate(() => {
        const info = {
          url: window.location.href,
          title: document.title,
          apiInterceptionActive: true,
          detectionMethod: 'api_only'
        };

        // Get sample of div classes
        const divsWithClasses = Array.from(document.querySelectorAll('div')).filter(div => div.className).slice(0, 20);
        info.sampleClasses = divsWithClasses.map(div => div.className);

        // Look for potential comment structures
        const allDivs = document.querySelectorAll('div');
        const potentialComments = Array.from(allDivs).filter(div => {
          const spans = div.querySelectorAll('span[dir="auto"]');
          return spans.length >= 2;
        }).slice(0, 10);

        info.potentialCommentStructures = potentialComments.map(div => ({
          className: div.className,
          spanCount: div.querySelectorAll('span[dir="auto"]').length,
          spanTexts: Array.from(div.querySelectorAll('span[dir="auto"]')).map(span => ({
            text: span.textContent.trim().substring(0, 50),
            className: span.className
          })),
          innerHTML: div.innerHTML.substring(0, 500)
        }));

        // Check for x17y8kql elements specifically
        const x17y8kqlElements = document.querySelectorAll('div.x17y8kql');
        info.x17y8kqlDetails = Array.from(x17y8kqlElements).slice(0, 5).map(el => ({
          className: el.className,
          innerHTML: el.innerHTML.substring(0, 300),
          spans: el.querySelectorAll('span[dir="auto"]').length,
          spanTexts: Array.from(el.querySelectorAll('span[dir="auto"]')).map(span => span.textContent.trim())
        }));

        return info;
      });

      this.logger.info('=== API-ONLY DEBUG INFO ===');
      this.logger.info('URL:', debugInfo.url);
      this.logger.info('Title:', debugInfo.title);
      this.logger.info('Detection Method:', debugInfo.detectionMethod);
      this.logger.info('API Interception Active:', debugInfo.apiInterceptionActive);
      this.logger.info('API Comments Received:', this.apiCommentsReceived);

      this.logger.info('=== API INTERCEPTION STATUS ===');
      this.logger.info('- DOM scraping: DISABLED');
      this.logger.info('- API interception: ENABLED');
      this.logger.info('- Comment source: Instagram Live API only');
      this.logger.info('- Duplicate detection: DISABLED (API ensures uniqueness)');

    } catch (error) {
      this.logger.error('Debug comment detection failed:', error);
    }
  }

  // Method to manually trigger debug from external call
  async forceDebug() {
    this.logger.info('=== FORCE DEBUG TRIGGERED ===');
    await this.debugCommentDetection();
  }
}

module.exports = InstagramScraper;
