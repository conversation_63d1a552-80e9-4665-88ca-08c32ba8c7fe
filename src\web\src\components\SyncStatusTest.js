import React from 'react';
import SyncStatusIndicator from './SyncStatusIndicator';

const SyncStatusTest = () => {
  return (
    <div className="p-8 space-y-8 bg-gray-100 min-h-screen">
      <div className="bg-white rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-bold mb-6">Sync Status Indicator Test</h2>
        
        {/* Size Variations */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Size Variations</h3>
          <div className="flex items-center space-x-6 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <SyncStatusIndicator size="xs" showText={true} />
              <p className="text-xs mt-2">Extra Small</p>
            </div>
            <div className="text-center">
              <SyncStatusIndicator size="sm" showText={true} />
              <p className="text-xs mt-2">Small</p>
            </div>
            <div className="text-center">
              <SyncStatusIndicator size="md" showText={true} />
              <p className="text-xs mt-2">Medium</p>
            </div>
            <div className="text-center">
              <SyncStatusIndicator size="lg" showText={true} />
              <p className="text-xs mt-2">Large</p>
            </div>
          </div>
        </div>

        {/* Text Variations */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Text Variations</h3>
          <div className="flex items-center space-x-6 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <SyncStatusIndicator size="sm" showText={false} />
              <p className="text-xs mt-2">Icon Only</p>
            </div>
            <div className="text-center">
              <SyncStatusIndicator size="sm" showText={true} />
              <p className="text-xs mt-2">With Text</p>
            </div>
          </div>
        </div>

        {/* Type Variations */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Type Variations</h3>
          <div className="flex items-center space-x-6 p-4 bg-gray-50 rounded-lg">
            <div className="text-center">
              <SyncStatusIndicator type="general" size="sm" showText={true} />
              <p className="text-xs mt-2">General</p>
            </div>
            <div className="text-center">
              <SyncStatusIndicator type="customers" size="sm" showText={true} />
              <p className="text-xs mt-2">Customers</p>
            </div>
            <div className="text-center">
              <SyncStatusIndicator type="history" size="sm" showText={true} />
              <p className="text-xs mt-2">History</p>
            </div>
          </div>
        </div>

        {/* Usage Examples */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Usage Examples</h3>
          
          {/* Header Style */}
          <div className="p-4 bg-white border rounded-lg">
            <h4 className="text-sm font-medium mb-3">Header Style (History Page)</h4>
            <div className="flex items-center space-x-3">
              <h1 className="text-lg font-semibold text-gray-900">Lịch sử in</h1>
              <div className="flex items-center space-x-2 px-3 py-1 bg-gray-50 rounded-lg border">
                <SyncStatusIndicator 
                  type="history" 
                  size="sm" 
                  showText={true}
                />
              </div>
            </div>
          </div>

          {/* Footer Style */}
          <div className="p-4 bg-white border rounded-lg">
            <h4 className="text-sm font-medium mb-3">Footer Style</h4>
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-4">
                <span>Tổng cộng: 150 bình luận đã in</span>
                <span>•</span>
                <span>25 người dùng</span>
                <span>•</span>
                <div className="flex items-center">
                  <span className="mr-2">Atlas:</span>
                  <SyncStatusIndicator 
                    type="history" 
                    size="xs" 
                    showText={false}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Customer Item Style */}
          <div className="p-4 bg-white border rounded-lg">
            <h4 className="text-sm font-medium mb-3">Customer Item Style</h4>
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                <span className="text-yellow-600">★</span>
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <span className="font-semibold text-gray-900">@customer123</span>
                  <span className="text-sm text-gray-500">2 giờ trước</span>
                  <div className="flex items-center space-x-1 px-2 py-1 bg-green-50 rounded-full">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-xs text-green-700">Đã sync</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Comment Item Style */}
          <div className="p-4 bg-white border rounded-lg">
            <h4 className="text-sm font-medium mb-3">Comment Item Style</h4>
            <div className="space-y-2">
              <p className="text-sm text-gray-700">Đây là một bình luận mẫu đã được in</p>
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <span>15:30:25 25/06/2024</span>
                <span>Loại: comment</span>
                <div className="flex items-center space-x-1 text-green-600">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span>Atlas</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Integration Notes */}
        <div className="mt-8 p-4 bg-blue-50 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Integration Notes</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Component automatically checks MongoDB status every 30 seconds</li>
            <li>• Listens for real-time sync events via Socket.IO</li>
            <li>• Manual sync button appears for larger sizes when connected</li>
            <li>• Tooltips provide detailed status information</li>
            <li>• Responsive design adapts to different screen sizes</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SyncStatusTest;
