import React, { useState, useEffect } from 'react';
import { 
  Cloud, 
  CloudOff, 
  CheckCircle, 
  AlertCircle, 
  Loader, 
  RefreshCw,
  Database,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { getApiUrl } from '../config/api';

const SyncStatusIndicator = ({ 
  type = 'general', // 'general', 'customers', 'history'
  showText = true,
  size = 'sm', // 'xs', 'sm', 'md', 'lg'
  className = ''
}) => {
  const { socket, isConnected } = useSocket();
  const [syncStatus, setSyncStatus] = useState({
    isConnected: false,
    lastSync: null,
    syncing: false,
    error: null
  });
  const [lastSyncTime, setLastSyncTime] = useState(null);

  // Size configurations
  const sizeConfig = {
    xs: { icon: 'h-3 w-3', text: 'text-xs', spacing: 'space-x-1' },
    sm: { icon: 'h-4 w-4', text: 'text-sm', spacing: 'space-x-2' },
    md: { icon: 'h-5 w-5', text: 'text-base', spacing: 'space-x-2' },
    lg: { icon: 'h-6 w-6', text: 'text-lg', spacing: 'space-x-3' }
  };

  const config = sizeConfig[size] || sizeConfig.sm;

  // Check MongoDB connection status
  const checkMongoDBStatus = async () => {
    try {
      const response = await fetch(getApiUrl('/api/mongodb/status'));
      const data = await response.json();
      
      setSyncStatus(prev => ({
        ...prev,
        isConnected: data.connected,
        lastSync: data.lastSync,
        error: data.connected ? null : 'MongoDB không kết nối'
      }));

      if (data.lastSync) {
        setLastSyncTime(new Date(data.lastSync));
      }
    } catch (error) {
      setSyncStatus(prev => ({
        ...prev,
        isConnected: false,
        error: 'Lỗi kiểm tra kết nối'
      }));
    }
  };

  useEffect(() => {
    // Initial check
    checkMongoDBStatus();

    // Check every 30 seconds
    const interval = setInterval(checkMongoDBStatus, 30000);

    return () => clearInterval(interval);
  }, []);

  // Listen for sync events
  useEffect(() => {
    if (!socket) return;

    const handleSyncCompleted = (data) => {
      setSyncStatus(prev => ({
        ...prev,
        syncing: false,
        lastSync: data.timestamp,
        error: null
      }));
      setLastSyncTime(new Date(data.timestamp));
    };

    const handleSyncStarted = () => {
      setSyncStatus(prev => ({
        ...prev,
        syncing: true,
        error: null
      }));
    };

    const handleSyncError = (error) => {
      setSyncStatus(prev => ({
        ...prev,
        syncing: false,
        error: error.message || 'Lỗi đồng bộ'
      }));
    };

    socket.on('mongodb-sync-completed', handleSyncCompleted);
    socket.on('mongodb-sync-started', handleSyncStarted);
    socket.on('mongodb-sync-error', handleSyncError);

    return () => {
      socket.off('mongodb-sync-completed', handleSyncCompleted);
      socket.off('mongodb-sync-started', handleSyncStarted);
      socket.off('mongodb-sync-error', handleSyncError);
    };
  }, [socket]);

  // Get status info based on current state
  const getStatusInfo = () => {
    if (!isConnected) {
      return {
        icon: WifiOff,
        color: 'text-gray-400',
        bgColor: 'bg-gray-100',
        text: 'Mất kết nối',
        tooltip: 'Mất kết nối với server'
      };
    }

    if (!syncStatus.isConnected) {
      return {
        icon: CloudOff,
        color: 'text-red-500',
        bgColor: 'bg-red-50',
        text: 'Chưa sync',
        tooltip: 'MongoDB Atlas chưa kết nối'
      };
    }

    if (syncStatus.syncing) {
      return {
        icon: Loader,
        color: 'text-blue-500',
        bgColor: 'bg-blue-50',
        text: 'Đang sync...',
        tooltip: 'Đang đồng bộ dữ liệu',
        animate: 'animate-spin'
      };
    }

    if (syncStatus.error) {
      return {
        icon: AlertCircle,
        color: 'text-orange-500',
        bgColor: 'bg-orange-50',
        text: 'Lỗi sync',
        tooltip: syncStatus.error
      };
    }

    if (lastSyncTime) {
      const timeDiff = Date.now() - lastSyncTime.getTime();
      const isRecent = timeDiff < 10 * 60 * 1000; // 10 minutes

      return {
        icon: isRecent ? CheckCircle : Cloud,
        color: isRecent ? 'text-green-500' : 'text-blue-500',
        bgColor: isRecent ? 'bg-green-50' : 'bg-blue-50',
        text: 'Đã sync',
        tooltip: `Lần sync cuối: ${lastSyncTime.toLocaleString('vi-VN')}`
      };
    }

    return {
      icon: Cloud,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      text: 'Sẵn sàng',
      tooltip: 'MongoDB Atlas đã kết nối'
    };
  };

  const statusInfo = getStatusInfo();
  const IconComponent = statusInfo.icon;

  // Format last sync time for display
  const formatLastSync = () => {
    if (!lastSyncTime) return '';
    
    const now = new Date();
    const diff = now - lastSyncTime;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) return `${days} ngày trước`;
    if (hours > 0) return `${hours} giờ trước`;
    if (minutes > 0) return `${minutes} phút trước`;
    return 'Vừa xong';
  };

  const handleManualSync = async () => {
    try {
      setSyncStatus(prev => ({ ...prev, syncing: true }));
      
      const response = await fetch(getApiUrl('/api/mongodb/smart-sync'), {
        method: 'POST'
      });
      
      const data = await response.json();
      
      if (data.success) {
        setSyncStatus(prev => ({
          ...prev,
          syncing: false,
          lastSync: data.timestamp,
          error: null
        }));
        setLastSyncTime(new Date(data.timestamp));
      } else {
        throw new Error(data.error || 'Sync failed');
      }
    } catch (error) {
      setSyncStatus(prev => ({
        ...prev,
        syncing: false,
        error: error.message
      }));
    }
  };

  return (
    <div className={`inline-flex items-center ${config.spacing} ${className}`}>
      {/* Status Icon */}
      <div 
        className={`
          flex items-center justify-center rounded-full p-1
          ${statusInfo.bgColor} ${statusInfo.color}
          ${size === 'xs' ? 'p-0.5' : size === 'lg' ? 'p-2' : 'p-1'}
        `}
        title={statusInfo.tooltip}
      >
        <IconComponent 
          className={`${config.icon} ${statusInfo.animate || ''}`}
        />
      </div>

      {/* Status Text */}
      {showText && (
        <div className="flex flex-col">
          <span className={`${config.text} font-medium ${statusInfo.color}`}>
            {statusInfo.text}
          </span>
          {lastSyncTime && size !== 'xs' && (
            <span className="text-xs text-gray-500">
              {formatLastSync()}
            </span>
          )}
        </div>
      )}

      {/* Manual Sync Button (only for larger sizes) */}
      {size !== 'xs' && syncStatus.isConnected && !syncStatus.syncing && (
        <button
          onClick={handleManualSync}
          className="ml-2 p-1 text-gray-400 hover:text-blue-500 transition-colors"
          title="Đồng bộ thủ công"
        >
          <RefreshCw className="h-3 w-3" />
        </button>
      )}
    </div>
  );
};

export default SyncStatusIndicator;
