# 🖼️ Hướng dẫn in Bitmap cho tiếng Việt

## 🎯 Tại sao cần Bitmap Printing?

### ❌ **Vấn đề với Text Mode:**
- Máy in nhiệt **không hỗ trợ Unicode** trực tiếp
- Tiếng Việt có dấu **bị hiển thị sai** hoặc **không hiển thị**
- Chỉ in được **ASCII characters** (a-z, A-Z, 0-9)
- Font chữ **cố định**, không thể tùy chỉnh

### ✅ **Ưu điểm của Bitmap Mode:**
- **Hỗ trợ đầy đủ** tiếng Việt có dấu: áàảãạăắằẳẵặâấầẩẫậ...
- **Font chữ tùy chỉnh**: Arial, Times New Roman, Verdana...
- **Kích thước font** linh hoạt: 12px - 24px
- **Chất lượng in** sắc nét như hình ảnh
- **Layout control** ch<PERSON>h xác

## 🔧 Cách hoạt động

### **Quy trình Bitmap Printing:**
1. **Text → Canvas**: Render text lên HTML5 Canvas
2. **Canvas → Image**: Convert canvas thành PNG image
3. **Image → Bitmap**: Chuyển đổi thành bitmap đen trắng
4. **Bitmap → ESC/POS**: Encode thành ESC/POS bitmap commands
5. **ESC/POS → Printer**: Gửi commands đến máy in

### **Technical Stack:**
- **Canvas API**: Render text với font Unicode
- **Sharp**: Image processing và resize
- **ESC/POS**: Bitmap printing commands
- **Buffer**: Binary data handling

## ⚙️ Cài đặt Bitmap

### **Thông số cơ bản:**
```json
{
  "printMode": "bitmap",
  "bitmapSettings": {
    "width": 576,        // pixels (80mm = 576px @ 203 DPI)
    "fontSize": 16,      // pixel size
    "fontFamily": "Arial, sans-serif",
    "lineSpacing": 4,    // pixels between lines
    "padding": 10,       // margin pixels
    "dpi": 203          // HPRT TP80N DPI
  }
}
```

### **Tính toán kích thước:**
- **80mm paper** = 576 pixels @ 203 DPI
- **58mm paper** = 420 pixels @ 203 DPI
- **Formula**: `width_pixels = (width_mm / 25.4) * DPI`

### **Font sizes khuyến nghị cho giấy 80mm:**
- **Username**: 28-36px (bold) - Tên người dùng nổi bật
- **Nội dung comment**: 24-32px (normal) - Dễ đọc trên giấy 80mm
- **Timestamp**: 18-24px (normal) - Thông tin thời gian
- **Header/Footer**: 16-20px (normal/bold) - Thông tin phụ

### **Font sizes cũ (quá nhỏ cho 80mm):**
- **Tiêu đề**: 18-20px (bold) - Quá nhỏ
- **Nội dung**: 14-16px (normal) - Quá nhỏ
- **Footer**: 12-14px (small) - Quá nhỏ

## 🎨 Tùy chỉnh giao diện

### **Font families hỗ trợ:**
- **Arial**: Sans-serif, dễ đọc
- **Times New Roman**: Serif, formal
- **Courier New**: Monospace, code-like
- **Verdana**: Sans-serif, web-friendly

### **Layout control:**
```javascript
// Center align cho headers
if (line.includes('===') || line.includes('---')) {
  ctx.textAlign = 'center';
  ctx.fillText(line, width / 2, y);
}

// Left align cho content
else {
  ctx.textAlign = 'left';
  ctx.fillText(line, padding, y);
}
```

### **Vietnamese text example:**
```
================================
    INSTAGRAM LIVE ORDER
================================

Thời gian: 06/06/2024 18:10:42
--------------------------------
Khách hàng: @nguyenvana
Nội dung:
Đặt mua áo thun size M màu đỏ
Giá: 150,000đ
Địa chỉ: 123 Nguyễn Trãi, Hà Nội

Tổng tiền: 150,000đ
--------------------------------
Cảm ơn quý khách!
Instagram: @shop_thoi_trang
================================
```

## 🔌 Kết nối và in ấn

### **USB Printing:**
```javascript
// Generate bitmap
const bitmapData = await generateBitmapPrint(comment);

// Send via USB
const device = usb.findByIds(0x0fe6, 0x811e);
outEndpoint.transfer(bitmapData);
```

### **Network Printing:**
```javascript
// Generate bitmap
const bitmapData = await generateBitmapPrint(comment);

// Send via TCP socket
const client = new net.Socket();
client.connect(9100, '*************');
client.write(bitmapData);
```

### **System Printer:**
```javascript
// Generate clean PNG image
const canvas = createCanvas(width, height);
// ... render text ...
const imageBuffer = canvas.toBuffer('image/png');

// Save and print via Windows
await fs.writeFile('temp.png', imageBuffer);
exec(`rundll32.exe shimgvw.dll,ImageView_PrintTo "temp.png"`);
```

## 📐 ESC/POS Bitmap Commands

### **Bitmap header:**
```
GS v 0 m xL xH yL yH d1...dk

GS = 0x1D
v = 0x76
0 = normal mode
m = 0x00 (normal)
xL, xH = width in bytes (little endian)
yL, yH = height in pixels (little endian)
d1...dk = bitmap data
```

### **Pixel encoding:**
- **8 pixels = 1 byte**
- **Black pixel = 1 bit**
- **White pixel = 0 bit**
- **MSB first** (bit 7 = leftmost pixel)

### **Example encoding:**
```
Pixels: ●○●●○○●○ (black=●, white=○)
Binary: 10110010
Hex:    0xB2
```

## 🚀 Performance Tips

### **Optimization:**
- **Cache fonts**: Load font once, reuse
- **Batch processing**: Multiple comments in one bitmap
- **Compress images**: Use Sharp for optimization
- **Memory management**: Clean up canvas after use

### **Error handling:**
```javascript
try {
  const bitmapData = await generateBitmapPrint(comment);
  await printToUSB(bitmapData);
} catch (error) {
  // Fallback to text mode
  const textData = formatCommentForPrint(comment);
  await printToSystem(textData);
}
```

## 🔍 Debugging

### **Common issues:**
1. **Font not found**: Use system fonts only
2. **Canvas too small**: Calculate height properly
3. **Bitmap corruption**: Check byte alignment
4. **Print quality**: Adjust DPI settings

### **Debug tools:**
```javascript
// Save bitmap as PNG for inspection
const debugImage = canvas.toBuffer('image/png');
await fs.writeFile('debug.png', debugImage);

// Log bitmap dimensions
console.log(`Bitmap: ${width}x${height}, ${bitmapBytes.length} bytes`);
```

## 📋 Checklist

### **Setup checklist:**
- ✅ Canvas package installed
- ✅ Sharp package installed  
- ✅ Font family available
- ✅ DPI settings correct
- ✅ Paper width configured

### **Testing checklist:**
- ✅ ASCII text prints correctly
- ✅ Vietnamese text renders properly
- ✅ Font size appropriate
- ✅ Layout aligned correctly
- ✅ Cut commands work

### **Production checklist:**
- ✅ Error handling implemented
- ✅ Fallback to text mode
- ✅ Memory cleanup
- ✅ Performance optimized
- ✅ User documentation

## 🎯 Best Practices

### **For Vietnamese text:**
- Use **bitmap mode** always
- Choose **sans-serif fonts** (Arial, Verdana)
- Set **fontSize ≥ 14px** for readability
- Add **padding** for margins
- Test with **full character set**

### **For performance:**
- **Cache** bitmap generation
- **Reuse** canvas instances
- **Batch** multiple prints
- **Compress** large bitmaps
- **Clean up** temporary files

**Bitmap printing đảm bảo tiếng Việt hiển thị hoàn hảo trên máy in nhiệt!** 🇻🇳✨
