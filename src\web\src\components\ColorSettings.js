import React, { useState, useEffect } from 'react';
import { Pa<PERSON>, <PERSON>ota<PERSON>Cc<PERSON>, Eye } from 'lucide-react';
import toast from 'react-hot-toast';

const ColorSettings = () => {
  const [colorSettings, setColorSettings] = useState({
    newCustomer: {
      background: '#f3f4f6', // gray-100
      border: '#d1d5db',     // gray-300
      username: '#0ea5e9',   // sky-500
      text: '#374151'        // gray-700
    },
    regularCustomer: {
      background: '#fef3c7', // yellow-100
      border: '#f59e0b',     // yellow-500
      username: '#d97706',   // yellow-600
      text: '#374151'        // gray-700
    }
  });

  const [previewMode, setPreviewMode] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedColors = localStorage.getItem('instagram-live-color-settings');
    if (savedColors) {
      try {
        const parsedColors = JSON.parse(savedColors);
        setColorSettings(parsedColors);
      } catch (error) {
        console.error('Failed to parse saved color settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('instagram-live-color-settings', JSON.stringify(colorSettings));
    // Dispatch custom event to notify Comments component
    window.dispatchEvent(new CustomEvent('colorSettingsChanged', { 
      detail: colorSettings 
    }));
  }, [colorSettings]);

  const handleColorChange = (customerType, colorType, value) => {
    setColorSettings(prev => ({
      ...prev,
      [customerType]: {
        ...prev[customerType],
        [colorType]: value
      }
    }));
  };

  const resetToDefaults = () => {
    const defaultColors = {
      newCustomer: {
        background: '#f3f4f6',
        border: '#d1d5db',
        username: '#0ea5e9',
        text: '#374151'
      },
      regularCustomer: {
        background: '#fef3c7',
        border: '#f59e0b',
        username: '#d97706',
        text: '#374151'
      }
    };
    setColorSettings(defaultColors);
    toast.success('Đã khôi phục màu sắc mặc định');
  };

  const presetColors = [
    { name: 'Xanh dương', value: '#3b82f6' },
    { name: 'Xanh lá', value: '#10b981' },
    { name: 'Đỏ', value: '#ef4444' },
    { name: 'Vàng', value: '#f59e0b' },
    { name: 'Tím', value: '#8b5cf6' },
    { name: 'Hồng', value: '#ec4899' },
    { name: 'Xám', value: '#6b7280' },
    { name: 'Cam', value: '#f97316' }
  ];

  const ColorPicker = ({ label, value, onChange, presets = [] }) => (
    <div className="space-y-2">
      <label className="block text-sm font-medium text-gray-700">{label}</label>
      <div className="flex items-center space-x-3">
        <input
          type="color"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="w-12 h-8 border border-gray-300 rounded cursor-pointer"
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-sky-500"
          placeholder="#000000"
        />
      </div>
      {presets.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {presets.map((preset) => (
            <button
              key={preset.value}
              onClick={() => onChange(preset.value)}
              className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
              style={{ backgroundColor: preset.value }}
              title={preset.name}
            />
          ))}
        </div>
      )}
    </div>
  );

  const PreviewComment = ({ type, settings }) => (
    <div 
      className="flex items-start p-4 rounded border transition-colors"
      style={{
        backgroundColor: settings.background,
        borderColor: settings.border
      }}
    >
      <button className="p-1 rounded transition-colors flex-shrink-0 text-yellow-500 mr-3">
        {type === 'regular' ? '⭐' : '☆'}
      </button>
      <div className="flex-1 min-w-0 mr-4">
        <div className="flex items-center space-x-3 mb-2">
          <span 
            className="text-lg font-medium"
            style={{ color: settings.username }}
          >
            {type === 'regular' ? 'khach_quen123' : 'khach_moi456'}
          </span>
          <span className="text-base text-gray-500">14:30:25</span>
        </div>
        <p 
          className="text-lg break-words leading-relaxed"
          style={{ color: settings.text }}
        >
          {type === 'regular' 
            ? 'Cho mình 2 ly trà sữa size L nhé!' 
            : 'Mình muốn đặt 1 ly cà phê đen đá'
          }
        </p>
      </div>
      <div className="flex-shrink-0">
        <button className="px-4 py-2 bg-sky-500 text-white rounded-lg text-sm">
          In
        </button>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Palette className="h-5 w-5 text-gray-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-900">Cài đặt màu sắc bình luận</h3>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={() => setPreviewMode(!previewMode)}
            className={`px-3 py-1 text-sm rounded-md transition-colors ${
              previewMode
                ? 'bg-sky-100 text-sky-700'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Eye className="h-4 w-4 mr-1 inline" />
            Xem trước
          </button>
          <button
            onClick={resetToDefaults}
            className="px-3 py-1 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
          >
            <RotateCcw className="h-4 w-4 mr-1 inline" />
            Mặc định
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* New Customer Colors */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-800 border-b pb-2">Khách mới</h4>
          <ColorPicker
            label="Màu nền"
            value={colorSettings.newCustomer.background}
            onChange={(value) => handleColorChange('newCustomer', 'background', value)}
            presets={presetColors.map(c => ({ ...c, value: c.value + '20' }))} // Add transparency
          />
          <ColorPicker
            label="Màu viền"
            value={colorSettings.newCustomer.border}
            onChange={(value) => handleColorChange('newCustomer', 'border', value)}
            presets={presetColors}
          />
          <ColorPicker
            label="Màu tên người dùng"
            value={colorSettings.newCustomer.username}
            onChange={(value) => handleColorChange('newCustomer', 'username', value)}
            presets={presetColors}
          />
          <ColorPicker
            label="Màu nội dung"
            value={colorSettings.newCustomer.text}
            onChange={(value) => handleColorChange('newCustomer', 'text', value)}
            presets={presetColors}
          />
        </div>

        {/* Regular Customer Colors */}
        <div className="space-y-4">
          <h4 className="text-md font-medium text-gray-800 border-b pb-2">Khách quen</h4>
          <ColorPicker
            label="Màu nền"
            value={colorSettings.regularCustomer.background}
            onChange={(value) => handleColorChange('regularCustomer', 'background', value)}
            presets={presetColors.map(c => ({ ...c, value: c.value + '20' }))}
          />
          <ColorPicker
            label="Màu viền"
            value={colorSettings.regularCustomer.border}
            onChange={(value) => handleColorChange('regularCustomer', 'border', value)}
            presets={presetColors}
          />
          <ColorPicker
            label="Màu tên người dùng"
            value={colorSettings.regularCustomer.username}
            onChange={(value) => handleColorChange('regularCustomer', 'username', value)}
            presets={presetColors}
          />
          <ColorPicker
            label="Màu nội dung"
            value={colorSettings.regularCustomer.text}
            onChange={(value) => handleColorChange('regularCustomer', 'text', value)}
            presets={presetColors}
          />
        </div>
      </div>

      {/* Preview Section */}
      {previewMode && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="text-md font-medium text-gray-800 mb-4">Xem trước</h4>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600 mb-2">Khách mới:</p>
              <PreviewComment type="new" settings={colorSettings.newCustomer} />
            </div>
            <div>
              <p className="text-sm text-gray-600 mb-2">Khách quen:</p>
              <PreviewComment type="regular" settings={colorSettings.regularCustomer} />
            </div>
          </div>
        </div>
      )}

      <div className="mt-4 p-3 bg-blue-50 rounded-lg">
        <p className="text-sm text-blue-700">
          💡 <strong>Mẹo:</strong> Màu sắc sẽ được lưu tự động và áp dụng ngay lập tức cho tất cả bình luận.
        </p>
      </div>
    </div>
  );
};

export default ColorSettings;
