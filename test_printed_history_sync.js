const Database = require('./src/backend/services/Database');
const MongoDBService = require('./src/backend/services/MongoDBService');

// Simple logger for testing
const logger = {
  info: console.log,
  error: console.error,
  warn: console.warn
};

// Test printed history sync functionality
async function testPrintedHistorySync() {
  console.log('🧪 Testing Printed History Sync Functionality...\n');

  try {
    // Initialize services
    const database = new Database();
    const mongoDBService = new MongoDBService();

    // Get MongoDB connection string from database settings
    const connectionString = await database.getSetting('mongodb_connection_string');
    if (!connectionString) {
      console.log('❌ No MongoDB connection string found in settings');
      console.log('💡 Please connect to MongoDB through the web interface first');
      return;
    }

    // Connect to MongoDB
    await mongoDBService.connect(connectionString);
    console.log('✅ Connected to MongoDB Atlas');

    // Set global reference for auto-sync
    global.mongoDBService = mongoDBService;

    // Test 1: Create test printed history records
    console.log('\n📝 Test 1: Creating test printed history records...');
    
    const testComments = [
      { id: 'test_001', username: 'testuser1', text: 'Test comment 1', type: 'comment' },
      { id: 'test_002', username: 'testuser1', text: 'Test comment 2', type: 'comment' },
      { id: 'test_003', username: 'testuser2', text: 'Test comment 3', type: 'backup' },
      { id: 'test_004', username: 'testuser1', text: 'Test comment 1', type: 'comment' }, // Duplicate content
    ];

    for (const comment of testComments) {
      await database.markCommentAsPrinted(comment.id, comment.username, comment.text, comment.type);
      console.log(`  ✅ Created: ${comment.username} - ${comment.text}`);
    }

    // Test 2: Check local records
    console.log('\n📊 Test 2: Checking local printed history...');
    const localRecords = await database.getPrintedHistoryForSync();
    console.log(`  📋 Local records: ${localRecords.length}`);
    localRecords.forEach(record => {
      console.log(`    - ID:${record.id} ${record.username}: ${record.comment_text} (deleted: ${record.is_deleted})`);
    });

    // Test 3: Manual sync to MongoDB
    console.log('\n🔄 Test 3: Manual sync to MongoDB...');
    const mongoPrintedHistory = await mongoDBService.syncPrintedHistoryFromMongo();
    const syncResult = await database.smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory);
    console.log(`  📤 Sync result:`, syncResult);

    // Test 4: Check MongoDB records
    console.log('\n🌐 Test 4: Checking MongoDB records...');
    const mongoRecords = await mongoDBService.syncPrintedHistoryFromMongo();
    console.log(`  📋 MongoDB records: ${mongoRecords.length}`);
    mongoRecords.forEach(record => {
      console.log(`    - ${record.username}: ${record.comment_text} (deleted: ${record.is_deleted})`);
    });

    // Test 5: Bulk delete user records
    console.log('\n🗑️ Test 5: Testing bulk delete...');
    const deletedCount = await database.deleteUserPrintedHistory('testuser1');
    console.log(`  ✅ Bulk deleted ${deletedCount} records for testuser1`);

    // Test 6: Check sync after bulk delete
    console.log('\n🔄 Test 6: Sync after bulk delete...');
    const mongoPrintedHistory2 = await mongoDBService.syncPrintedHistoryFromMongo();
    const syncResult2 = await database.smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory2);
    console.log(`  📤 Sync result after delete:`, syncResult2);

    // Test 7: Verify MongoDB deletion
    console.log('\n✅ Test 7: Verifying MongoDB deletion...');
    const mongoRecordsAfterDelete = await mongoDBService.syncPrintedHistoryFromMongo();
    console.log(`  📋 MongoDB records after delete: ${mongoRecordsAfterDelete.length}`);
    mongoRecordsAfterDelete.forEach(record => {
      console.log(`    - ${record.username}: ${record.comment_text} (deleted: ${record.is_deleted})`);
    });

    // Test 8: Check for duplicate content handling
    console.log('\n🔍 Test 8: Checking duplicate content handling...');
    const testuser1Records = mongoRecordsAfterDelete.filter(r => r.username === 'testuser1');
    const duplicateContentRecords = testuser1Records.filter(r => r.comment_text === 'Test comment 1');
    console.log(`  📊 testuser1 records: ${testuser1Records.length}`);
    console.log(`  📊 Duplicate content records: ${duplicateContentRecords.length}`);
    
    if (duplicateContentRecords.length > 1) {
      const allDeleted = duplicateContentRecords.every(r => r.is_deleted === true);
      console.log(`  ✅ All duplicate content records deleted: ${allDeleted}`);
      if (!allDeleted) {
        console.log('  ❌ ISSUE: Not all duplicate content records were marked as deleted!');
        duplicateContentRecords.forEach((record, index) => {
          console.log(`    Record ${index + 1}: deleted=${record.is_deleted}, local_id=${record.local_id}`);
        });
      }
    }

    // Test 9: Cleanup test data
    console.log('\n🧹 Test 9: Cleaning up test data...');
    
    // Delete remaining test records
    const remainingRecords = await database.allQuery(
      'SELECT id FROM printed_history WHERE comment_id LIKE "test_%"'
    );
    
    for (const record of remainingRecords) {
      await database.deletePrintedHistoryRecord(record.id);
    }
    
    console.log(`  ✅ Cleaned up ${remainingRecords.length} test records`);

    // Final sync to clean MongoDB
    const mongoPrintedHistory3 = await mongoDBService.syncPrintedHistoryFromMongo();
    await database.smartSyncPrintedHistoryWithMongoDB(mongoPrintedHistory3);
    console.log('  ✅ Final sync completed');

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    throw error;
  }
}

// Run the test
if (require.main === module) {
  testPrintedHistorySync()
    .then(() => {
      console.log('\n✅ Test suite completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test suite failed:', error);
      process.exit(1);
    });
}

module.exports = { testPrintedHistorySync };
