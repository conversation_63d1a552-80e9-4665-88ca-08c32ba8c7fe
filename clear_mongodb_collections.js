#!/usr/bin/env node

/**
 * Clear MongoDB collections để đồng bộ với database reset
 */

const { MongoClient } = require('mongodb');
const sqlite3 = require('sqlite3').verbose();

// Database path
const DB_PATH = './src/backend/data/instagram_live.db';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

function queryDatabase(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

async function getMongoConnectionString() {
  try {
    const db = new sqlite3.Database(DB_PATH);
    
    try {
      const result = await queryDatabase(db, 'SELECT value FROM settings WHERE key = ?', ['mongodb_connection_string']);
      
      if (result.length === 0 || !result[0].value) {
        logError('MongoDB connection string not found in settings');
        return null;
      }
      
      return result[0].value;
    } finally {
      db.close();
    }
  } catch (error) {
    logError(`Failed to get MongoDB connection string: ${error.message}`);
    return null;
  }
}

async function clearMongoDBCollections() {
  log('🚀 Starting MongoDB collections cleanup...', 'bold');
  
  try {
    // Get MongoDB connection string
    const connectionString = await getMongoConnectionString();
    if (!connectionString) {
      logError('Cannot proceed without MongoDB connection string');
      return;
    }
    
    logInfo('Connecting to MongoDB...');
    const client = new MongoClient(connectionString);
    await client.connect();
    
    const db = client.db();
    
    try {
      // Get collections to clear
      const collectionsToCheck = ['printed_history', 'regular_customers', 'instagram_threads', 'send_once_history'];
      
      // Get existing collections
      const collections = await db.listCollections().toArray();
      const existingCollectionNames = collections.map(c => c.name);
      
      logInfo('Existing collections:');
      existingCollectionNames.forEach(name => {
        log(`  - ${name}`, 'blue');
      });
      
      // Clear printed_history collection (main target)
      if (existingCollectionNames.includes('printed_history')) {
        const printedHistoryCount = await db.collection('printed_history').countDocuments();
        logWarning(`Found ${printedHistoryCount} documents in printed_history collection`);
        
        if (printedHistoryCount > 0) {
          await db.collection('printed_history').deleteMany({});
          logSuccess(`Cleared ${printedHistoryCount} documents from printed_history collection`);
        } else {
          logInfo('printed_history collection is already empty');
        }
      } else {
        logInfo('printed_history collection does not exist');
      }
      
      // Check other collections (don't clear, just report)
      for (const collectionName of ['regular_customers', 'instagram_threads', 'send_once_history']) {
        if (existingCollectionNames.includes(collectionName)) {
          const count = await db.collection(collectionName).countDocuments();
          logInfo(`${collectionName}: ${count} documents (preserved)`);
        } else {
          logInfo(`${collectionName}: collection does not exist`);
        }
      }
      
      // Update sync timestamps to force fresh sync
      const db_sqlite = new sqlite3.Database(DB_PATH);
      try {
        await new Promise((resolve, reject) => {
          db_sqlite.run(
            'UPDATE settings SET value = ? WHERE key = ?',
            [new Date().toISOString(), 'mongodb_last_sync'],
            function(error) {
              if (error) reject(error);
              else resolve();
            }
          );
        });
        logSuccess('Updated MongoDB last sync timestamp');
      } finally {
        db_sqlite.close();
      }
      
      log('\n=== CLEANUP SUMMARY ===', 'bold');
      logSuccess('MongoDB printed_history collection cleared');
      logSuccess('Other collections preserved');
      logSuccess('Sync timestamps updated');
      logInfo('Ready for fresh sync with new schema');
      
    } finally {
      await client.close();
    }
    
  } catch (error) {
    logError(`MongoDB cleanup failed: ${error.message}`);
    throw error;
  }
}

// Confirmation prompt
async function confirmClear() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  return new Promise((resolve) => {
    rl.question('Clear MongoDB printed_history collection? This will delete all cloud backup! (yes/no): ', (answer) => {
      rl.close();
      resolve(answer.toLowerCase() === 'yes' || answer.toLowerCase() === 'y');
    });
  });
}

// Main execution
async function main() {
  try {
    logWarning('⚠️  MONGODB CLEANUP WARNING ⚠️');
    logWarning('This will delete all printed_history documents from MongoDB Atlas!');
    logInfo('Local database has been reset and backup created.');
    
    const confirmed = await confirmClear();
    
    if (!confirmed) {
      logInfo('Cleanup cancelled by user.');
      process.exit(0);
    }
    
    await clearMongoDBCollections();
    
  } catch (error) {
    logError(`Cleanup script failed: ${error.message}`);
    process.exit(1);
  }
}

main();
