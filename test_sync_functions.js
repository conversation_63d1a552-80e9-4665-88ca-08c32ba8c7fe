#!/usr/bin/env node

/**
 * Test script để kiểm tra các chức năng sync của MongoDB
 * Kiểm tra: printed_history, threads, send_once_history
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function makeRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      timeout: 30000
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.error || error.message,
      status: error.response?.status
    };
  }
}

async function checkMongoDBConnection() {
  logInfo('Checking MongoDB connection...');
  const result = await makeRequest('GET', '/api/mongodb/status');
  
  if (result.success && result.data.connected) {
    logSuccess('MongoDB is connected');
    return true;
  } else {
    logError('MongoDB is not connected');
    if (result.data) {
      console.log('Status:', result.data);
    }
    return false;
  }
}

async function testPrintedHistorySync() {
  log('\n=== TESTING PRINTED HISTORY SYNC ===', 'bold');
  
  // Test manual sync
  logInfo('Testing manual printed history sync...');
  const syncResult = await makeRequest('POST', '/api/mongodb/sync-printed-history');
  
  if (syncResult.success) {
    logSuccess('Manual printed history sync completed');
    console.log('Sync result:', syncResult.data);
  } else {
    logError(`Manual sync failed: ${syncResult.error}`);
  }
  
  return syncResult.success;
}

async function testThreadSync() {
  log('\n=== TESTING THREAD SYNC ===', 'bold');
  
  // Test thread sync from MongoDB
  logInfo('Testing thread sync from MongoDB...');
  const syncResult = await makeRequest('POST', '/api/instagram-threads/sync-from-mongo');
  
  if (syncResult.success) {
    logSuccess('Thread sync from MongoDB completed');
    console.log('Sync result:', syncResult.data);
  } else {
    logError(`Thread sync failed: ${syncResult.error}`);
  }
  
  return syncResult.success;
}

async function testSendOnceHistorySync() {
  log('\n=== TESTING SEND_ONCE HISTORY SYNC ===', 'bold');
  
  // Test smart sync (includes send_once history)
  logInfo('Testing smart sync (includes send_once history)...');
  const syncResult = await makeRequest('POST', '/api/mongodb/smart-sync');
  
  if (syncResult.success) {
    logSuccess('Smart sync completed (includes send_once history)');
    console.log('Send_once sync result:', syncResult.data.sendOnceHistory);
  } else {
    logError(`Smart sync failed: ${syncResult.error}`);
  }
  
  return syncResult.success;
}

async function testDebugCounts() {
  log('\n=== TESTING DEBUG COUNTS ===', 'bold');
  
  logInfo('Getting debug counts...');
  const result = await makeRequest('GET', '/api/mongodb/debug-counts');
  
  if (result.success) {
    logSuccess('Debug counts retrieved');
    console.log('Counts:', result.data);
  } else {
    logError(`Debug counts failed: ${result.error}`);
  }
  
  return result.success;
}

async function testBooleanConversion() {
  log('\n=== TESTING BOOLEAN CONVERSION ===', 'bold');
  
  logInfo('Testing boolean conversion...');
  const result = await makeRequest('GET', '/api/mongodb/debug-boolean-conversion');
  
  if (result.success) {
    logSuccess('Boolean conversion test completed');
    console.log('Conversion test:', result.data);
  } else {
    logError(`Boolean conversion test failed: ${result.error}`);
  }
  
  return result.success;
}

async function runAllTests() {
  log('🚀 Starting MongoDB Sync Function Tests', 'bold');
  
  // Check MongoDB connection first
  const isConnected = await checkMongoDBConnection();
  if (!isConnected) {
    logError('Cannot run tests without MongoDB connection');
    process.exit(1);
  }
  
  const results = {
    printedHistory: false,
    threads: false,
    sendOnceHistory: false,
    debugCounts: false,
    booleanConversion: false
  };
  
  // Run all tests
  results.printedHistory = await testPrintedHistorySync();
  results.threads = await testThreadSync();
  results.sendOnceHistory = await testSendOnceHistorySync();
  results.debugCounts = await testDebugCounts();
  results.booleanConversion = await testBooleanConversion();
  
  // Summary
  log('\n=== TEST SUMMARY ===', 'bold');
  const passed = Object.values(results).filter(r => r).length;
  const total = Object.keys(results).length;
  
  Object.entries(results).forEach(([test, passed]) => {
    if (passed) {
      logSuccess(`${test}: PASSED`);
    } else {
      logError(`${test}: FAILED`);
    }
  });
  
  log(`\nOverall: ${passed}/${total} tests passed`, passed === total ? 'green' : 'red');
  
  if (passed === total) {
    logSuccess('All sync functions are working correctly! 🎉');
  } else {
    logWarning('Some sync functions need attention');
  }
}

// Run tests
runAllTests().catch(error => {
  logError(`Test runner failed: ${error.message}`);
  process.exit(1);
});
