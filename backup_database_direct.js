#!/usr/bin/env node

/**
 * Direct database backup script
 * Backup dữ liệu tr<PERSON><PERSON> tiếp từ SQLite database
 */

const sqlite3 = require('sqlite3').verbose();
const fs = require('fs').promises;
const path = require('path');

// Database path
const DB_PATH = './src/backend/data/instagram_live.db';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function createBackupDirectory() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = `database_backup_${timestamp}`;
  
  try {
    await fs.mkdir(backupDir, { recursive: true });
    logSuccess(`Created backup directory: ${backupDir}`);
    return backupDir;
  } catch (error) {
    logError(`Failed to create backup directory: ${error.message}`);
    throw error;
  }
}

async function saveToFile(filePath, data, description) {
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
    logSuccess(`${description} saved to ${filePath} (${data.length} records)`);
    return true;
  } catch (error) {
    logError(`Failed to save ${description}: ${error.message}`);
    return false;
  }
}

function queryDatabase(db, sql, params = []) {
  return new Promise((resolve, reject) => {
    db.all(sql, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

async function backupTable(db, tableName, backupDir) {
  try {
    logInfo(`Backing up table: ${tableName}`);
    
    // Check if table exists
    const tableExists = await queryDatabase(db, 
      "SELECT name FROM sqlite_master WHERE type='table' AND name=?", 
      [tableName]
    );
    
    if (tableExists.length === 0) {
      logWarning(`Table ${tableName} does not exist`);
      return false;
    }
    
    // Get all data from table
    const data = await queryDatabase(db, `SELECT * FROM ${tableName}`);
    
    // Save to file
    const filePath = path.join(backupDir, `${tableName}.json`);
    await saveToFile(filePath, data, `Table ${tableName}`);
    
    return true;
  } catch (error) {
    logError(`Failed to backup table ${tableName}: ${error.message}`);
    return false;
  }
}

async function backupSettings(db, backupDir) {
  try {
    logInfo('Backing up settings...');
    
    const settings = await queryDatabase(db, 'SELECT * FROM settings');
    
    // Convert to key-value format
    const settingsObj = {};
    settings.forEach(setting => {
      settingsObj[setting.key] = setting.value;
    });
    
    const filePath = path.join(backupDir, 'settings_formatted.json');
    await fs.writeFile(filePath, JSON.stringify(settingsObj, null, 2), 'utf8');
    logSuccess(`Settings saved to ${filePath} (${settings.length} settings)`);
    
    return true;
  } catch (error) {
    logError(`Failed to backup settings: ${error.message}`);
    return false;
  }
}

async function getTableStats(db, backupDir) {
  try {
    logInfo('Getting table statistics...');
    
    // Get all tables
    const tables = await queryDatabase(db, 
      "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
    );
    
    const stats = {};
    
    for (const table of tables) {
      try {
        const count = await queryDatabase(db, `SELECT COUNT(*) as count FROM ${table.name}`);
        stats[table.name] = count[0].count;
      } catch (error) {
        stats[table.name] = `Error: ${error.message}`;
      }
    }
    
    const filePath = path.join(backupDir, 'table_stats.json');
    await fs.writeFile(filePath, JSON.stringify(stats, null, 2), 'utf8');
    logSuccess(`Table stats saved to ${filePath}`);
    
    return stats;
  } catch (error) {
    logError(`Failed to get table stats: ${error.message}`);
    return {};
  }
}

async function runDatabaseBackup() {
  log('🚀 Starting direct database backup...', 'bold');
  
  try {
    // Check if database exists
    try {
      await fs.access(DB_PATH);
    } catch (error) {
      logError(`Database file not found: ${DB_PATH}`);
      return;
    }
    
    // Create backup directory
    const backupDir = await createBackupDirectory();
    
    // Open database
    const db = new sqlite3.Database(DB_PATH);
    
    try {
      // Get table stats first
      const stats = await getTableStats(db, backupDir);
      logInfo('Table statistics:');
      Object.entries(stats).forEach(([table, count]) => {
        log(`  ${table}: ${count} records`, 'blue');
      });
      
      // Tables to backup
      const tablesToBackup = [
        'regular_customers',
        'instagram_threads', 
        'send_once_history',
        'templates',
        'settings',
        'printed_history'
      ];
      
      // Backup each table
      const results = [];
      for (const table of tablesToBackup) {
        const result = await backupTable(db, table, backupDir);
        results.push(result);
      }
      
      // Backup settings in formatted way
      await backupSettings(db, backupDir);
      
      // Copy entire database file
      const dbBackupPath = path.join(backupDir, 'database_full.db');
      await fs.copyFile(DB_PATH, dbBackupPath);
      logSuccess(`Full database copied to ${dbBackupPath}`);
      
      // Create summary
      const summary = {
        backup_timestamp: new Date().toISOString(),
        backup_reason: 'Pre-reset backup for database restructure',
        original_db_path: DB_PATH,
        tables_backed_up: tablesToBackup.length,
        successful_backups: results.filter(r => r).length,
        table_stats: stats,
        backup_directory: backupDir
      };
      
      const summaryPath = path.join(backupDir, 'backup_summary.json');
      await fs.writeFile(summaryPath, JSON.stringify(summary, null, 2), 'utf8');
      logSuccess(`Backup summary saved to ${summaryPath}`);
      
      log(`\n=== BACKUP SUMMARY ===`, 'bold');
      log(`Backup directory: ${backupDir}`, 'blue');
      log(`Tables backed up: ${results.filter(r => r).length}/${tablesToBackup.length}`, 'green');
      log(`Total printed_history records: ${stats.printed_history || 'unknown'}`, 'yellow');
      
      logSuccess('Database backup completed! 🎉');
      
      return backupDir;
      
    } finally {
      // Close database
      db.close();
    }
    
  } catch (error) {
    logError(`Database backup failed: ${error.message}`);
    throw error;
  }
}

// Run backup
runDatabaseBackup().catch(error => {
  logError(`Backup script failed: ${error.message}`);
  process.exit(1);
});
