const EventEmitter = require('events');
const winston = require('winston');

class CommentProcessor extends EventEmitter {
  constructor(database) {
    super();
    this.database = database;
    this.processedComments = new Set();
    this.duplicateThreshold = 60000; // 1 minute
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.simple(),
      transports: [new winston.transports.Console()]
    });
  }

  async processComment(comment) {
    try {
      // Check for duplicates
      if (await this.isDuplicate(comment)) {
        this.emit('duplicate-comment', comment);
        return;
      }

      // REMOVED: saveComment to database - comments table no longer exists
      // Comments are only stored in session memory for performance optimization

      this.emit('comment-processed', comment);

      this.logger.info(`Comment processed: ${comment.id} from ${comment.username}`);
    } catch (error) {
      this.logger.error('Failed to process comment:', error);
      this.emit('error', error);
    }
  }

  async isDuplicate(comment) {
    try {
      // FIXED: Comments table no longer exists - use in-memory duplicate detection
      // Check if we've seen this exact comment recently using processedComments Set
      const commentKey = `${comment.username}:${comment.text}`;

      if (this.processedComments.has(commentKey)) {
        return true;
      }

      // Add to processed comments set - NO CLEANUP, unlimited like Instagram Live
      this.processedComments.add(commentKey);

      return false;
    } catch (error) {
      this.logger.error('Error checking for duplicates:', error);
      return false;
    }
  }

  // Removed comment analysis - not needed for simple template messaging

  // Removed order and price inquiry processing - not needed

  async getMessageTemplate(templateName) {
    try {
      return await this.database.getQuery(
        'SELECT * FROM message_templates WHERE name = ? AND is_active = TRUE',
        [templateName]
      );
    } catch (error) {
      this.logger.error('Failed to get message template:', error);
      return null;
    }
  }

  // Removed manual order and custom message methods - not needed for simple template messaging

  getProcessingStats() {
    return {
      processedCount: this.processedComments.size,
      duplicateThreshold: this.duplicateThreshold
    };
  }
}

module.exports = CommentProcessor;
