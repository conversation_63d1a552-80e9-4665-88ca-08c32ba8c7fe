const EventEmitter = require('events');
const winston = require('winston');

class CommentProcessor extends EventEmitter {
  constructor(database) {
    super();
    this.database = database;
    // No duplicate detection needed with API-only approach
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.simple(),
      transports: [new winston.transports.Console()]
    });
  }

  async processComment(comment) {
    try {
      // No duplicate detection needed with API-only approach
      // API ensures each comment is unique

      // REMOVED: saveComment to database - comments table no longer exists
      // Comments are only stored in session memory for performance optimization

      this.emit('comment-processed', comment);

      this.logger.info(`API comment processed: ${comment.id} from ${comment.username} (source: Instagram Live API)`);
    } catch (error) {
      this.logger.error('Failed to process comment:', error);
      this.emit('error', error);
    }
  }

  // isDuplicate method removed - not needed with API-only approach

  // Removed comment analysis - not needed for simple template messaging

  // Removed order and price inquiry processing - not needed

  async getMessageTemplate(templateName) {
    try {
      return await this.database.getQuery(
        'SELECT * FROM message_templates WHERE name = ? AND is_active = TRUE',
        [templateName]
      );
    } catch (error) {
      this.logger.error('Failed to get message template:', error);
      return null;
    }
  }

  // Removed manual order and custom message methods - not needed for simple template messaging

  getProcessingStats() {
    return {
      detectionMethod: 'api_only',
      duplicateDetection: 'disabled',
      commentSource: 'instagram_live_api'
    };
  }
}

module.exports = CommentProcessor;
