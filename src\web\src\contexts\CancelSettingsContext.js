import React, { createContext, useContext, useState, useEffect } from 'react';

const CancelSettingsContext = createContext();

export const useCancelSettings = () => {
  const context = useContext(CancelSettingsContext);
  if (!context) {
    throw new Error('useCancelSettings must be used within a CancelSettingsProvider');
  }
  return context;
};

export const CancelSettingsProvider = ({ children }) => {
  const [settings, setSettings] = useState({
    enabled: true,           // Enable/disable cancel functionality
    duration: 1500,         // Duration in milliseconds (1.5 seconds default)
    enableForPrint: true,   // Enable cancel for print button
    enableForBackup: true   // Enable cancel for backup button
  });
  const [isLoaded, setIsLoaded] = useState(false);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('cancelSettings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Failed to parse cancel settings:', error);
      }
    }
    setIsLoaded(true);
  }, []);

  // Save settings to localStorage whenever they change (but only after initial load)
  useEffect(() => {
    if (isLoaded) {
      localStorage.setItem('cancelSettings', JSON.stringify(settings));
    }
  }, [settings, isLoaded]);

  const updateSettings = (newSettings) => {
    setSettings(prev => ({ ...prev, ...newSettings }));
  };

  const resetToDefaults = () => {
    setSettings({
      enabled: true,
      duration: 1500,
      enableForPrint: true,
      enableForBackup: true
    });
  };

  // Helper function to check if cancel should be enabled for specific action
  const isCancelEnabled = (action = 'both') => {
    if (!settings.enabled) return false;
    
    switch (action) {
      case 'print':
        return settings.enableForPrint;
      case 'backup':
        return settings.enableForBackup;
      case 'both':
      default:
        return settings.enableForPrint || settings.enableForBackup;
    }
  };

  const value = {
    settings,
    updateSettings,
    resetToDefaults,
    isCancelEnabled
  };

  return (
    <CancelSettingsContext.Provider value={value}>
      {children}
    </CancelSettingsContext.Provider>
  );
};
