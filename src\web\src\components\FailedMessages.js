import React, { useState, useEffect } from 'react';
import toast from 'react-hot-toast';
import { Trash2, RotateCcw, CheckCircle } from 'lucide-react';

const FailedMessages = () => {
  const [failedMessages, setFailedMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [resolving, setResolving] = useState({});
  const [bulkOperating, setBulkOperating] = useState(false);

  useEffect(() => {
    fetchFailedMessages();
  }, []);

  const fetchFailedMessages = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/failed-messages');
      const data = await response.json();

      if (data.success) {
        setFailedMessages(data.failedMessages);
      } else {
        toast.error('Lỗi khi tải danh sách tin nhắn thất bại');
      }
    } catch (error) {
      console.error('Error fetching failed messages:', error);
      toast.error('Lỗi khi tải danh sách tin nhắn thất bại');
    } finally {
      setLoading(false);
    }
  };

  const handleResolve = async (messageId, notes = '') => {
    try {
      setResolving(prev => ({ ...prev, [messageId]: true }));

      const response = await fetch(`/api/failed-messages/${messageId}/resolve`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          resolvedBy: 'admin',
          notes: notes
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã đánh dấu tin nhắn đã xử lý');
        // Remove from list
        setFailedMessages(prev => prev.filter(msg => msg.id !== messageId));
      } else {
        toast.error('Lỗi khi đánh dấu tin nhắn đã xử lý');
      }
    } catch (error) {
      console.error('Error resolving failed message:', error);
      toast.error('Lỗi khi đánh dấu tin nhắn đã xử lý');
    } finally {
      setResolving(prev => ({ ...prev, [messageId]: false }));
    }
  };

  const handleDelete = async (messageId) => {
    if (!window.confirm('Bạn có chắc chắn muốn xóa tin nhắn thất bại này?')) {
      return;
    }

    try {
      setResolving(prev => ({ ...prev, [messageId]: true }));

      const response = await fetch(`/api/failed-messages/${messageId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Đã xóa tin nhắn thất bại');
        // Remove from list
        setFailedMessages(prev => prev.filter(msg => msg.id !== messageId));
      } else {
        toast.error('Lỗi khi xóa tin nhắn thất bại');
      }
    } catch (error) {
      console.error('Error deleting failed message:', error);
      toast.error('Lỗi khi xóa tin nhắn thất bại');
    } finally {
      setResolving(prev => ({ ...prev, [messageId]: false }));
    }
  };

  const handleRetry = async (messageId) => {
    try {
      setResolving(prev => ({ ...prev, [messageId]: true }));

      const response = await fetch(`/api/failed-messages/${messageId}/retry`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Tin nhắn đã được thêm vào hàng đợi để thử lại');
        // Remove from list
        setFailedMessages(prev => prev.filter(msg => msg.id !== messageId));
      } else {
        toast.error('Lỗi khi thử lại tin nhắn');
      }
    } catch (error) {
      console.error('Error retrying failed message:', error);
      toast.error('Lỗi khi thử lại tin nhắn');
    } finally {
      setResolving(prev => ({ ...prev, [messageId]: false }));
    }
  };

  const handleDeleteAll = async () => {
    if (!window.confirm(`Bạn có chắc chắn muốn xóa tất cả ${failedMessages.length} tin nhắn thất bại?`)) {
      return;
    }

    try {
      setBulkOperating(true);

      const response = await fetch('/api/failed-messages/bulk/all', {
        method: 'DELETE'
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Đã xóa ${data.deletedCount} tin nhắn thất bại`);
        setFailedMessages([]); // Clear all messages
      } else {
        toast.error('Lỗi khi xóa tất cả tin nhắn thất bại');
      }
    } catch (error) {
      console.error('Error deleting all failed messages:', error);
      toast.error('Lỗi khi xóa tất cả tin nhắn thất bại');
    } finally {
      setBulkOperating(false);
    }
  };

  const handleRetryAll = async () => {
    if (!window.confirm(`Bạn có chắc chắn muốn thử lại tất cả ${failedMessages.length} tin nhắn thất bại?`)) {
      return;
    }

    try {
      setBulkOperating(true);

      const response = await fetch('/api/failed-messages/bulk/retry-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const data = await response.json();

      if (data.success) {
        toast.success(`Đã thử lại ${data.successCount} tin nhắn${data.errorCount > 0 ? `, ${data.errorCount} lỗi` : ''}`);
        setFailedMessages([]); // Clear all messages since they're moved to queue
      } else {
        toast.error('Lỗi khi thử lại tất cả tin nhắn thất bại');
      }
    } catch (error) {
      console.error('Error retrying all failed messages:', error);
      toast.error('Lỗi khi thử lại tất cả tin nhắn thất bại');
    } finally {
      setBulkOperating(false);
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Đang tải...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Tin nhắn thất bại ({failedMessages.length})
        </h3>
        <div className="flex items-center space-x-2">
          {failedMessages.length > 0 && (
            <>
              {/* Retry All Button */}
              <button
                onClick={handleRetryAll}
                disabled={bulkOperating}
                className="inline-flex items-center px-3 py-1 rounded text-sm font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors disabled:opacity-50"
                title="Thử lại tất cả tin nhắn thất bại"
              >
                {bulkOperating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                ) : (
                  <RotateCcw className="h-4 w-4 mr-2" />
                )}
                Thử lại tất cả
              </button>

              {/* Delete All Button */}
              <button
                onClick={handleDeleteAll}
                disabled={bulkOperating}
                className="inline-flex items-center px-3 py-1 rounded text-sm font-medium bg-red-100 text-red-800 hover:bg-red-200 transition-colors disabled:opacity-50"
                title="Xóa tất cả tin nhắn thất bại"
              >
                {bulkOperating ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-600 mr-2"></div>
                ) : (
                  <Trash2 className="h-4 w-4 mr-2" />
                )}
                Xóa tất cả
              </button>
            </>
          )}

          {/* Refresh Button */}
          <button
            onClick={fetchFailedMessages}
            disabled={bulkOperating}
            className="inline-flex items-center px-3 py-1 rounded text-sm font-medium bg-gray-100 text-gray-700 hover:bg-gray-200 transition-colors disabled:opacity-50"
          >
            Làm mới
          </button>
        </div>
      </div>

      {failedMessages.length === 0 ? (
        <div className="text-center py-8 text-gray-500">
          <div className="text-4xl mb-2">✅</div>
          <p>Không có tin nhắn thất bại nào</p>
        </div>
      ) : (
        <div className="space-y-3">
          {failedMessages.map((message) => (
            <div
              key={message.id}
              className="bg-red-50 border border-red-200 rounded-lg p-4"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="font-medium text-red-900">
                      @{message.username}
                    </span>
                    <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                      {message.customer_type}
                    </span>
                  </div>

                  <div className="text-sm text-gray-700 mb-2">
                    <strong>Bình luận:</strong> {message.original_comment}
                  </div>

                  <div className="text-sm text-red-700 mb-2">
                    <strong>Lỗi:</strong> {message.error_message}
                  </div>

                  <div className="text-xs text-gray-500">
                    <span>Thời gian: {formatDate(message.failed_at)}</span>
                    {message.search_attempted && (
                      <span className="ml-4">
                        Tìm kiếm: @{message.search_attempted}
                      </span>
                    )}
                    <span className="ml-4">
                      Thử lại: {message.retry_count}/{message.max_retries}
                    </span>
                  </div>
                </div>

                <div className="ml-4 flex flex-col space-y-2">
                  {/* Retry Button */}
                  <button
                    onClick={() => handleRetry(message.id)}
                    disabled={resolving[message.id]}
                    className="inline-flex items-center px-3 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors disabled:opacity-50"
                    title="Thử lại gửi tin nhắn"
                  >
                    {resolving[message.id] ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 mr-1"></div>
                    ) : (
                      <RotateCcw className="h-3 w-3 mr-1" />
                    )}
                    Thử lại
                  </button>

                  {/* Delete Button */}
                  <button
                    onClick={() => handleDelete(message.id)}
                    disabled={resolving[message.id]}
                    className="inline-flex items-center px-3 py-1 rounded text-xs font-medium bg-red-100 text-red-800 hover:bg-red-200 transition-colors disabled:opacity-50"
                    title="Xóa tin nhắn thất bại"
                  >
                    {resolving[message.id] ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-1"></div>
                    ) : (
                      <Trash2 className="h-3 w-3 mr-1" />
                    )}
                    Xóa
                  </button>

                  {/* Resolve Button */}
                  <button
                    onClick={() => handleResolve(message.id, 'Đã xử lý thủ công')}
                    disabled={resolving[message.id]}
                    className="inline-flex items-center px-3 py-1 rounded text-xs font-medium bg-green-100 text-green-800 hover:bg-green-200 transition-colors disabled:opacity-50"
                    title="Đánh dấu đã xử lý"
                  >
                    {resolving[message.id] ? (
                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-green-600 mr-1"></div>
                    ) : (
                      <CheckCircle className="h-3 w-3 mr-1" />
                    )}
                    Đã xử lý
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default FailedMessages;
