const Database = require('./src/backend/services/Database');
const MongoDBService = require('./src/backend/services/MongoDBService');
const PrinterService = require('./src/backend/services/PrinterService');

async function testNewSchemaFlow() {
  console.log('🧪 Testing new schema flow...');
  
  try {
    // Initialize services
    const database = new Database();
    await database.initialize();
    
    // Skip MongoDB for local testing
    global.mongoDBService = null;
    
    const printerService = new PrinterService(database);
    
    console.log('✅ Services initialized');
    
    // Test 1: Simulate API comment with pk
    const testComment = {
      pk: '18012345678901234567', // Instagram API pk format
      username: 'test_user_api',
      text: 'Test comment from API with pk',
      timestamp: new Date().toISOString()
    };
    
    console.log('\n📝 Test 1: Mark comment as printed with pk');
    await database.markCommentAsPrinted(
      testComment.pk,
      testComment.username,
      testComment.text,
      'comment'
    );
    console.log('✅ Comment marked as printed with pk');
    
    // Test 2: Check local database
    console.log('\n🔍 Test 2: Check local database structure');
    const localRecords = await database.getPrintedHistoryForSync();
    const latestRecord = localRecords[localRecords.length - 1];
    
    console.log('Latest record:', {
      id: latestRecord.id,
      comment_pk: latestRecord.comment_pk,
      username: latestRecord.username,
      device_id: latestRecord.device_id
    });
    
    if (latestRecord.comment_pk === testComment.pk) {
      console.log('✅ Local database uses comment_pk correctly');
    } else {
      console.log('❌ Local database comment_pk mismatch');
    }
    
    // Test 3: Skip MongoDB tests for local testing
    console.log('\n⏭️ Test 3: Skipping MongoDB tests (local testing mode)');
    
    // Test 4: Test backward compatibility
    console.log('\n🔄 Test 4: Test backward compatibility with old comment_id');
    const oldFormatComment = {
      id: 'old_comment_123', // Old format
      username: 'test_user_old',
      text: 'Test comment with old format',
      timestamp: new Date().toISOString()
    };
    
    await database.markCommentAsPrinted(
      oldFormatComment.id,
      oldFormatComment.username,
      oldFormatComment.text,
      'comment'
    );
    console.log('✅ Backward compatibility test completed');
    
    console.log('\n🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    process.exit(0);
  }
}

// Run the test
testNewSchemaFlow();
