{"name": "instagram-live-web", "version": "1.0.0", "description": "Mobile web interface for Instagram Live Comment System", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "start": "vite --host"}, "dependencies": {"axios": "^1.4.0", "instagram-live-web": "file:", "lucide-react": "^0.263.1", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.14.2", "react-select": "^5.10.1", "socket.io-client": "^4.7.2"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "vite": "^4.4.5"}}