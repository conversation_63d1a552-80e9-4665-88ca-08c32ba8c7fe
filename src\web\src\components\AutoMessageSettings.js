import React, { useState, useEffect } from 'react';
import { MessageSquare, Save, RotateCcw, Settings, AlertCircle } from 'lucide-react';
import toast from 'react-hot-toast';

const AutoMessageSettings = () => {
  const [settings, setSettings] = useState({
    enabled: false,
    delayBetweenMessages: 2000,
    maxRetries: 3,
    requireMessengerLogin: true,
    maxMessagesBeforeRestart: 2000,
    maxMemoryUsageMB: 3000
  });
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Load settings on component mount
  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/auto-message-settings');
      const data = await response.json();
      
      if (data.success) {
        setSettings(data.settings);
      } else {
        toast.error('Không thể tải cài đặt auto message');
      }
    } catch (error) {
      console.error('Failed to load auto message settings:', error);
      toast.error('Lỗi khi tải cài đặt');
    } finally {
      setIsLoading(false);
    }
  };

  const saveSettings = async () => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/auto-message-settings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Đã lưu cài đặt auto message');
      } else {
        toast.error('Không thể lưu cài đặt');
      }
    } catch (error) {
      console.error('Failed to save auto message settings:', error);
      toast.error('Lỗi khi lưu cài đặt');
    } finally {
      setIsSaving(false);
    }
  };

  const resetToDefaults = () => {
    if (window.confirm('Bạn có chắc muốn khôi phục cài đặt về mặc định?')) {
      setSettings({
        enabled: false,
        delayBetweenMessages: 2000,
        maxRetries: 3,
        requireMessengerLogin: true,
        maxMessagesBeforeRestart: 2000,
        maxMemoryUsageMB: 3000
      });
      toast.success('Đã khôi phục cài đặt về mặc định');
    }
  };

  const handleSettingChange = (key, value) => {
    // Validate numeric inputs
    if (typeof value === 'number') {
      if (key === 'delayBetweenMessages' && (value < 500 || value > 10000)) {
        toast.error('Thời gian delay phải từ 0.5-10 giây');
        return;
      }
      if (key === 'maxRetries' && (value < 1 || value > 10)) {
        toast.error('Số lần thử lại phải từ 1-10');
        return;
      }
      if (key === 'maxMessagesBeforeRestart' && (value < 100 || value > 10000)) {
        toast.error('Giới hạn message phải từ 100-10,000');
        return;
      }
      if (key === 'maxMemoryUsageMB' && (value < 1000 || value > 8000)) {
        toast.error('Giới hạn memory phải từ 1,000-8,000 MB');
        return;
      }
    }

    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-2">Đang tải cài đặt...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center">
          <MessageSquare className="h-6 w-6 text-blue-600 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Cài đặt Auto Message</h3>
            <p className="text-sm text-gray-600">Cấu hình hệ thống gửi tin nhắn tự động</p>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Basic Settings */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            Cài đặt cơ bản
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thời gian delay giữa các message (ms)
              </label>
              <input
                type="number"
                min="500"
                max="10000"
                step="100"
                value={settings.delayBetweenMessages}
                onChange={(e) => handleSettingChange('delayBetweenMessages', parseInt(e.target.value))}
                className="input w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                Khuyến nghị: 2000ms (2 giây) để tránh spam
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Số lần thử lại khi lỗi
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={settings.maxRetries}
                onChange={(e) => handleSettingChange('maxRetries', parseInt(e.target.value))}
                className="input w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                Số lần thử lại khi gửi message thất bại
              </p>
            </div>
          </div>
        </div>

        {/* Performance Settings */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center">
            <AlertCircle className="h-4 w-4 mr-2" />
            Cài đặt hiệu suất & tự động restart
          </h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Giới hạn message trước khi restart
              </label>
              <input
                type="number"
                min="100"
                max="10000"
                step="100"
                value={settings.maxMessagesBeforeRestart}
                onChange={(e) => handleSettingChange('maxMessagesBeforeRestart', parseInt(e.target.value))}
                className="input w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                Hệ thống sẽ tự động restart sau khi gửi số message này để dọn dẹp memory
              </p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Giới hạn memory trước khi restart (MB)
              </label>
              <input
                type="number"
                min="1000"
                max="8000"
                step="100"
                value={settings.maxMemoryUsageMB}
                onChange={(e) => handleSettingChange('maxMemoryUsageMB', parseInt(e.target.value))}
                className="input w-full"
              />
              <p className="text-xs text-gray-500 mt-1">
                Hệ thống sẽ tự động restart khi memory vượt quá giới hạn này
              </p>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0" />
              <div className="text-sm text-blue-800">
                <p className="font-medium mb-1">Lưu ý về tự động restart:</p>
                <ul className="list-disc list-inside space-y-1 text-xs">
                  <li>Hệ thống sẽ không restart khi đang gửi message</li>
                  <li>Restart sẽ được thực hiện sau khi message hiện tại hoàn thành</li>
                  <li>Tối thiểu 10 phút giữa các lần restart</li>
                  <li>Cookies và session sẽ được lưu để tự động đăng nhập lại</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3 pt-4 border-t border-gray-200">
          <button
            onClick={saveSettings}
            disabled={isSaving}
            className="btn-primary flex items-center"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Đang lưu...' : 'Lưu cài đặt'}
          </button>

          <button
            onClick={resetToDefaults}
            disabled={isSaving}
            className="btn-secondary flex items-center"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Khôi phục mặc định
          </button>
        </div>
      </div>
    </div>
  );
};

export default AutoMessageSettings;
