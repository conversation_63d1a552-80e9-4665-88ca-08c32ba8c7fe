# Debug Comments Troubleshooting Guide

## 🔍 **Vấn đề: Debug Comments không nhận ra Scraper đang chạy**

### ✅ **Đã sửa:**

#### **1. Frontend State Management:**
```javascript
// Trước: Sử dụng sai state
disabled={isAddingDebugComments || !state.scraper?.isRunning}

// Sau: Sử dụng đúng systemState từ SocketContext
disabled={isAddingDebugComments || !systemState?.isRunning}
```

#### **2. Backend API Logic:**
```javascript
// Trước: Check systemState có thể không sync
if (!instagramScraper || !systemState.isRunning) {
  return res.status(400).json({ error: 'Scraper must be running' });
}

// Sau: Check trực tiếp từ scraper instance
const scraperStatus = instagramScraper.getStatus();
if (!scraperStatus.isRunning) {
  return res.status(400).json({ 
    error: '<PERSON>raper must be running to add debug comments',
    scraperStatus: scraperStatus
  });
}
```

#### **3. Enhanced Status API:**
```javascript
// Trước: Chỉ trả về systemState
const status = { isRunning: systemState.isRunning, ... };

// Sau: Kết hợp cả systemState và scraper instance
const combinedStatus = {
  system: systemStatus,
  scraper: scraperInstanceStatus,
  isRunning: systemStatus.isRunning || scraperInstanceStatus.isRunning
};
```

#### **4. Debug UI Improvements:**
- ✅ Thêm "Debug Info" button để xem trạng thái chi tiết
- ✅ Hiển thị JSON debug info với scroll
- ✅ Better error messages với scraper status
- ✅ Console logging cho debugging

## 🧪 **Cách test:**

### **1. Kiểm tra Server Health:**
```bash
node test_debug_comments_fix.js --status-only
```

### **2. Test Debug Comments API:**
```bash
node test_debug_comments_fix.js --debug-only
```

### **3. Full Test Suite:**
```bash
node test_debug_comments_fix.js
```

## 🔧 **Troubleshooting Steps:**

### **Bước 1: Kiểm tra Server**
1. Server có đang chạy không? (http://localhost:3000)
2. API health check: `GET /api/health`
3. Check console logs cho errors

### **Bước 2: Kiểm tra Scraper Status**
1. Vào Settings → Debug Comments
2. Nhấn "Debug Info" button
3. Xem JSON response:
   ```json
   {
     "system": {
       "isRunning": true/false,
       "scraperConnected": true/false
     },
     "scraper": {
       "isRunning": true/false,
       "browserConnected": true/false,
       "pageConnected": true/false
     }
   }
   ```

### **Bước 3: Khởi động Scraper**
Nếu scraper chưa chạy:
1. Vào Settings → Thu thập bình luận
2. Đăng nhập Instagram (hoặc dùng saved cookies)
3. Nhập username (có thể fake để test)
4. Nhấn "Bắt đầu thu thập"
5. Đợi status chuyển thành "Đang hoạt động"

### **Bước 4: Test Debug Comments**
1. Quay lại Settings → Debug Comments
2. Nhập số lượng (1-100)
3. Nhấn "Thêm Comments"
4. Check console logs và toast notifications

## 🐛 **Common Issues:**

### **Issue 1: "Scraper must be running"**
**Nguyên nhân:** Scraper chưa được khởi động hoặc đã crash
**Giải pháp:**
1. Check scraper status với Debug Info
2. Restart scraper từ Settings
3. Check browser processes trong Task Manager

### **Issue 2: Button vẫn disabled**
**Nguyên nhân:** Frontend state không sync với backend
**Giải pháp:**
1. Refresh trang web
2. Check systemState trong React DevTools
3. Verify socket connection

### **Issue 3: API returns 400 error**
**Nguyên nhân:** Validation hoặc scraper status issues
**Giải pháp:**
1. Check request payload
2. Verify count parameter (1-100)
3. Check server logs

### **Issue 4: Comments không xuất hiện**
**Nguyên nhân:** Socket events không được emit
**Giải pháp:**
1. Check browser console cho socket errors
2. Verify comment events trong Network tab
3. Check Comments page có refresh không

## 📊 **Debug Information:**

### **Frontend State Check:**
```javascript
// Trong browser console
console.log('SystemState:', window.systemState);
console.log('Socket connected:', window.socket?.connected);
```

### **Backend Status Check:**
```bash
curl http://localhost:3000/api/scraper-status
```

### **API Test:**
```bash
curl -X POST http://localhost:3000/api/add-debug-comments \
  -H "Content-Type: application/json" \
  -d '{"count": 3}'
```

## 🔄 **Recovery Steps:**

### **Nếu tất cả đều fail:**
1. **Restart server:** Stop và start lại server
2. **Clear browser cache:** Hard refresh (Ctrl+F5)
3. **Restart scraper:** Stop và start lại scraper
4. **Check logs:** Server console và browser console
5. **Test API directly:** Dùng curl hoặc Postman

### **Verification Checklist:**
- [ ] Server đang chạy (port 3000)
- [ ] Socket connection established
- [ ] Scraper status = running
- [ ] Browser và page connected
- [ ] API responds correctly
- [ ] Frontend state synced
- [ ] Debug comments appear on Comments page

## 🎯 **Expected Behavior:**

### **Khi scraper đang chạy:**
- ✅ Button "Thêm Comments" enabled
- ✅ Không có warning message
- ✅ API call thành công
- ✅ Toast success notification
- ✅ Comments xuất hiện ngay lập tức

### **Khi scraper không chạy:**
- ✅ Button "Thêm Comments" disabled
- ✅ Warning message hiển thị
- ✅ Debug Info button available
- ✅ API returns 400 với clear error message

## 📞 **Support:**

Nếu vẫn gặp vấn đề:
1. Chạy full test suite: `node test_debug_comments_fix.js`
2. Copy output và debug info
3. Check server logs và browser console
4. Provide screenshots của UI state
