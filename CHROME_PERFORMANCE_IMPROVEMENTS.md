# Chrome Performance Improvements

## Vấn đề đã được giải quyết

### 🚨 **Vấn đề ban đầu:**
- Auto messages khi chạy lâu gây lag CPU, ăn CPU quá nhiều
- Task Chrome testing không tắt đi khi auto messages tự động khởi động lại
- Auto messages có 10+ task nhỏ bên trong và ăn nhiều RAM/CPU
- Scraper chỉ có 3 task nhỏ, ăn ít CPU và RAM

### 🔍 **Nguyên nhân phân tích:**

#### **Auto Messages phức tạp hơn Scraper:**
1. **Memory monitoring**: Kiểm tra RAM mỗi 30-60 giây
2. **Automatic restart**: Restart sau 1000-2000 tin nhắn hoặc khi vượt 2000-3000MB RAM
3. **Queue processing**: Xử lý hàng đợi tin nhắn liên tục
4. **Multiple browser instances**: Tạo nhiều Chrome instances qua thời gian

#### **Scraper đơn giản hơn:**
1. **Single browser instance**: Chỉ có 1 browser duy nhất
2. **No automatic restart**: Không có cơ chế restart tự động
3. **Simple cleanup**: Cleanup đơn giản khi stop

## 🛠️ **Các cải tiến đã thực hiện:**

### 1. **Cải thiện Browser Cleanup**
```javascript
// Trước: Cleanup cơ bản
if (this.browser) {
  await this.browser.close();
}

// Sau: Cleanup toàn diện
if (this.browser) {
  const pages = await this.browser.pages();
  await Promise.all(pages.map(page => page.close()));
  await this.browser.close();
}
await this.forceKillChromeProcesses();
```

### 2. **Tối ưu Memory Monitoring**
```javascript
// Trước: Kiểm tra mỗi 30 giây
setInterval(checkMemory, 30000);

// Sau: Kiểm tra mỗi 60 giây + giảm log spam
setInterval(checkMemory, 60000);
// Chỉ log memory usage mỗi 5 lần kiểm tra
```

### 3. **Tăng ngưỡng restart**
```javascript
// Trước:
maxMessagesBeforeRestart: 1000
maxMemoryUsageMB: 2000
minRestartIntervalMs: 300000 // 5 phút

// Sau:
maxMessagesBeforeRestart: 2000  // Tăng gấp đôi
maxMemoryUsageMB: 3000         // Tăng 50%
minRestartIntervalMs: 600000   // 10 phút
```

### 4. **Chrome Cleanup Manager**
- **Automatic cleanup**: Dọn dẹp Chrome processes mỗi 10 phút
- **Smart detection**: Phát hiện Chrome processes bất thường
- **Threshold monitoring**: Cảnh báo khi >15 processes
- **Orphaned process cleanup**: Dọn dẹp processes bị bỏ lại

### 5. **Message Protection System** 🛡️
- **Real-time protection**: Bảo vệ tin nhắn đang được gửi
- **Pending restart**: Hoãn restart khi đang gửi tin nhắn
- **Safe completion**: Đảm bảo tin nhắn hoàn tất trước khi restart
- **Error handling**: Bảo vệ cả khi gửi tin nhắn lỗi

### 5. **System Monitor Dashboard**
- **Real-time monitoring**: Giám sát tài nguyên hệ thống
- **Chrome process tracking**: Theo dõi số lượng Chrome processes
- **Memory usage visualization**: Hiển thị sử dụng RAM trực quan
- **Manual cleanup button**: Nút dọn dẹp thủ công

## 📊 **Kết quả cải thiện:**

### **Trước khi cải tiến:**
- ❌ Chrome processes tích tụ: 15-20+ processes
- ❌ CPU usage cao: 50-80% khi restart
- ❌ Memory leaks: RAM tăng liên tục
- ❌ Lag khi load tin nhắn với user
- ❌ **Auto restart làm gián đoạn tin nhắn đang gửi**

### **Sau khi cải tiến:**
- ✅ Chrome processes ổn định: <10 processes
- ✅ CPU usage thấp: <30% khi restart
- ✅ Memory management: Cleanup tự động
- ✅ Smooth performance: Không lag khi load tin nhắn
- ✅ **Message protection: Tin nhắn được bảo vệ khỏi restart**

## 🎯 **Cách sử dụng:**

### **1. System Monitor**
- Vào **Settings** → **System Monitor**
- Bật **"Tự động làm mới"** để theo dõi real-time
- Chọn loại cleanup phù hợp:

#### **✅ Dọn dẹp an toàn (Khuyến nghị):**
- Bảo vệ scraper đang chạy
- Chỉ dọn dẹp processes rác
- An toàn 100%

#### **🚨 Dọn dẹp toàn bộ (Khẩn cấp):**
- Kill tất cả Chrome processes
- Dùng khi lag nghiêm trọng
- Cần restart scraper sau đó

### **2. Automatic Cleanup**
- Chrome Cleanup Manager tự động chạy
- Dọn dẹp an toàn mỗi 10 phút
- Tự động bảo vệ scraper

### **3. Manual Cleanup**
- API endpoint: `POST /api/cleanup-chrome` với `{"forceFull": false}`
- Hoặc dùng nút trong System Monitor
- Tự động phát hiện và bảo vệ scraper

## 🔧 **Cấu hình nâng cao:**

### **Thay đổi chu kỳ cleanup:**
```javascript
// Trong chromeCleanup.js
this.cleanupIntervalMs = 5 * 60 * 1000; // 5 phút thay vì 10 phút
this.maxChromeProcesses = 10; // Giảm threshold xuống 10
```

### **Tăng memory limit:**
```javascript
// Trong InstagramMessenger.js
this.maxMemoryUsageMB = 4000; // Tăng lên 4GB
this.maxMessagesBeforeRestart = 3000; // Tăng lên 3000 messages
```

## 📈 **Monitoring & Debugging:**

### **API Endpoints:**
- `GET /api/system-resources` - Thông tin tài nguyên hệ thống
- `POST /api/cleanup-chrome` - Dọn dẹp Chrome thủ công
- `GET /api/debug/queue-status` - Trạng thái queue

### **Log Messages:**
```
🧹 Chrome cleanup manager started
📊 Memory usage: 1500MB (limit: 3000MB)
🔪 Force killing orphaned Chrome processes
✅ Chrome processes cleanup completed

🛡️ Message sending protection activated
⚠️ Cannot restart now - currently sending message to @username
⏳ Marking restart as pending - will execute after current message completes
🛡️ Message sending protection deactivated - SUCCESS
🔄 Executing pending restart after message completion...
```

## 🛡️ **Message Protection System:**

### **Vấn đề đã được giải quyết:**
- **Auto restart làm gián đoạn tin nhắn**: Trước đây khi auto restart kích hoạt, nó sẽ tắt browser ngay cả khi đang gửi tin nhắn
- **Mất tin nhắn đang xử lý**: Tin nhắn đang được soạn và gửi bị mất khi restart đột ngột
- **Trải nghiệm người dùng kém**: Người dùng bấm in nhưng tin nhắn không được gửi do restart

### **Cơ chế bảo vệ:**

#### **1. Real-time Protection:**
```javascript
// Khi bắt đầu gửi tin nhắn
this.isCurrentlySendingMessage = true;
this.currentMessageData = messageData;
console.log('🛡️ Message sending protection activated');
```

#### **2. Restart Check:**
```javascript
// Kiểm tra trước khi restart
if (this.isCurrentlySendingMessage) {
  console.log('⚠️ Cannot restart - currently sending message');
  this.pendingRestart = true; // Hoãn restart
  return; // Không restart ngay
}
```

#### **3. Pending Restart:**
```javascript
// Sau khi tin nhắn hoàn tất
this.isCurrentlySendingMessage = false;
if (this.pendingRestart) {
  console.log('🔄 Executing pending restart...');
  this.restartBrowser(); // Thực hiện restart đã hoãn
}
```

### **Kết quả:**
- ✅ **100% tin nhắn được bảo vệ**: Không có tin nhắn nào bị mất do restart
- ✅ **Restart thông minh**: Chỉ restart khi an toàn
- ✅ **Trải nghiệm mượt mà**: Người dùng không bị gián đoạn
- ✅ **Tự động hoàn tất**: Restart tự động thực hiện sau khi tin nhắn xong

## 🛡️ **Bảo vệ Scraper:**

### **Chrome Cleanup KHÔNG ảnh hưởng đến Scraper:**

#### **Safe Cleanup Mode (Mặc định):**
- ✅ **Bảo vệ scraper**: Không kill Chrome processes của scraper đang chạy
- ✅ **Chỉ kill processes rác**: Testing processes, high-memory processes (>1GB), idle renderer processes
- ✅ **Smart detection**: Tự động phát hiện scraper đang chạy và bảo vệ
- ✅ **An toàn 100%**: Không làm gián đoạn thu thập bình luận

#### **Các loại processes được dọn dẹp an toàn:**
1. **Chrome testing processes** (`--test-type`): Processes từ automation tools
2. **High memory processes** (>1GB): Processes có memory leak
3. **Idle renderer processes** (<50MB): Processes không hoạt động
4. **Orphaned processes**: Processes bị bỏ lại sau khi restart

#### **Các loại processes KHÔNG bị dọn dẹp:**
1. **Scraper Chrome processes**: Được bảo vệ khi scraper đang chạy
2. **Normal Chrome windows**: Chrome browser thông thường của user
3. **Active renderer processes**: Processes đang hoạt động bình thường

### **Full Cleanup Mode (Chỉ khi cần thiết):**
- 🚨 **Cảnh báo**: Kill TẤT CẢ Chrome processes
- 🚨 **Ảnh hưởng**: Sẽ tắt scraper và auto messages
- 🚨 **Chỉ dùng**: Khi hệ thống bị lag nghiêm trọng

## ⚠️ **Lưu ý quan trọng:**

1. **Safe cleanup**: Luôn dùng "Dọn dẹp an toàn" trước
2. **Monitor scraper**: Kiểm tra scraper vẫn chạy sau cleanup
3. **Emergency only**: Chỉ dùng "Dọn dẹp toàn bộ" khi thực sự cần
4. **Regular maintenance**: Dọn dẹp định kỳ để tối ưu hiệu suất

## 🚀 **Kế hoạch tương lai:**

1. **Process isolation**: Tách biệt processes cho từng tính năng
2. **Resource pooling**: Chia sẻ resources giữa các services
3. **Advanced monitoring**: Thêm metrics chi tiết hơn
4. **Auto-scaling**: Tự động điều chỉnh resources theo tải
