import toast from 'react-hot-toast';

// Toast ID storage for different types
const toastIds = {
  print: null,
  message: null,
  newComment: null,
  error: null
};

/**
 * Show a print success toast, dismissing any previous print toast
 */
export const showPrintToast = (message, options = {}) => {
  if (toastIds.print) {
    toast.dismiss(toastIds.print);
  }
  
  toastIds.print = toast.success(message, {
    duration: 3000,
    icon: '🖨️',
    ...options
  });
  
  return toastIds.print;
};

/**
 * Show a message sent toast, dismissing any previous message toast
 */
export const showMessageToast = (message, options = {}) => {
  if (toastIds.message) {
    toast.dismiss(toastIds.message);
  }
  
  toastIds.message = toast.success(message, {
    duration: 3000,
    icon: '✅',
    ...options
  });
  
  return toastIds.message;
};

/**
 * Show a new comment toast, dismissing any previous new comment toast
 */
export const showNewCommentToast = (message, options = {}) => {
  if (toastIds.newComment) {
    toast.dismiss(toastIds.newComment);
  }
  
  toastIds.newComment = toast.success(message, {
    duration: 3000,
    icon: '💬',
    ...options
  });
  
  return toastIds.newComment;
};

/**
 * Show an error toast, dismissing any previous error toast
 */
export const showErrorToast = (message, options = {}) => {
  if (toastIds.error) {
    toast.dismiss(toastIds.error);
  }
  
  toastIds.error = toast.error(message, {
    duration: 4000,
    icon: '❌',
    ...options
  });
  
  return toastIds.error;
};

/**
 * Dismiss all toasts of a specific type
 */
export const dismissToast = (type) => {
  if (toastIds[type]) {
    toast.dismiss(toastIds[type]);
    toastIds[type] = null;
  }
};

/**
 * Dismiss all toasts
 */
export const dismissAllToasts = () => {
  Object.keys(toastIds).forEach(type => {
    if (toastIds[type]) {
      toast.dismiss(toastIds[type]);
      toastIds[type] = null;
    }
  });
};

/**
 * Show a regular toast without management (for one-off messages)
 */
export const showToast = (message, type = 'success', options = {}) => {
  return toast[type](message, options);
};

export default {
  showPrintToast,
  showMessageToast,
  showNewCommentToast,
  showErrorToast,
  dismissToast,
  dismissAllToasts,
  showToast
};
