# Safe Chrome Cleanup Feature

## 🎯 **Vấn đề đã gi<PERSON>i quyết:**

T<PERSON><PERSON><PERSON><PERSON> đ<PERSON>, hệ thống kill tất cả Chrome processes khi messenger restart, bao gồ<PERSON> cả scraper đang chạy ổn định. Đi<PERSON>u này gây gián đoạn không cần thiết cho scraper.

## 🔧 **Gi<PERSON>i pháp: Targeted Chrome Cleanup**

### **1. Process Identification:**
```javascript
// Messenger browser với identification flags
this.browser = await puppeteer.launch({
  args: [
    '--test-type',    // Identify messenger processes
    '--automation',   // Additional identification
    '--no-sandbox',
    // ... other args
  ]
});

// Store PID for targeted cleanup
this.browserProcessId = this.browser.process()?.pid;
```

### **2. Targeted Cleanup Methods:**

#### **Method 1: PID-based Cleanup (Most Precise)**
```javascript
// Kill specific messenger browser by PID
if (this.browserProcessId) {
  exec(`taskkill /F /PID ${this.browserProcessId} /T`);
}
```

#### **Method 2: Flag-based Cleanup**
```javascript
// Kill Chrome processes with messenger-specific flags
exec('wmic process where "name like \'%chrome%\' and commandline like \'%test-type%\' and commandline like \'%automation%\'" delete');
```

#### **Method 3: Memory-based Cleanup (Fallback)**
```javascript
// Kill high-memory Chrome processes (messenger tends to leak memory)
exec('wmic process where "name=\'chrome.exe\' and WorkingSetSize>1000000000" delete');
```

## 🎨 **Process Classification:**

### **Scraper Processes:**
- ✅ **No --test-type flag**
- ✅ **No --automation flag**
- ✅ **Stable memory usage**
- ✅ **Long-running**

### **Messenger Processes:**
- 🔍 **Has --test-type flag**
- 🔍 **Has --automation flag**
- 🔍 **Higher memory usage (due to leaks)**
- 🔍 **Frequent restarts**

## 📊 **Cleanup Strategy:**

### **Before (Dangerous):**
```
Messenger Restart → Kill ALL Chrome → Scraper Dies → Manual Restart Required
```

### **After (Safe):**
```
Messenger Restart → Kill ONLY Messenger Chrome → Scraper Continues → No Interruption
```

## 🔍 **Implementation Details:**

### **1. Browser Launch (InstagramMessenger.js):**
```javascript
// Add identification flags to messenger browser
this.browser = await puppeteer.launch({
  args: [
    '--test-type',      // Primary identifier
    '--automation',     // Secondary identifier
    '--no-sandbox',
    '--disable-setuid-sandbox',
    // ... other args
  ]
});

// Store PID for targeted cleanup
this.browserProcessId = this.browser.process()?.pid;
```

### **2. Safe Cleanup (forceKillChromeProcesses):**
```javascript
async forceKillChromeProcesses() {
  // Method 1: Kill by specific PID
  if (this.browserProcessId) {
    exec(`taskkill /F /PID ${this.browserProcessId} /T`);
  }

  // Method 2: Kill by automation flags
  exec('wmic process where "name like \'%chrome%\' and commandline like \'%test-type%\' and commandline like \'%automation%\'" delete');

  // Method 3: Kill high-memory processes (fallback)
  exec('wmic process where "name=\'chrome.exe\' and WorkingSetSize>1000000000" delete');
}
```

### **3. Chrome Cleanup Manager Updates:**
```javascript
async forceKillOrphanedChromeProcesses() {
  // Check if scraper is running
  const scraperRunning = this.scraperService && this.scraperService.isRunning;
  
  if (scraperRunning) {
    logger.info('⚠️ Scraper is running - using SAFE cleanup mode');
  }

  // Use higher memory threshold if scraper is running
  const memoryThreshold = scraperRunning ? 1200000000 : 800000000;
  
  // Kill only messenger-specific processes
  await this.executeCleanupCommand(
    `wmic process where "name='chrome.exe' and WorkingSetSize>${memoryThreshold}" delete`,
    `high-memory Chrome processes`
  );
}
```

## 🧪 **Testing:**

### **Test Script:**
```bash
# Test process identification
node test_safe_chrome_cleanup.js --processes

# Test safe cleanup
node test_safe_chrome_cleanup.js --cleanup

# Full test suite
node test_safe_chrome_cleanup.js
```

### **Manual Testing Steps:**
1. **Start both services:**
   - Scraper: Settings → Thu thập bình luận
   - Messenger: Settings → Tin nhắn tự động

2. **Monitor Chrome processes:**
   - Task Manager → Details → Chrome.exe
   - Note PIDs and command lines

3. **Trigger messenger restart:**
   - High memory usage
   - Manual restart
   - Browser crash recovery

4. **Verify results:**
   - Scraper continues running
   - Messenger restarts successfully
   - Only messenger Chrome processes killed

## 📊 **Expected Behavior:**

### **Scraper (Should be Preserved):**
- ✅ **Continues running** during messenger restart
- ✅ **No interruption** in comment detection
- ✅ **No manual restart** required
- ✅ **Stable performance**

### **Messenger (Should Restart Cleanly):**
- ✅ **Old processes killed** completely
- ✅ **New browser launched** successfully
- ✅ **Session restored** from cookies
- ✅ **Queue processing** resumes

## 🔍 **Log Messages:**

### **Safe Cleanup Logs:**
```
🔪 Targeted Chrome cleanup for messenger only (preserving scraper)...
🎯 Killing messenger browser by PID: 12345
✅ Messenger browser PID 12345 killed successfully
🔧 Killing Chrome processes with messenger flags...
✅ Messenger Chrome processes killed by flags
✅ Targeted Chrome cleanup completed - scraper preserved
```

### **Restart Logs:**
```
🚨 HANDLING BROWSER CRASH - Starting recovery process (safe mode)
🚀 Restarting messenger after browser crash (scraper preserved)...
✅ Messenger restarted successfully after browser crash - scraper unaffected
```

## 🎯 **Benefits:**

### **1. System Stability:**
- ✅ **No scraper interruption** during messenger issues
- ✅ **Independent operation** of services
- ✅ **Reduced manual intervention**

### **2. Performance:**
- ✅ **Faster recovery** (no scraper restart needed)
- ✅ **Continuous comment detection**
- ✅ **Better resource utilization**

### **3. User Experience:**
- ✅ **Seamless operation**
- ✅ **No data loss** from scraper
- ✅ **Reliable auto-messaging**

### **4. Development:**
- ✅ **Easier debugging** (services isolated)
- ✅ **Better error handling**
- ✅ **Cleaner logs**

## ⚠️ **Fallback Mechanisms:**

### **If PID-based cleanup fails:**
1. **Flag-based cleanup** (--test-type, --automation)
2. **Memory-based cleanup** (high memory usage)
3. **Pattern-based cleanup** (command line patterns)

### **If all targeted cleanup fails:**
1. **Log warning** about cleanup failure
2. **Continue with restart** anyway
3. **Monitor for orphaned processes**
4. **Manual intervention** if needed

## 🔧 **Configuration:**

### **Memory Thresholds:**
```javascript
// Adjust based on scraper status
const memoryThreshold = scraperRunning ? 1200000000 : 800000000; // 1.2GB vs 800MB
```

### **Identification Flags:**
```javascript
// Messenger-specific flags
'--test-type',
'--automation'
```

### **Cleanup Timeouts:**
```javascript
// PID cleanup: 5 seconds
// Flag cleanup: 8 seconds  
// Memory cleanup: 5 seconds
```

## 🎉 **Result:**

Hệ thống giờ đây có thể restart messenger mà không ảnh hưởng đến scraper, đảm bảo hoạt động ổn định và liên tục của cả hai services! 🚀

**Key Achievement:** Scraper chạy nhẹ và ổn định sẽ không bị gián đoạn bởi messenger restarts nữa!
