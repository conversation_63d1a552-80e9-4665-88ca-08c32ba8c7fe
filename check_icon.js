/**
 * <PERSON>ript để kiểm tra icon files trước khi package
 */

const fs = require('fs');
const path = require('path');

function checkIconFile(iconPath, expectedFormat) {
  console.log(`🔍 Checking ${expectedFormat} icon: ${iconPath}`);
  
  if (!fs.existsSync(iconPath)) {
    console.log(`   ❌ File not found: ${iconPath}`);
    return false;
  }
  
  const stats = fs.statSync(iconPath);
  console.log(`   📊 File size: ${(stats.size / 1024).toFixed(2)} KB`);
  console.log(`   📅 Last modified: ${stats.mtime.toLocaleString()}`);
  
  // Check file extension
  const ext = path.extname(iconPath).toLowerCase();
  const expectedExt = expectedFormat.toLowerCase();
  
  if (ext !== expectedExt) {
    console.log(`   ⚠️  Extension mismatch: expected ${expectedExt}, got ${ext}`);
  } else {
    console.log(`   ✅ Extension correct: ${ext}`);
  }
  
  // Basic file header check
  try {
    const buffer = fs.readFileSync(iconPath);
    
    if (expectedFormat === '.ico') {
      // ICO files start with 00 00 01 00
      if (buffer[0] === 0x00 && buffer[1] === 0x00 && buffer[2] === 0x01 && buffer[3] === 0x00) {
        console.log(`   ✅ Valid ICO file header`);
      } else {
        console.log(`   ❌ Invalid ICO file header`);
        return false;
      }
    } else if (expectedFormat === '.png') {
      // PNG files start with 89 50 4E 47
      if (buffer[0] === 0x89 && buffer[1] === 0x50 && buffer[2] === 0x4E && buffer[3] === 0x47) {
        console.log(`   ✅ Valid PNG file header`);
      } else {
        console.log(`   ❌ Invalid PNG file header`);
        return false;
      }
    }
    
    console.log(`   ✅ Icon file is valid`);
    return true;
    
  } catch (error) {
    console.log(`   ❌ Error reading file: ${error.message}`);
    return false;
  }
}

function checkAllIcons() {
  console.log('🎨 CommiLive Icon Checker');
  console.log('========================\n');
  
  const iconDir = path.join(__dirname, 'src', 'assets');
  console.log(`📁 Icon directory: ${iconDir}\n`);
  
  const icons = [
    { path: path.join(iconDir, 'icon.ico'), format: '.ico', platform: 'Windows' },
    { path: path.join(iconDir, 'icon.png'), format: '.png', platform: 'Linux' },
    { path: path.join(iconDir, 'icon.icns'), format: '.icns', platform: 'macOS' }
  ];
  
  let allValid = true;
  
  icons.forEach(({ path: iconPath, format, platform }) => {
    console.log(`🖥️  ${platform} Icon (${format}):`);
    const isValid = checkIconFile(iconPath, format);
    if (!isValid) {
      allValid = false;
    }
    console.log('');
  });
  
  console.log('📋 Summary:');
  if (allValid) {
    console.log('   ✅ All icon files are valid and ready for packaging');
    console.log('\n💡 You can now run:');
    console.log('   npm run package        # Package for Windows with icon');
    console.log('   npm run package:clean  # Clean and package');
    console.log('   npm run build:win      # Build with electron-builder');
  } else {
    console.log('   ❌ Some icon files have issues');
    console.log('\n🔧 To fix icon issues:');
    console.log('   1. Make sure icon files exist in src/assets/');
    console.log('   2. Verify file formats (.ico for Windows, .png for Linux, .icns for macOS)');
    console.log('   3. Check file sizes (ICO should be < 1MB, PNG should be 256x256 or 512x512)');
  }
  
  return allValid;
}

// Additional function to show packaging commands
function showPackagingCommands() {
  console.log('\n🚀 Available Packaging Commands:');
  console.log('================================');
  console.log('');
  console.log('📦 Electron Packager (faster, simpler):');
  console.log('   npm run package        # Windows x64 with icon');
  console.log('   npm run package:clean  # Clean dist folder first');
  console.log('   npm run package:all    # All platforms');
  console.log('');
  console.log('🏗️  Electron Builder (more features, installer):');
  console.log('   npm run build:win      # Windows with NSIS installer');
  console.log('   npm run build:mac      # macOS with DMG');
  console.log('   npm run build:linux    # Linux AppImage');
  console.log('   npm run dist           # Build without publishing');
  console.log('');
  console.log('💡 Recommended for development: npm run package');
  console.log('💡 Recommended for distribution: npm run build:win');
}

// Run the checker
if (require.main === module) {
  const isValid = checkAllIcons();
  showPackagingCommands();
  
  if (isValid) {
    console.log('\n🎉 Ready to package!');
    process.exit(0);
  } else {
    console.log('\n❌ Fix icon issues before packaging');
    process.exit(1);
  }
}

module.exports = { checkIconFile, checkAllIcons };
