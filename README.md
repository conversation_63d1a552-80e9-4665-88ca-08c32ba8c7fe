# Instagram Live Comment Management System

Hệ thống quản lý bình luận Instagram Live và gửi tin nhắn tự động toàn diện với kiến trúc đa thành phần.

## 🚀 Tính năng chính

### 📱 Ứng dụng Desktop (Electron)
- Trung tâm điều khiển chính cho quản lý hệ thống
- Giao diện cấu hình tài khoản Instagram
- Đ<PERSON>ều khiển khởi động/dừng server
- Dashboard giám sát trạng thái hệ thống
- Quản lý template tin nhắn tự động
- <PERSON> dõi lịch sử đơn hàng
- <PERSON>ài đặt cấu hình máy in

### 🖥️ Backend Server (Node.js)
- Engine xử lý cốt lõi với Puppeteer automation
- Trích xuất và lọc bình luận Instagram Live real-time
- Phát hiện bình luận trùng lặp
- <PERSON><PERSON> thống queue tin nhắn với retry logic
- API endpoints RESTful
- WebSocket connections cho cập nhật real-time

### 📱 Giao diện Web Mobile (React)
- Giao diện xử lý bình luận real-time
- Nút in và gửi tin nhắn cho từng bình luận
- Tính năng lọc và tìm kiếm bình luận
- Dashboard thống kê đơn hàng
- Thiết kế responsive cho mọi thiết bị mobile
- Hỗ trợ PWA và chế độ offline

### 🤖 Tự động hóa Puppeteer
- **Instance 1**: Thu thập bình luận Instagram Live
- **Instance 2**: Gửi tin nhắn direct message tự động

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Desktop App    │    │  Backend Server │    │  Mobile Web     │
│  (Electron)     │◄──►│  (Node.js)      │◄──►│  (React PWA)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  Puppeteer      │
                    │  Instances      │
                    │  ┌─────┐ ┌─────┐│
                    │  │ IG  │ │ DM  ││
                    │  │Live │ │Send ││
                    │  └─────┘ └─────┘│
                    └─────────────────┘
```

## 🛠️ Công nghệ sử dụng

### Frontend
- **Electron**: Desktop application framework
- **React**: Mobile web interface
- **Tailwind CSS**: Styling framework
- **Socket.IO Client**: Real-time communication
- **Lucide React**: Icon library

### Backend
- **Node.js**: Runtime environment
- **Express.js**: Web framework
- **Socket.IO**: Real-time communication
- **Puppeteer**: Browser automation
- **Bull**: Queue management system
- **SQLite**: Database
- **Winston**: Logging

### DevOps & Tools
- **Redis**: Caching and queue backend
- **Electron Builder**: Desktop app packaging
- **ESLint**: Code linting
- **Jest**: Testing framework

## 📦 Cài đặt

### Yêu cầu hệ thống
- Node.js 16+ 
- npm hoặc yarn
- Redis server (tùy chọn)
- Chrome/Chromium browser

### Bước 1: Clone repository
```bash
git clone <repository-url>
cd instagram-live-comment-system
```

### Bước 2: Cài đặt dependencies
```bash
# Cài đặt dependencies chính
npm install

# Cài đặt dependencies cho web interface
cd src/web
npm install
cd ../..
```

### Bước 3: Cấu hình môi trường
```bash
# Copy file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa file .env với thông tin của bạn
nano .env
```

### Bước 4: Khởi tạo database
```bash
# Database sẽ được tạo tự động khi chạy lần đầu
mkdir -p src/backend/data
mkdir -p logs
```

## 🚀 Chạy ứng dụng

### Development Mode
```bash
# Chạy tất cả services
npm run dev

# Hoặc chạy từng service riêng biệt
npm run server    # Backend server
npm run web       # Web interface
npm start         # Desktop app
```

### Production Mode
```bash
# Build web interface
cd src/web
npm run build
cd ../..

# Build desktop app
npm run build

# Chạy server
npm run server
```

## 📱 Sử dụng

### 1. Khởi động hệ thống
1. Mở ứng dụng Desktop
2. Cấu hình thông tin Instagram trong Settings
3. Khởi động Backend Server
4. Truy cập Web Interface trên mobile

### 2. Thu thập bình luận
1. Nhập URL Instagram Live stream
2. Nhập thông tin đăng nhập Instagram
3. Nhấn "Bắt đầu thu thập"
4. Theo dõi bình luận real-time trên mobile

### 3. Xử lý đơn hàng
1. Xem bình luận mới trên mobile interface
2. Nhấn nút "In" để in thông tin
3. Nhấn nút "Gửi tin nhắn" để thêm vào queue
4. Theo dõi trạng thái gửi tin nhắn

## ⚙️ Cấu hình

### Template tin nhắn
Tạo và quản lý template tin nhắn với biến động:
- `{{username}}`: Tên người dùng
- `{{product}}`: Tên sản phẩm
- `{{price}}`: Giá sản phẩm
- `{{payment_link}}`: Link thanh toán

### Từ khóa tự động
Cấu hình từ khóa để tự động phát hiện:
- **Đơn hàng**: mua, order, đặt hàng, book, lấy
- **Hỏi giá**: giá, bao nhiêu, price, cost, tiền

### Cài đặt hiệu suất
- Số lượng bình luận tối đa trong bộ nhớ
- Thời gian cleanup tự động
- Số lần retry khi gửi tin nhắn thất bại

## 🔧 API Documentation

### REST Endpoints

#### Health Check
```
GET /api/health
```

#### Statistics
```
GET /api/stats
```

#### Comments
```
GET /api/comments?page=1&limit=50&search=keyword
```

#### Start/Stop Scraping
```
POST /api/start-scraping
POST /api/stop-scraping
```

### WebSocket Events

#### Client → Server
- `send-message`: Gửi tin nhắn
- `print-comment`: In bình luận

#### Server → Client
- `new-comment`: Bình luận mới
- `comment-processed`: Bình luận đã xử lý
- `message-sent`: Tin nhắn đã gửi
- `queue-stats`: Thống kê queue

## 🛡️ Bảo mật

### Mã hóa dữ liệu
- Thông tin đăng nhập Instagram được mã hóa
- JWT tokens cho authentication
- Rate limiting để tránh spam

### Tuân thủ Instagram Policy
- Respect rate limits
- Tránh hành vi spam
- Chính sách lưu trữ dữ liệu phù hợp

## 📊 Monitoring & Logging

### Logs
- Error logs: `logs/error.log`
- Combined logs: `logs/combined.log`
- Console output với Winston

### Metrics
- Số lượng bình luận thu thập
- Tỷ lệ chuyển đổi đơn hàng
- Hiệu suất gửi tin nhắn
- Thời gian hoạt động hệ thống

## 🧪 Testing

```bash
# Chạy tests
npm test

# Test coverage
npm run test:coverage

# Lint code
npm run lint
```

## 📦 Build & Deploy

### Desktop App
```bash
# Build cho Windows
npm run build:win

# Build cho macOS
npm run build:mac

# Build cho Linux
npm run build:linux
```

### Web Interface
```bash
cd src/web
npm run build
```

## 🤝 Contributing

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📄 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Support

- Email: <EMAIL>
- Documentation: [Wiki](link-to-wiki)
- Issues: [GitHub Issues](link-to-issues)

## 🔄 Changelog

### v1.0.0
- ✨ Initial release
- 🚀 Desktop application với Electron
- 📱 Mobile web interface với React
- 🤖 Instagram Live comment scraping
- 💬 Automated direct messaging
- 📊 Real-time statistics dashboard
- 🎨 Modern UI/UX design

---

**Made with ❤️ for Instagram Live sellers**
