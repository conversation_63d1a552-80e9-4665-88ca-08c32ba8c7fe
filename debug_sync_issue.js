#!/usr/bin/env node

/**
 * Debug script để tìm hiểu tại sao hệ thống add lại tất c<PERSON> comments
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3001';

async function makeRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      timeout: 10000
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.error || error.message,
      status: error.response?.status
    };
  }
}

async function debugSyncIssue() {
  console.log('🔍 Debugging sync issue...\n');
  
  // 1. Check device IDs
  console.log('=== DEVICE ID ANALYSIS ===');
  const deviceResult = await makeRequest('GET', '/api/mongodb/debug-boolean-conversion');
  
  if (deviceResult.success) {
    const localDeviceIds = [...new Set(deviceResult.data.local.map(r => r.device_id || 'unknown'))];
    const mongoDeviceIds = [...new Set(deviceResult.data.mongo.map(r => r.device_id || 'unknown'))];
    
    console.log('Local device IDs:', localDeviceIds);
    console.log('MongoDB device IDs:', mongoDeviceIds);
    
    // Check if device IDs match
    const deviceMatch = localDeviceIds.every(id => mongoDeviceIds.includes(id));
    console.log('Device IDs match:', deviceMatch);
    
    if (!deviceMatch) {
      console.log('❌ Device ID mismatch detected!');
      console.log('Local only:', localDeviceIds.filter(id => !mongoDeviceIds.includes(id)));
      console.log('MongoDB only:', mongoDeviceIds.filter(id => !localDeviceIds.includes(id)));
    }
  }
  
  // 2. Check key matching logic
  console.log('\n=== KEY MATCHING ANALYSIS ===');
  if (deviceResult.success) {
    const localRecords = deviceResult.data.local.slice(0, 5);
    const mongoRecords = deviceResult.data.mongo.slice(0, 5);
    
    console.log('Sample local keys:');
    localRecords.forEach(r => {
      const key = `${r.id}_${r.device_id || 'unknown'}`;
      console.log(`  ${key} (ID:${r.id}, device:${r.device_id || 'unknown'})`);
    });
    
    console.log('Sample MongoDB keys:');
    mongoRecords.forEach(r => {
      const key = `${r.local_id}_${r.device_id || 'unknown'}`;
      console.log(`  ${key} (local_id:${r.local_id}, device:${r.device_id || 'unknown'})`);
    });
    
    // Check for potential matches
    const localKeys = new Set(localRecords.map(r => `${r.id}_${r.device_id || 'unknown'}`));
    const mongoKeys = new Set(mongoRecords.map(r => `${r.local_id}_${r.device_id || 'unknown'}`));
    
    const matchingKeys = [...localKeys].filter(key => mongoKeys.has(key));
    console.log(`Matching keys in sample: ${matchingKeys.length}/${localRecords.length}`);
    
    if (matchingKeys.length === 0) {
      console.log('❌ No matching keys found in sample!');
    }
  }
  
  // 3. Check counts breakdown
  console.log('\n=== COUNTS BREAKDOWN ===');
  const countsResult = await makeRequest('GET', '/api/mongodb/debug-counts');
  
  if (countsResult.success) {
    const counts = countsResult.data;
    console.log('Local printed history:', counts.printedHistory.local);
    console.log('MongoDB printed history:', counts.printedHistory.mongo);
    
    const localTotal = counts.printedHistory.local.total;
    const mongoTotal = counts.printedHistory.mongo.total;
    const difference = localTotal - mongoTotal;
    
    console.log(`Difference: ${difference} records need to be synced`);
    
    if (difference > 1000) {
      console.log('⚠️  Large difference detected - this will cause timeout');
      console.log('Recommendation: Sync in smaller batches');
    }
  }
  
  // 4. Check for null device_ids
  console.log('\n=== NULL DEVICE ID CHECK ===');
  if (deviceResult.success) {
    const localNullDevices = deviceResult.data.local.filter(r => !r.device_id || r.device_id === 'unknown');
    const mongoNullDevices = deviceResult.data.mongo.filter(r => !r.device_id || r.device_id === 'unknown');
    
    console.log(`Local records with null/unknown device_id: ${localNullDevices.length}`);
    console.log(`MongoDB records with null/unknown device_id: ${mongoNullDevices.length}`);
    
    if (localNullDevices.length > 0 || mongoNullDevices.length > 0) {
      console.log('⚠️  Found records with missing device_id - this may cause sync issues');
    }
  }
  
  console.log('\n=== RECOMMENDATIONS ===');
  console.log('1. Check if device_id is consistent between local and MongoDB');
  console.log('2. Consider adding batch processing for large sync operations');
  console.log('3. Add more detailed logging to sync process');
  console.log('4. Consider using pagination for large datasets');
}

debugSyncIssue().catch(error => {
  console.error('Debug script failed:', error.message);
  process.exit(1);
});
