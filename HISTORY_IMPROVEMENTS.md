# ✅ History Page Improvements

## 🎯 **Vấn đề đã được giải quyết:**

### **1. ✅ Kiểm tra "In lại" không tạo duplicate history:**
- **Confirmed**: API `/api/print-comment` **KHÔNG** tạo history entry mới
- **Confirmed**: API `/api/print-comment` **KHÔNG** gửi tin nhắn (`messageQueued: false`)
- **Behavior**: Chỉ in lại comment, không lưu vào database và không queue message

### **2. ✅ Thêm nút "Gửi lại tin nhắn":**
- **New Feature**: <PERSON><PERSON><PERSON> "Gửi lại" cho mỗi comment trong history
- **API**: Tạo endpoint `/api/queue-message` để handle resend
- **UI**: Thêm nút với icon `Send` và màu xanh dương

## 🔧 **<PERSON><PERSON><PERSON> thay đổi đã thực hiện:**

### **1. ✅ Frontend Changes (History.js):**

#### **Import Icon:**
```javascript
import { Send } from 'lucide-react';
```

#### **New Handler Function:**
```javascript
const handleResendMessage = async (comment) => {
  try {
    const response = await fetch('/api/queue-message', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        username: comment.username,
        comment_text: comment.comment_text || comment.text,
        source: 'history_resend'
      }),
    });

    const data = await response.json();
    if (data.success) {
      showToast(`Đã thêm tin nhắn vào hàng đợi cho @${comment.username}`, 'success');
    } else {
      throw new Error(data.error || 'Failed to queue message');
    }
  } catch (error) {
    showErrorToast('Lỗi khi gửi lại tin nhắn: ' + error.message);
  }
};
```

#### **New UI Button:**
```javascript
<button
  onClick={() => handleResendMessage(comment)}
  className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 hover:bg-blue-200 transition-colors"
  title="Gửi lại tin nhắn cho bình luận này"
>
  <Send className="h-3 w-3 mr-1" />
  Gửi lại
</button>
```

### **2. ✅ Backend Changes (server.js):**

#### **New API Endpoint:**
```javascript
app.post('/api/queue-message', async (req, res) => {
  try {
    const { username, comment_text, source = 'manual' } = req.body;

    // Validate input
    if (!username || !comment_text) {
      return res.status(400).json({ error: 'Username and comment_text are required' });
    }

    // Get customer type for template selection
    const customerType = await database.getCustomerType(username);

    // Create message data for queue
    const messageData = {
      id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      comment_id: `resend_${Date.now()}`,
      username: username,
      original_comment: comment_text,
      customer_type: customerType,
      template_type: 'normal',
      status: 'pending',
      retries: 0,
      max_retries: 3
    };

    // Add to database queue
    await database.addToMessageQueue(messageData);

    // Update system state and emit events
    systemState.queuedMessages++;
    io.emit('system-state', systemState);
    io.emit('message-queued', { username, messageId: messageData.id, source });

    res.json({
      success: true,
      message: `Message queued for @${username}`,
      messageId: messageData.id,
      customerType: customerType
    });

  } catch (error) {
    logger.error('Failed to queue message:', error);
    res.status(500).json({ error: 'Failed to queue message: ' + error.message });
  }
});
```

## 🎨 **UI Layout Bây Giờ:**

### **Normal View (Active Comments):**
```
[⭐] @username - timestamp
Comment text here...
[🖨️ In lại] [📤 Gửi lại] [🗑️ Xóa]
```

### **Deleted View:**
```
[⭐] @username - timestamp  
Comment text here...
[↩️ Khôi phục]
```

## 🔄 **Workflow "Gửi lại tin nhắn":**

1. **User clicks "Gửi lại"** → `handleResendMessage(comment)`
2. **API call** → `POST /api/queue-message`
3. **Get customer type** → `database.getCustomerType(username)`
4. **Create message data** → With normal template type
5. **Add to queue** → `database.addToMessageQueue(messageData)`
6. **Update counters** → `systemState.queuedMessages++`
7. **Emit events** → `message-queued` and `system-state`
8. **Show toast** → "Đã thêm tin nhắn vào hàng đợi cho @username"

## ✅ **Kết quả:**

### **✅ "In lại" behavior:**
- **Chỉ in**: Không tạo history mới, không gửi tin nhắn
- **Safe**: Có thể in lại nhiều lần mà không duplicate

### **✅ "Gửi lại" feature:**
- **Queue message**: Thêm vào hàng đợi auto-messaging
- **Customer type**: Tự động detect và sử dụng template phù hợp
- **Real-time**: Update counter và emit events ngay lập tức
- **User feedback**: Toast notification xác nhận thành công

### **✅ UI/UX:**
- **3 buttons**: In lại (xanh lá) | Gửi lại (xanh dương) | Xóa (đỏ)
- **Clear icons**: Printer | Send | Trash
- **Responsive**: Buttons scale properly on mobile
- **Tooltips**: Hover text explains each action

**🎉 History page bây giờ có đầy đủ chức năng: In lại, Gửi lại tin nhắn, và Xóa!**
