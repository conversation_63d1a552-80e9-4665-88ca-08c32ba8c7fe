# 📊 Printed History MongoDB Atlas Sync

## 🎯 Tổng quan

Tính năng đồng bộ thông minh cho `printed_history` lên <PERSON>, tương tự như hệ thống đồng bộ hiện có cho `instagram_threads` và `regular_customers`.

**Lưu ý**: <PERSON><PERSON> thống chỉ đồng bộ dữ liệu quan trọng (printed_history, customers, threads, send_once_history) lên <PERSON>goDB Atlas. Không lưu tất cả comments vào Atlas để tối ưu storage và performance.

## 🗄️ Cấu trúc Database

### **Local SQLite (printed_history table):**
```sql
CREATE TABLE printed_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  comment_id TEXT NOT NULL,
  username TEXT NOT NULL,
  comment_text TEXT NOT NULL,
  print_type TEXT DEFAULT 'comment',
  printed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  synced_at DATETIME DEFAULT NULL,
  device_id TEXT DEFAULT NULL,
  is_deleted INTEGER DEFAULT 0
);
```

### **MongoDB Atlas (printed_history collection):**
```javascript
{
  local_id: Number,           // ID từ local SQLite
  comment_id: String,         // ID của comment
  username: String,           // Username của người comment
  comment_text: String,       // Nội dung comment
  print_type: String,         // 'comment' hoặc 'backup'
  printed_at: Date,          // Thời gian in
  created_at: Date,          // Thời gian tạo record
  device_id: String,         // ID thiết bị
  is_deleted: Boolean,       // Đã xóa hay chưa
  synced_at: Date           // Thời gian sync cuối
}
```

## 🔄 Cơ chế Đồng bộ

### **1. Auto-sync khi có thay đổi:**
- **Khi in comment**: Tự động sync lên MongoDB
- **Khi xóa record**: Mark as deleted và sync
- **Real-time sync**: Không cần chờ periodic sync

### **2. Smart Bidirectional Sync:**
- **Conflict Resolution**: So sánh `created_at` timestamp
- **Merge Strategy**: Newer record wins
- **Device Awareness**: Sử dụng `device_id` để phân biệt thiết bị

### **3. Periodic Sync:**
- **Interval**: Mỗi 5 phút (configurable)
- **Comprehensive**: Sync tất cả customers, threads, và printed_history
- **Error Handling**: Local operations không bị ảnh hưởng nếu sync fail

## 🚀 API Endpoints

### **Manual Smart Sync:**
```bash
POST /api/mongodb/smart-sync
```
**Response:**
```json
{
  "success": true,
  "customers": { "addedToLocal": 0, "addedToMongo": 2, "updatedLocal": 1, "updatedMongo": 0 },
  "threads": { "addedToLocal": 1, "addedToMongo": 0, "updatedLocal": 0, "updatedMongo": 1 },
  "printedHistory": { "addedToLocal": 5, "addedToMongo": 3, "updatedLocal": 0, "updatedMongo": 2 },
  "message": "Smart sync completed..."
}
```

## 🔧 Implementation Details

### **1. Database Migration:**
- Tự động thêm `synced_at`, `device_id`, `is_deleted` columns
- Backward compatible với printed_history hiện có
- Index optimization cho sync performance

### **2. Conflict Resolution:**
```javascript
// So sánh timestamp để quyết định record nào mới hơn
const localUpdated = new Date(localRecord.created_at);
const mongoUpdated = new Date(mongoRecord.created_at);

if (mongoUpdated > localUpdated) {
  // MongoDB version mới hơn - update local
} else if (localUpdated > mongoUpdated) {
  // Local version mới hơn - update MongoDB
}
```

### **3. Unique Key Strategy:**
- **Primary Key**: `comment_id + username` (business logic)
- **MongoDB Upsert**: `local_id + device_id` (technical)
- **Prevents Duplicates**: Across multiple devices

## 📊 Monitoring & Logging

### **Sync Logs:**
```
📥 Added printed history from MongoDB: @username - comment_123
📤 Added printed history to MongoDB: @username - comment_456
🔄 Updated local printed history from MongoDB: @username - comment_789
🔄 Updated MongoDB printed history from local: @username - comment_101
```

### **Socket Events:**
```javascript
// Client nhận thông báo khi sync hoàn thành
socket.on('mongodb-sync-completed', (data) => {
  console.log('Sync completed:', data.printedHistory);
});
```

## 🛡️ Error Handling

### **Graceful Degradation:**
- Local operations luôn thành công
- Sync failures không block printing
- Retry mechanism trong periodic sync

### **Data Integrity:**
- Soft delete thay vì hard delete
- Timestamp-based conflict resolution
- Device-aware sync để tránh overwrites

## 🎛️ Configuration

### **Sync Settings:**
```javascript
const MONGODB_SYNC_CONFIG = {
  ENABLED: true,
  AUTO_SYNC_INTERVAL: 5 * 60 * 1000, // 5 minutes
  BATCH_SIZE: 100,
  RETRY_ATTEMPTS: 3
};
```

### **Collection Indexes:**
```javascript
// MongoDB indexes for performance
db.printed_history.createIndex({ username: 1 });
db.printed_history.createIndex({ printed_at: -1 });
db.printed_history.createIndex({ device_id: 1 });
db.printed_history.createIndex({ comment_id: 1 });
```

## 🔍 Testing

### **Manual Test Scenarios:**
1. **Print comment** → Check MongoDB có record mới
2. **Delete history** → Check MongoDB record marked as deleted
3. **Multi-device sync** → Check conflict resolution
4. **Network failure** → Check local operations still work

### **Verification Commands:**
```bash
# Check local printed_history
sqlite3 database.db "SELECT * FROM printed_history ORDER BY printed_at DESC LIMIT 5;"

# Check MongoDB collection
mongosh "mongodb+srv://..." --eval "db.printed_history.find().sort({printed_at:-1}).limit(5)"
```

## 🎉 Benefits

### **Multi-device Support:**
- ✅ Printed history sync across devices
- ✅ Consistent data everywhere
- ✅ No data loss when switching devices

### **Backup & Recovery:**
- ✅ Cloud backup của printed history
- ✅ Easy restore from MongoDB
- ✅ Historical data preservation

### **Analytics Ready:**
- ✅ Centralized data for reporting
- ✅ Cross-device analytics
- ✅ Business intelligence integration

## 📝 Migration Notes

### **Existing Data:**
- Printed history hiện có sẽ được sync lên MongoDB
- Không cần manual migration
- Auto-migration khi connect MongoDB lần đầu

### **Backward Compatibility:**
- App vẫn hoạt động bình thường nếu không có MongoDB
- Local-only mode supported
- Gradual rollout possible
