import axios from 'axios';

// Dynamic server URL detection for mobile compatibility
const getServerUrl = () => {
  const hostname = window.location.hostname;
  const port = '3001';

  // If accessing via IP address (mobile), use that IP
  // If accessing via localhost (desktop), use localhost
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    return `http://localhost:${port}`;
  } else {
    return `http://${hostname}:${port}`;
  }
};

// Create axios instance with base configuration
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? '' : getServerUrl(),
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`);
    return config;
  },
  (error) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    console.log(`Response from ${response.config.url}:`, response.status);
    return response;
  },
  (error) => {
    console.error('Response error:', error);
    
    // Handle common error cases
    if (error.code === 'ECONNREFUSED') {
      console.error('Cannot connect to server. Make sure the backend is running.');
    }
    
    return Promise.reject(error);
  }
);

// Helper function to get full API URL for fetch calls
export const getApiUrl = (endpoint) => {
  const baseUrl = process.env.NODE_ENV === 'production' ? '' : getServerUrl();
  return `${baseUrl}${endpoint}`;
};

export default api;
