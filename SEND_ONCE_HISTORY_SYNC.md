# 📝 Send_once History MongoDB Atlas Sync

## 🎯 Tổng quan

Tính năng đồng bộ thông minh cho `send_once_history` lên MongoDB Atlas với cơ chế tự động xóa sau 2 ngày, tương tự như hệ thống đồng bộ hiện có cho `printed_history`, `instagram_threads` và `regular_customers`.

## 🗄️ Cấu trúc Database

### **Local SQLite (send_once_history table):**
```sql
CREATE TABLE send_once_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT NOT NULL,
  template_name TEXT NOT NULL,
  customer_type TEXT NOT NULL,
  template_type TEXT DEFAULT 'normal',
  sent_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  synced_at DATETIME DEFAULT NULL,
  device_id TEXT DEFAULT NULL,
  is_deleted INTEGER DEFAULT 0,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(username, template_name, customer_type, template_type)
);
```

### **MongoDB Atlas (send_once_history collection):**
```javascript
{
  local_id: Number,           // ID từ local SQLite
  username: String,           // Username của người nhận
  template_name: String,      // Tên template đã gửi
  customer_type: String,      // 'regular' hoặc 'vip'
  template_type: String,      // 'normal' hoặc 'backup'
  sent_at: Date,             // Thời gian gửi
  created_at: Date,          // Thời gian tạo record
  updated_at: Date,          // Thời gian cập nhật cuối
  device_id: String,         // ID thiết bị
  is_deleted: Boolean,       // Đã xóa hay chưa
  synced_at: Date           // Thời gian sync cuối
}
```

## 🔄 Cơ chế Đồng bộ

### **1. Auto-sync khi có thay đổi:**
- **Khi gửi send_once template**: Tự động sync lên MongoDB
- **Khi cleanup (2 ngày)**: Mark as deleted và sync
- **Real-time sync**: Không cần chờ periodic sync

### **2. Smart Bidirectional Sync:**
- **Unique Key**: `username + template_name + customer_type + template_type`
- **Conflict Resolution**: So sánh `updated_at` timestamp
- **Merge Strategy**: Newer record wins
- **Device Awareness**: Sử dụng `device_id` để phân biệt thiết bị

### **3. Automatic Cleanup:**
- **Schedule**: Mỗi 24 giờ
- **Retention**: 2 ngày (configurable)
- **Method**: Soft delete (mark as deleted)
- **Sync**: Auto-sync deletions to MongoDB

## ⏰ Automatic Cleanup System

### **Cleanup Schedule:**
```javascript
// Chạy mỗi 24 giờ
setInterval(async () => {
  const deletedCount = await database.cleanupOldSendOnceHistory(2); // 2 days
  // Auto-sync deletions to MongoDB
}, 24 * 60 * 60 * 1000);
```

### **Cleanup Logic:**
1. **Find old records**: `sent_at < (current_date - 2 days)`
2. **Soft delete**: `UPDATE ... SET is_deleted = 1`
3. **Auto-sync**: Sync deletions to MongoDB
4. **Notification**: Emit socket event về cleanup

### **Manual Cleanup API:**
```bash
POST /api/send-once-history/cleanup
{
  "olderThanDays": 2
}
```

## 🚀 API Endpoints

### **Manual Smart Sync:**
```bash
POST /api/mongodb/smart-sync
```
**Response:**
```json
{
  "success": true,
  "customers": { "addedToLocal": 0, "addedToMongo": 2, "updatedLocal": 1, "updatedMongo": 0 },
  "threads": { "addedToLocal": 1, "addedToMongo": 0, "updatedLocal": 0, "updatedMongo": 1 },
  "printedHistory": { "addedToLocal": 5, "addedToMongo": 3, "updatedLocal": 0, "updatedMongo": 2 },
  "sendOnceHistory": { "addedToLocal": 2, "addedToMongo": 1, "updatedLocal": 1, "updatedMongo": 0 },
  "message": "Smart sync completed..."
}
```

## 🔧 Implementation Details

### **1. Database Migration:**
- Tự động thêm `synced_at`, `device_id`, `is_deleted`, `updated_at` columns
- Backward compatible với send_once_history hiện có
- Index optimization cho sync performance

### **2. Conflict Resolution:**
```javascript
// So sánh updated_at timestamp để quyết định record nào mới hơn
const localUpdated = new Date(localRecord.updated_at || localRecord.created_at);
const mongoUpdated = new Date(mongoRecord.updated_at || mongoRecord.created_at);

if (mongoUpdated > localUpdated) {
  // MongoDB version mới hơn - update local
} else if (localUpdated > mongoUpdated) {
  // Local version mới hơn - update MongoDB
}
```

### **3. Unique Key Strategy:**
- **Business Key**: `username + template_name + customer_type + template_type`
- **MongoDB Upsert**: Sử dụng business key cho upsert
- **Prevents Duplicates**: Across multiple devices và time

## 📊 Monitoring & Logging

### **Sync Logs:**
```
📥 Added send_once history from MongoDB: @username - template_name
📤 Added send_once history to MongoDB: @username - template_name
🔄 Updated local send_once history from MongoDB: @username - template_name
🔄 Updated MongoDB send_once history from local: @username - template_name
```

### **Cleanup Logs:**
```
🧹 Starting automatic send_once history cleanup...
✅ Automatic cleanup: Removed 15 send_once history records older than 2 days
🗑️ Cleaned up 15 send_once history records older than 2 days
```

### **Socket Events:**
```javascript
// Client nhận thông báo khi sync hoàn thành
socket.on('mongodb-sync-completed', (data) => {
  console.log('Sync completed:', data.sendOnceHistory);
});

// Client nhận thông báo khi cleanup hoàn thành
socket.on('send-once-cleanup-completed', (data) => {
  console.log('Cleanup completed:', data.deletedCount, 'records');
});
```

## 🛡️ Error Handling

### **Graceful Degradation:**
- Local operations luôn thành công
- Sync failures không block send_once functionality
- Retry mechanism trong periodic sync

### **Data Integrity:**
- Soft delete thay vì hard delete
- Timestamp-based conflict resolution
- Device-aware sync để tránh overwrites
- Unique constraint enforcement

## 🎛️ Configuration

### **Cleanup Settings:**
```javascript
const SEND_ONCE_CLEANUP_CONFIG = {
  ENABLED: true,
  CLEANUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
  RETENTION_DAYS: 2,
  AUTO_SYNC_DELETIONS: true
};
```

### **Collection Indexes:**
```javascript
// MongoDB indexes for performance
db.send_once_history.createIndex({ username: 1 });
db.send_once_history.createIndex({ template_name: 1 });
db.send_once_history.createIndex({ sent_at: -1 });
db.send_once_history.createIndex({ device_id: 1 });
db.send_once_history.createIndex({ 
  username: 1, 
  template_name: 1, 
  customer_type: 1, 
  template_type: 1 
}, { unique: true });
```

## 🔍 Testing

### **Manual Test Scenarios:**
1. **Send send_once template** → Check MongoDB có record mới
2. **Wait 2+ days** → Check automatic cleanup
3. **Multi-device sync** → Check conflict resolution
4. **Network failure** → Check local operations still work

### **Verification Commands:**
```bash
# Check local send_once_history
sqlite3 database.db "SELECT * FROM send_once_history ORDER BY sent_at DESC LIMIT 5;"

# Check MongoDB collection
mongosh "mongodb+srv://..." --eval "db.send_once_history.find().sort({sent_at:-1}).limit(5)"

# Check cleanup
sqlite3 database.db "SELECT COUNT(*) FROM send_once_history WHERE is_deleted = 1;"
```

## 🎉 Benefits

### **Multi-device Support:**
- ✅ Send_once history sync across devices
- ✅ Consistent send_once tracking everywhere
- ✅ No duplicate sends when switching devices

### **Automatic Cleanup:**
- ✅ Auto-cleanup sau 2 ngày
- ✅ Prevents database bloat
- ✅ Maintains performance

### **Backup & Recovery:**
- ✅ Cloud backup của send_once history
- ✅ Easy restore from MongoDB
- ✅ Historical data preservation (before cleanup)

## 📝 Migration Notes

### **Existing Data:**
- Send_once history hiện có sẽ được sync lên MongoDB
- Không cần manual migration
- Auto-migration khi connect MongoDB lần đầu

### **Backward Compatibility:**
- App vẫn hoạt động bình thường nếu không có MongoDB
- Local-only mode supported
- Gradual rollout possible
