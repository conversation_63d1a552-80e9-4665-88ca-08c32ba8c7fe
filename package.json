{"name": "commilive", "version": "1.0.0", "description": "CommiLive - Instagram Live comment management and auto-messaging system", "main": "src/main.js", "scripts": {"start": "cross-env NODE_ENV=production electron .", "dev": "cross-env NODE_ENV=development electron .", "dev-full": "concurrently \"npm run server\" \"npm run electron-dev\"", "server": "nodemon src/backend/server.js", "electron-dev": "wait-on http://localhost:3001 && electron .", "build": "npm run build:web && electron-builder", "build:web": "cd src/web && npm run build", "build:win": "npm run build:web && electron-builder --win", "build:mac": "npm run build:web && electron-builder --mac", "build:linux": "npm run build:web && electron-builder --linux", "dist": "npm run build:web && electron-builder --publish=never", "package": "npm run build:web && electron-packager . commilive --platform=win32 --arch=x64 --out=dist --overwrite --icon=src/assets/icon.ico --app-version=1.0.0 --win32metadata.CompanyName=\"CommiLive\" --win32metadata.ProductName=\"CommiLive\" --ignore=src/web/node_modules --ignore=src/web/src --ignore=src/web/public", "package:all": "npm run build:web && electron-packager . commilive --all --out=dist --overwrite --icon=src/assets/icon.ico --ignore=src/web/node_modules", "package:clean": "rimraf dist && npm run package", "test": "jest", "lint": "eslint src/", "web": "cd src/web && npm start"}, "keywords": ["instagram", "live", "comments", "automation", "electron", "puppeteer"], "author": "Your Name", "license": "MIT", "devDependencies": {"concurrently": "^7.6.0", "cross-env": "^7.0.3", "electron": "^22.0.0", "electron-builder": "^24.13.3", "electron-packager": "^17.1.2", "eslint": "^8.0.0", "jest": "^29.0.0", "nodemon": "^2.0.20", "rimraf": "^3.0.2", "wait-on": "^7.0.1"}, "dependencies": {"axios": "^1.4.0", "bcrypt": "^5.1.0", "bull": "^4.11.3", "canvas": "^3.1.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "electron-store": "^8.2.0", "express": "^4.18.2", "git": "^0.1.5", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "lowdb": "^6.0.1", "moment": "^2.29.4", "mongodb": "^6.3.0", "node-notifier": "^10.0.1", "puppeteer": "^21.0.0", "qrcode": "^1.5.4", "redis": "^4.6.7", "sharp": "^0.34.2", "socket.io": "^4.7.2", "sqlite3": "^5.1.6", "usb": "^2.15.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "build": {"appId": "com.yourcompany.commilive", "productName": "CommiLive", "directories": {"output": "dist"}, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "npmRebuild": false, "forceCodeSigning": false, "files": ["src/**/*", "node_modules/**/*", "!src/web/node_modules", "!src/web/src", "!src/web/public", "!src/web/vite.config.js", "!src/web/package*.json"], "extraResources": [{"from": "src/web/build", "to": "web", "filter": ["**/*"]}], "mac": {"category": "public.app-category.productivity", "icon": "src/assets/icon.icns"}, "win": {"target": "nsis", "icon": "src/assets/icon.ico", "sign": false, "verifyUpdateCodeSignature": false}, "linux": {"target": "AppImage", "icon": "src/assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}}