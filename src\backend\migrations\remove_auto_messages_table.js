const Database = require('../services/Database');
const winston = require('winston');

// Configure logger
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.simple(),
  transports: [new winston.transports.Console()]
});

async function removeAutoMessagesTable() {
  let database;
  
  try {
    logger.info('🗑️ Starting migration: Remove auto_messages table');
    
    // Initialize database
    database = new Database();
    await database.init();
    
    // Check if auto_messages table exists
    const tableExists = await database.allQuery(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='auto_messages'
    `);
    
    if (tableExists.length > 0) {
      logger.info('📊 Found auto_messages table, checking data...');
      
      // Check if table has any data
      const rowCount = await database.allQuery('SELECT COUNT(*) as count FROM auto_messages');
      const count = rowCount[0].count;
      
      if (count > 0) {
        logger.info(`⚠️ Table contains ${count} records - backing up data first`);
        
        // Backup data to a JSON file
        const data = await database.allQuery('SELECT * FROM auto_messages');
        const fs = require('fs').promises;
        const path = require('path');
        
        const backupDir = path.join(__dirname, '../backups');
        await fs.mkdir(backupDir, { recursive: true });
        
        const backupFile = path.join(backupDir, `auto_messages_backup_${Date.now()}.json`);
        await fs.writeFile(backupFile, JSON.stringify(data, null, 2));
        
        logger.info(`💾 Data backed up to: ${backupFile}`);
      } else {
        logger.info('✅ Table is empty, safe to remove');
      }
      
      // Drop the table
      await database.runQuery('DROP TABLE auto_messages');
      logger.info('🗑️ auto_messages table removed successfully');
      
    } else {
      logger.info('ℹ️ auto_messages table does not exist, nothing to remove');
    }
    
    // Verify table is gone
    const verifyGone = await database.allQuery(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name='auto_messages'
    `);
    
    if (verifyGone.length === 0) {
      logger.info('✅ Migration completed successfully - auto_messages table removed');
    } else {
      throw new Error('Table still exists after DROP command');
    }
    
  } catch (error) {
    logger.error('❌ Migration failed:', error);
    throw error;
  } finally {
    if (database) {
      await database.close();
    }
  }
}

// Run migration if called directly
if (require.main === module) {
  removeAutoMessagesTable()
    .then(() => {
      logger.info('🎉 Migration completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      logger.error('💥 Migration failed:', error);
      process.exit(1);
    });
}

module.exports = removeAutoMessagesTable;
