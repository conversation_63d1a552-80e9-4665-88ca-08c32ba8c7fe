# 🔧 Chrome Process Monitoring Fix

## 🐛 Vấn đề phát hiệ<PERSON>

Trong Task Manager hiển thị **2 Chrome for Testing processes** nhưng web interface hiển thị **18 tổng số** và **4 testing processes**. <PERSON><PERSON> sự không khớp giữa actual processes và monitoring data.

## 🔍 Root Cause Analysis

### **Vấn đề 1: Inconsistent Counting Methods**
```javascript
// Method 1: getChromeProcessCount() - Sử dụng tasklist
exec('tasklist /FI "IMAGENAME eq chrome.exe" /FO CSV | find /C "chrome.exe"')

// Method 2: getDetailedChromeInfo() - Sử dụng wmic  
exec('wmic process where "name=\'chrome.exe\'" get ProcessId,WorkingSetSize,CommandLine /format:csv')
```

**Vấn đề**: Hai commands khác nhau có thể trả về kết qu<PERSON> khác nhau do:
- Different process enumeration methods
- Different timing of execution
- Different filtering logic

### **Vấn đề 2: CSV Parsing Issues**
```javascript
// Parsing cũ - không robust
const parts = line.split(',');
if (parts.length >= 4) {
  processes.push({
    pid: parts[2],
    memory: parseInt(parts[3]) || 0,
    commandLine: parts[1] || ''
  });
}
```

**Vấn đề**: 
- Không filter malformed lines
- Không validate data
- Không handle edge cases

### **Vấn đề 3: Testing Process Detection**
```javascript
// Detection cũ - quá đơn giản
const testingProcesses = processes.filter(p => p.commandLine.includes('--test-type'));
```

**Vấn đề**: Chỉ check `--test-type`, miss các flags khác như `--automation`, `Chrome for Testing`

## ✅ Solution Implemented

### **1. Unified Counting Method**
```javascript
async getChromeProcessCount() {
  try {
    // Use the same method as getDetailedChromeInfo for consistency
    const detailedInfo = await this.getDetailedChromeInfo();
    return detailedInfo.totalProcesses;
  } catch (error) {
    logger.error('Error getting Chrome process count:', error);
    return 0;
  }
}
```

**Benefits**:
- ✅ Single source of truth
- ✅ Consistent results
- ✅ No timing discrepancies

### **2. Robust CSV Parsing**
```javascript
lines.forEach(line => {
  try {
    // More robust CSV parsing
    const parts = line.split(',');
    if (parts.length >= 4 && parts[2] && parts[3]) {
      const pid = parts[2].trim();
      const memory = parseInt(parts[3].trim()) || 0;
      const commandLine = parts[1] ? parts[1].trim() : '';
      
      // Only add if we have valid data
      if (pid && !isNaN(parseInt(pid))) {
        processes.push({
          pid: pid,
          memory: memory,
          commandLine: commandLine
        });
      }
    }
  } catch (parseError) {
    // Skip malformed lines
    logger.debug('Skipped malformed Chrome process line:', line);
  }
});
```

**Improvements**:
- ✅ Validate data before adding
- ✅ Handle malformed lines gracefully
- ✅ Trim whitespace
- ✅ Skip invalid entries

### **3. Enhanced Testing Process Detection**
```javascript
// Filter testing processes (Chrome for Testing)
const testingProcesses = processes.filter(p => 
  p.commandLine.includes('--test-type') || 
  p.commandLine.includes('--automation') ||
  p.commandLine.includes('Chrome for Testing')
);
```

**Improvements**:
- ✅ Multiple detection patterns
- ✅ Catch more testing processes
- ✅ More accurate counting

### **4. Cross-Platform Support**
```javascript
} else {
  // Linux/Mac fallback
  await new Promise((resolve) => {
    exec('ps aux | grep chrome | grep -v grep', (error, stdout) => {
      if (!error && stdout) {
        const lines = stdout.split('\n').filter(line => line.trim());
        lines.forEach(line => {
          const parts = line.split(/\s+/);
          if (parts.length >= 11) {
            processes.push({
              pid: parts[1],
              memory: parseInt(parts[5]) * 1024 || 0, // Convert KB to bytes
              commandLine: parts.slice(10).join(' ')
            });
          }
        });
      }
      resolve();
    });
  });
}
```

**Benefits**:
- ✅ Works on Linux/Mac
- ✅ Consistent interface
- ✅ Proper memory unit conversion

### **5. Debug Logging**
```javascript
logger.debug(`Chrome process info: ${result.totalProcesses} total, ${result.testingProcesses} testing, ${result.highMemoryProcesses} high memory`);
```

**Benefits**:
- ✅ Easier debugging
- ✅ Visibility into counting
- ✅ Performance monitoring

## 📊 Expected Results

### **Before Fix:**
- **Inconsistent counts** between different methods
- **Inaccurate testing process detection**
- **Parsing errors** with malformed data
- **No debug visibility**

### **After Fix:**
- **Consistent counts** across all methods
- **Accurate testing process detection** with multiple patterns
- **Robust parsing** with error handling
- **Debug logging** for troubleshooting

## 🧪 Testing

### **Manual Testing Steps:**
1. **Open Task Manager** → Check actual Chrome processes
2. **Open web interface** → Check Chrome Processes section
3. **Compare numbers** → Should match now
4. **Start/stop services** → Monitor count changes
5. **Check debug logs** → Verify detection logic

### **Expected Behavior:**
```
Task Manager: 2 Chrome for Testing processes
Web Interface: 2 total, 2 testing processes ✅

Task Manager: 5 Chrome processes (3 normal + 2 testing)  
Web Interface: 5 total, 2 testing processes ✅
```

## 🔮 Future Improvements

### **1. Real-time Monitoring**
- WebSocket updates for process counts
- Live refresh without page reload
- Real-time alerts for high process counts

### **2. Process Details**
- Show individual process PIDs
- Memory usage per process
- Command line arguments display
- Process tree visualization

### **3. Advanced Filtering**
- Filter by memory usage
- Filter by process age
- Filter by command line patterns
- Custom detection rules

### **4. Performance Optimization**
- Cache process info for short periods
- Batch multiple requests
- Optimize command execution
- Reduce system load

## ⚠️ Important Notes

### **System Impact**
- **Low overhead**: Commands run quickly
- **Minimal CPU usage**: Only when requested
- **No continuous polling**: On-demand execution

### **Accuracy**
- **Timing sensitive**: Process counts can change rapidly
- **System dependent**: Different Windows versions may vary
- **Permission dependent**: Requires appropriate system access

### **Troubleshooting**
- **Check debug logs** for parsing issues
- **Verify system permissions** for wmic/tasklist
- **Test commands manually** in PowerShell
- **Monitor system performance** during execution
