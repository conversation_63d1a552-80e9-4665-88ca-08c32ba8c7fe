const { MongoClient } = require('mongodb');
const winston = require('winston');

class MongoDBService {
  constructor() {
    this.client = null;
    this.db = null;
    this.isConnected = false;
    this.connectionString = null;
    
    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.simple(),
      transports: [new winston.transports.Console()]
    });
  }

  async connect(connectionString) {
    try {
      this.logger.info('Connecting to MongoDB Atlas...');

      if (!connectionString) {
        throw new Error('Connection string is required');
      }

      this.connectionString = connectionString;

      // Create MongoDB client
      this.client = new MongoClient(connectionString, {
        serverSelectionTimeoutMS: 15000, // 15 seconds timeout
        connectTimeoutMS: 15000,
        socketTimeoutMS: 15000,
      });

      // Connect to MongoDB
      await this.client.connect();

      // Test connection
      await this.client.db('admin').command({ ping: 1 });

      // Use fixed database name: instagram-live
      const dbName = 'instagram-live';
      this.db = this.client.db(dbName);

      // Ensure database and collections exist
      await this.ensureDatabaseStructure();

      this.isConnected = true;
      this.logger.info(`Successfully connected to MongoDB Atlas database: ${dbName}`);

      return { success: true, database: dbName };
    } catch (error) {
      this.logger.error('Failed to connect to MongoDB Atlas:', error);
      this.isConnected = false;
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.client) {
        await this.client.close();
        this.isConnected = false;
        this.logger.info('Disconnected from MongoDB Atlas');
      }
    } catch (error) {
      this.logger.error('Error disconnecting from MongoDB:', error);
    }
  }

  async ensureDatabaseStructure() {
    try {
      this.logger.info('Ensuring database structure...');

      // Create collections if they don't exist
      const collections = await this.db.listCollections().toArray();
      const collectionNames = collections.map(c => c.name);

      // Create regular_customers collection if it doesn't exist
      if (!collectionNames.includes('regular_customers')) {
        await this.db.createCollection('regular_customers');
        this.logger.info('Created regular_customers collection');

        // Create index on username for better performance
        await this.db.collection('regular_customers').createIndex({ username: 1 }, { unique: true });
        this.logger.info('Created index on username field');
      }

      // Create instagram_threads collection if it doesn't exist
      if (!collectionNames.includes('instagram_threads')) {
        await this.db.createCollection('instagram_threads');
        this.logger.info('Created instagram_threads collection');

        // Create index on username for better performance
        await this.db.collection('instagram_threads').createIndex({ username: 1 }, { unique: true });
        this.logger.info('Created index on username field for threads');
      }

      // Create printed_history collection if it doesn't exist
      if (!collectionNames.includes('printed_history')) {
        await this.db.createCollection('printed_history');
        this.logger.info('Created printed_history collection');

        // Create indexes for better performance
        await this.db.collection('printed_history').createIndex({ username: 1 });
        await this.db.collection('printed_history').createIndex({ printed_at: -1 });
        await this.db.collection('printed_history').createIndex({ device_id: 1 });
        await this.db.collection('printed_history').createIndex({ comment_id: 1 });
        this.logger.info('Created indexes on printed_history collection');
      }

      // Create send_once_history collection if it doesn't exist
      if (!collectionNames.includes('send_once_history')) {
        await this.db.createCollection('send_once_history');
        this.logger.info('Created send_once_history collection');

        // Create indexes for better performance
        await this.db.collection('send_once_history').createIndex({ username: 1 });
        await this.db.collection('send_once_history').createIndex({ template_name: 1 });
        await this.db.collection('send_once_history').createIndex({ sent_at: -1 });
        await this.db.collection('send_once_history').createIndex({ device_id: 1 });
        await this.db.collection('send_once_history').createIndex({
          username: 1,
          template_name: 1,
          customer_type: 1,
          template_type: 1
        }, { unique: true });
        this.logger.info('Created indexes on send_once_history collection');
      }



      this.logger.info('Database structure ensured successfully');
    } catch (error) {
      this.logger.error('Failed to ensure database structure:', error);
      // Don't throw error - this is not critical
    }
  }

  extractDatabaseName(connectionString) {
    try {
      // Extract database name from connection string
      // Format: mongodb+srv://user:<EMAIL>/database?options
      const match = connectionString.match(/\/([^/?]+)(\?|$)/);
      return match ? match[1] : null;
    } catch (error) {
      return null;
    }
  }

  // Customer Management Methods
  async syncCustomersToMongo(localCustomers) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('regular_customers');

      // Clear existing data and insert new data (legacy method)
      await collection.deleteMany({});

      if (localCustomers.length > 0) {
        const mongoCustomers = localCustomers.map(customer => ({
          username: customer.username,
          notes: customer.notes || '',
          marked_at: new Date(customer.marked_at),
          updated_at: new Date(customer.updated_at || customer.marked_at),
          device_id: customer.device_id || 'unknown',
          is_deleted: Boolean(customer.is_deleted), // Convert SQLite integer to boolean
          synced_from_local: true
        }));

        await collection.insertMany(mongoCustomers);
        this.logger.info(`Synced ${mongoCustomers.length} customers to MongoDB`);
      }

      return { success: true, count: localCustomers.length };
    } catch (error) {
      this.logger.error('Failed to sync customers to MongoDB:', error);
      throw error;
    }
  }

  // Sync single customer to MongoDB (for real-time sync)
  async syncSingleCustomerToMongo(customerData) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('regular_customers');

      const mongoCustomer = {
        username: customerData.username,
        notes: customerData.notes || '',
        marked_at: new Date(customerData.marked_at || new Date()),
        updated_at: new Date(customerData.updated_at || new Date()),
        device_id: customerData.device_id || 'unknown',
        is_deleted: Boolean(customerData.is_deleted) // Convert SQLite integer to boolean
      };

      await collection.replaceOne(
        { username: customerData.username },
        mongoCustomer,
        { upsert: true }
      );

      this.logger.info(`Synced single customer to MongoDB: ${customerData.username}`);
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to sync single customer to MongoDB:', error);
      throw error;
    }
  }

  // Mark customer as deleted in MongoDB
  async markCustomerAsDeleted(username, deviceId) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('regular_customers');

      await collection.updateOne(
        { username: username },
        {
          $set: {
            is_deleted: true,
            updated_at: new Date(),
            device_id: deviceId || 'unknown'
          }
        },
        { upsert: true }
      );

      this.logger.info(`Marked customer as deleted in MongoDB: ${username}`);
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to mark customer as deleted in MongoDB:', error);
      throw error;
    }
  }

  async syncCustomersFromMongo() {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('regular_customers');
      const customers = await collection.find({}).sort({ updated_at: -1 }).toArray();

      // Convert MongoDB format to local format with sync metadata
      const localCustomers = customers.map(customer => ({
        username: customer.username,
        notes: customer.notes || '',
        marked_at: customer.marked_at.toISOString(),
        updated_at: customer.updated_at ? customer.updated_at.toISOString() : new Date().toISOString(),
        device_id: customer.device_id || 'unknown',
        is_deleted: customer.is_deleted || false
      }));

      this.logger.info(`Retrieved ${localCustomers.length} customers from MongoDB`);
      return localCustomers;
    } catch (error) {
      this.logger.error('Failed to sync customers from MongoDB:', error);
      throw error;
    }
  }

  async addCustomerToMongo(customer) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('regular_customers');
      
      const mongoCustomer = {
        username: customer.username,
        notes: customer.notes || '',
        marked_at: new Date(),
        updated_at: new Date(),
        synced_from_local: false
      };
      
      await collection.replaceOne(
        { username: customer.username },
        mongoCustomer,
        { upsert: true }
      );
      
      this.logger.info(`Added/updated customer in MongoDB: ${customer.username}`);
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to add customer to MongoDB:', error);
      throw error;
    }
  }

  async removeCustomerFromMongo(username) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('regular_customers');
      const result = await collection.deleteOne({ username });
      
      this.logger.info(`Removed customer from MongoDB: ${username}`);
      return { success: true, deletedCount: result.deletedCount };
    } catch (error) {
      this.logger.error('Failed to remove customer from MongoDB:', error);
      throw error;
    }
  }

  async updateCustomerInMongo(username, updates) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('regular_customers');
      
      const updateDoc = {
        ...updates,
        updated_at: new Date()
      };
      
      const result = await collection.updateOne(
        { username },
        { $set: updateDoc }
      );
      
      this.logger.info(`Updated customer in MongoDB: ${username}`);
      return { success: true, modifiedCount: result.modifiedCount };
    } catch (error) {
      this.logger.error('Failed to update customer in MongoDB:', error);
      throw error;
    }
  }

  // Instagram Threads Management Methods
  async syncThreadsToMongo(localThreads) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('instagram_threads');

      // Clear existing data and insert new data (legacy method)
      await collection.deleteMany({});

      if (localThreads.length > 0) {
        const mongoThreads = localThreads.map(thread => ({
          username: thread.username,
          thread_id: thread.thread_id,
          last_used: new Date(thread.last_used),
          updated_at: new Date(thread.updated_at || thread.last_used),
          device_id: thread.device_id || 'unknown',
          is_deleted: Boolean(thread.is_deleted), // Convert SQLite integer to boolean
          synced_from_local: true
        }));

        await collection.insertMany(mongoThreads);
        this.logger.info(`Synced ${mongoThreads.length} threads to MongoDB`);
      }

      return { success: true, count: localThreads.length };
    } catch (error) {
      this.logger.error('Failed to sync threads to MongoDB:', error);
      throw error;
    }
  }

  // Sync single thread to MongoDB (for real-time sync)
  async syncSingleThreadToMongo(threadData) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('instagram_threads');

      const mongoThread = {
        username: threadData.username,
        thread_id: threadData.thread_id,
        last_used: new Date(threadData.last_used || new Date()),
        updated_at: new Date(threadData.updated_at || new Date()),
        device_id: threadData.device_id || 'unknown',
        is_deleted: Boolean(threadData.is_deleted) // Convert SQLite integer to boolean
      };

      await collection.replaceOne(
        { username: threadData.username },
        mongoThread,
        { upsert: true }
      );

      this.logger.info(`Synced single thread to MongoDB: ${threadData.username}`);
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to sync single thread to MongoDB:', error);
      throw error;
    }
  }

  // Mark thread as deleted in MongoDB
  async markThreadAsDeleted(username, deviceId) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('instagram_threads');

      await collection.updateOne(
        { username: username },
        {
          $set: {
            is_deleted: true,
            updated_at: new Date(),
            device_id: deviceId || 'unknown'
          }
        },
        { upsert: true }
      );

      this.logger.info(`Marked thread as deleted in MongoDB: ${username}`);
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to mark thread as deleted in MongoDB:', error);
      throw error;
    }
  }

  async syncThreadsFromMongo() {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('instagram_threads');
      const threads = await collection.find({}).sort({ updated_at: -1 }).toArray();

      // Convert MongoDB format to local format with sync metadata
      const localThreads = threads.map(thread => ({
        username: thread.username,
        thread_id: thread.thread_id,
        last_used: thread.last_used.toISOString(),
        updated_at: thread.updated_at ? thread.updated_at.toISOString() : new Date().toISOString(),
        device_id: thread.device_id || 'unknown',
        is_deleted: thread.is_deleted || false
      }));

      this.logger.info(`Retrieved ${localThreads.length} threads from MongoDB`);
      return localThreads;
    } catch (error) {
      this.logger.error('Failed to sync threads from MongoDB:', error);
      throw error;
    }
  }

  async addThreadToMongo(thread) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('instagram_threads');

      const mongoThread = {
        username: thread.username,
        thread_id: thread.thread_id,
        last_used: new Date(),
        updated_at: new Date(),
        synced_from_local: false
      };

      await collection.replaceOne(
        { username: thread.username },
        mongoThread,
        { upsert: true }
      );

      this.logger.info(`Added/updated thread in MongoDB: ${thread.username}`);
      return { success: true };
    } catch (error) {
      this.logger.error('Failed to add thread to MongoDB:', error);
      throw error;
    }
  }

  async removeThreadFromMongo(username) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('instagram_threads');
      const result = await collection.deleteOne({ username });

      this.logger.info(`Removed thread from MongoDB: ${username}`);
      return { success: true, deletedCount: result.deletedCount };
    } catch (error) {
      this.logger.error('Failed to remove thread from MongoDB:', error);
      throw error;
    }
  }

  async updateThreadInMongo(username, updates) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('instagram_threads');

      const updateDoc = {
        ...updates,
        updated_at: new Date()
      };

      const result = await collection.updateOne(
        { username },
        { $set: updateDoc }
      );

      this.logger.info(`Updated thread in MongoDB: ${username}`);
      return { success: true, modifiedCount: result.modifiedCount };
    } catch (error) {
      this.logger.error('Failed to update thread in MongoDB:', error);
      throw error;
    }
  }

  // Test connection method
  async testConnection(connectionString) {
    try {
      const testClient = new MongoClient(connectionString, {
        serverSelectionTimeoutMS: 10000,
        connectTimeoutMS: 10000,
      });

      await testClient.connect();
      await testClient.db('admin').command({ ping: 1 });

      // Test access to instagram-live database
      const testDb = testClient.db('instagram-live');
      await testDb.admin().ping();

      await testClient.close();

      return { success: true, message: 'Connection successful. Database "instagram-live" will be created automatically.' };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  getStatus() {
    return {
      isConnected: this.isConnected,
      connectionString: this.connectionString ? this.maskConnectionString(this.connectionString) : null,
      database: this.db ? this.db.databaseName : null
    };
  }

  maskConnectionString(connectionString) {
    // Mask password in connection string for security
    return connectionString.replace(/:([^:@]+)@/, ':***@');
  }

  // ==================== PRINTED HISTORY SYNC METHODS ====================

  // Sync single printed history record to MongoDB
  async syncSinglePrintedHistoryToMongo(printedHistoryData) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('printed_history');

      const mongoPrintedHistory = {
        local_id: printedHistoryData.id,
        comment_pk: printedHistoryData.comment_pk || printedHistoryData.comment_id, // Support both new and old format
        username: printedHistoryData.username,
        comment_text: printedHistoryData.comment_text,
        print_type: printedHistoryData.print_type || 'comment',
        printed_at: new Date(printedHistoryData.printed_at),
        created_at: new Date(printedHistoryData.created_at || printedHistoryData.printed_at),
        device_id: printedHistoryData.device_id || 'unknown',
        is_deleted: Boolean(printedHistoryData.is_deleted), // Convert SQLite integer to boolean
        synced_at: new Date()
      };

      // Boolean conversion: SQLite integer → MongoDB boolean

      // Use local_id only as unique identifier for upsert (simplified)
      await collection.replaceOne(
        {
          local_id: printedHistoryData.id
        },
        mongoPrintedHistory,
        { upsert: true }
      );

      this.logger.info(`Synced printed history to MongoDB: ${printedHistoryData.username} - ${printedHistoryData.comment_pk || printedHistoryData.comment_id}`);
    } catch (error) {
      this.logger.error('Failed to sync printed history to MongoDB:', error);
      throw error;
    }
  }

  // Mark printed history as deleted in MongoDB
  async markPrintedHistoryAsDeleted(localId, deviceId) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('printed_history');

      if (localId) {
        // Mark specific record as deleted using local_id only (simplified after migration)
        const result = await collection.updateOne(
          { local_id: localId },
          {
            $set: {
              is_deleted: true,
              deleted_at: new Date(),
              synced_at: new Date()
            }
          }
        );

        if (result.matchedCount === 0) {
          this.logger.warn(`⚠️ No MongoDB record found with local_id: ${localId} for deletion`);
        } else if (result.modifiedCount === 0) {
          this.logger.warn(`⚠️ MongoDB record found but not modified for local_id: ${localId}`);
        } else {
          this.logger.info(`✅ Marked printed history as deleted in MongoDB: ${localId} (matched: ${result.matchedCount}, modified: ${result.modifiedCount})`);
        }
      }
    } catch (error) {
      this.logger.error('Failed to mark printed history as deleted in MongoDB:', error);
      throw error;
    }
  }

  // Mark all user printed history as deleted in MongoDB (bulk delete)
  async markUserPrintedHistoryAsDeleted(username, deviceId) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('printed_history');

      // Mark ALL user records as deleted (including already deleted ones for consistency)
      // Use username only for bulk operations (device_id is for logging purposes)
      const result = await collection.updateMany(
        {
          username: username
          // Removed device_id filter - update ALL records for this user across all devices
        },
        {
          $set: {
            is_deleted: true,
            deleted_at: new Date(),
            synced_at: new Date()
          }
        }
      );

      if (result.matchedCount === 0) {
        this.logger.warn(`⚠️ No MongoDB records found for user: ${username} for bulk deletion`);
      } else {
        this.logger.info(`✅ Bulk deleted ${result.modifiedCount}/${result.matchedCount} printed history records in MongoDB for user: ${username} (including previously deleted)`);
      }
      return result;
    } catch (error) {
      this.logger.error('Failed to mark user printed history as deleted in MongoDB:', error);
      throw error;
    }
  }

  // Restore printed history in MongoDB (mark as not deleted)
  async restorePrintedHistoryInMongo(localId, deviceId) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('printed_history');

      if (localId) {
        // Mark specific record as restored (not deleted) using local_id only
        const result = await collection.updateOne(
          { local_id: localId },
          {
            $set: {
              is_deleted: false,
              synced_at: new Date()
            },
            $unset: {
              deleted_at: ""
            }
          }
        );

        if (result.matchedCount === 0) {
          this.logger.warn(`⚠️ No MongoDB record found with local_id: ${localId} for restoration`);
        } else if (result.modifiedCount === 0) {
          this.logger.warn(`⚠️ MongoDB record found but not modified for local_id: ${localId}`);
        } else {
          this.logger.info(`✅ Restored printed history in MongoDB: ${localId} (matched: ${result.matchedCount}, modified: ${result.modifiedCount})`);
        }
      }
    } catch (error) {
      this.logger.error('Failed to restore printed history in MongoDB:', error);
      throw error;
    }
  }

  // Restore all user printed history in MongoDB (bulk restore)
  async restoreUserPrintedHistoryInMongo(username, deviceId) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('printed_history');

      // Mark all user records as restored (not deleted)
      // Use username only for bulk operations (device_id is for logging purposes)
      const result = await collection.updateMany(
        { username: username, is_deleted: true },
        {
          $set: {
            is_deleted: false,
            synced_at: new Date()
          },
          $unset: {
            deleted_at: ""
          }
        }
      );

      if (result.matchedCount === 0) {
        this.logger.warn(`⚠️ No deleted MongoDB records found for user: ${username} for bulk restoration`);
      } else {
        this.logger.info(`✅ Bulk restored ${result.modifiedCount}/${result.matchedCount} printed history records in MongoDB for user: ${username}`);
      }
      return result.modifiedCount;
    } catch (error) {
      this.logger.error('Failed to restore user printed history in MongoDB:', error);
      throw error;
    }
  }

  // Sync all printed history to MongoDB (initial sync)
  async syncPrintedHistoryToMongo(localPrintedHistory) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      this.logger.info(`Starting printed history sync to MongoDB: ${localPrintedHistory.length} records`);

      const collection = this.db.collection('printed_history');

      if (localPrintedHistory.length > 0) {
        const mongoPrintedHistory = localPrintedHistory.map(record => ({
          local_id: record.id,
          comment_pk: record.comment_pk || record.comment_id, // Support both new and old format
          username: record.username,
          comment_text: record.comment_text,
          print_type: record.print_type || 'comment',
          printed_at: new Date(record.printed_at),
          created_at: new Date(record.created_at || record.printed_at),
          device_id: record.device_id || 'unknown',
          is_deleted: Boolean(record.is_deleted), // Convert SQLite integer to boolean
          synced_at: new Date(),
          synced_from_local: true
        }));

        // Use bulk operations for better performance - simplified filter using local_id only
        const bulkOps = mongoPrintedHistory.map(record => ({
          replaceOne: {
            filter: { local_id: record.local_id },
            replacement: record,
            upsert: true
          }
        }));

        await collection.bulkWrite(bulkOps);
        this.logger.info(`Synced ${mongoPrintedHistory.length} printed history records to MongoDB`);
      }

      return { success: true, synced: localPrintedHistory.length };
    } catch (error) {
      this.logger.error('Failed to sync printed history to MongoDB:', error);
      throw error;
    }
  }

  // Sync printed history from MongoDB to local
  async syncPrintedHistoryFromMongo() {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('printed_history');
      const printedHistory = await collection.find({}).sort({ printed_at: -1 }).toArray();

      // Convert MongoDB format to local format
      const localPrintedHistory = printedHistory.map(record => {
        const converted = {
          id: record.local_id,
          comment_id: record.comment_id,
          username: record.username,
          comment_text: record.comment_text,
          print_type: record.print_type || 'comment',
          printed_at: record.printed_at.toISOString(),
          created_at: record.created_at ? record.created_at.toISOString() : record.printed_at.toISOString(),
          device_id: record.device_id || 'unknown',
          is_deleted: record.is_deleted || false,
          synced_at: record.synced_at ? record.synced_at.toISOString() : null
        };

        // Debug logging for boolean conversion (only for deleted records)
        if (record.is_deleted) {
          this.logger.info(`🔍 MongoDB→Local conversion debug - MongoDB: ${record.is_deleted} (${typeof record.is_deleted}) → Local: ${converted.is_deleted} (${typeof converted.is_deleted}) for ${record.username}`);
        }

        return converted;
      });

      this.logger.info(`Retrieved ${localPrintedHistory.length} printed history records from MongoDB`);
      return localPrintedHistory;
    } catch (error) {
      this.logger.error('Failed to sync printed history from MongoDB:', error);
      throw error;
    }
  }

  // ==================== SEND_ONCE HISTORY SYNC METHODS ====================

  // Sync single send_once history record to MongoDB
  async syncSingleSendOnceHistoryToMongo(sendOnceHistoryData) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('send_once_history');

      const mongoSendOnceHistory = {
        local_id: sendOnceHistoryData.id,
        username: sendOnceHistoryData.username,
        template_name: sendOnceHistoryData.template_name,
        customer_type: sendOnceHistoryData.customer_type,
        template_type: sendOnceHistoryData.template_type,
        sent_at: new Date(sendOnceHistoryData.sent_at),
        created_at: new Date(sendOnceHistoryData.created_at || sendOnceHistoryData.sent_at),
        updated_at: new Date(sendOnceHistoryData.updated_at || sendOnceHistoryData.created_at || sendOnceHistoryData.sent_at),
        device_id: sendOnceHistoryData.device_id || 'unknown',
        is_deleted: Boolean(sendOnceHistoryData.is_deleted), // Convert SQLite integer to boolean
        synced_at: new Date()
      };

      // Use unique business key for upsert (username + template_name + customer_type + template_type)
      await collection.replaceOne(
        {
          username: sendOnceHistoryData.username,
          template_name: sendOnceHistoryData.template_name,
          customer_type: sendOnceHistoryData.customer_type,
          template_type: sendOnceHistoryData.template_type
        },
        mongoSendOnceHistory,
        { upsert: true }
      );

      this.logger.info(`Synced send_once history to MongoDB: ${sendOnceHistoryData.username} - ${sendOnceHistoryData.template_name}`);
    } catch (error) {
      this.logger.error('Failed to sync send_once history to MongoDB:', error);
      throw error;
    }
  }

  // Mark send_once history as deleted in MongoDB
  async markSendOnceHistoryAsDeleted(localId, deviceId) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('send_once_history');

      if (localId && deviceId) {
        // Mark specific record as deleted
        await collection.updateOne(
          { local_id: localId, device_id: deviceId },
          {
            $set: {
              is_deleted: true,
              deleted_at: new Date(),
              synced_at: new Date()
            }
          }
        );
        this.logger.info(`Marked send_once history as deleted in MongoDB: ${localId}`);
      }
    } catch (error) {
      this.logger.error('Failed to mark send_once history as deleted in MongoDB:', error);
      throw error;
    }
  }

  // Sync all send_once history to MongoDB (initial sync)
  async syncSendOnceHistoryToMongo(localSendOnceHistory) {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      this.logger.info(`Starting send_once history sync to MongoDB: ${localSendOnceHistory.length} records`);

      const collection = this.db.collection('send_once_history');

      if (localSendOnceHistory.length > 0) {
        const mongoSendOnceHistory = localSendOnceHistory.map(record => ({
          local_id: record.id,
          username: record.username,
          template_name: record.template_name,
          customer_type: record.customer_type,
          template_type: record.template_type,
          sent_at: new Date(record.sent_at),
          created_at: new Date(record.created_at || record.sent_at),
          updated_at: new Date(record.updated_at || record.created_at || record.sent_at),
          device_id: record.device_id || 'unknown',
          is_deleted: Boolean(record.is_deleted), // Convert SQLite integer to boolean
          synced_at: new Date(),
          synced_from_local: true
        }));

        // Use bulk operations for better performance
        const bulkOps = mongoSendOnceHistory.map(record => ({
          replaceOne: {
            filter: {
              username: record.username,
              template_name: record.template_name,
              customer_type: record.customer_type,
              template_type: record.template_type
            },
            replacement: record,
            upsert: true
          }
        }));

        await collection.bulkWrite(bulkOps);
        this.logger.info(`Synced ${mongoSendOnceHistory.length} send_once history records to MongoDB`);
      }

      return { success: true, synced: localSendOnceHistory.length };
    } catch (error) {
      this.logger.error('Failed to sync send_once history to MongoDB:', error);
      throw error;
    }
  }

  // Sync send_once history from MongoDB to local
  async syncSendOnceHistoryFromMongo() {
    try {
      if (!this.isConnected) {
        throw new Error('Not connected to MongoDB');
      }

      const collection = this.db.collection('send_once_history');
      const sendOnceHistory = await collection.find({}).sort({ sent_at: -1 }).toArray();

      // Convert MongoDB format to local format
      const localSendOnceHistory = sendOnceHistory.map(record => ({
        id: record.local_id,
        username: record.username,
        template_name: record.template_name,
        customer_type: record.customer_type,
        template_type: record.template_type,
        sent_at: record.sent_at.toISOString(),
        created_at: record.created_at ? record.created_at.toISOString() : record.sent_at.toISOString(),
        updated_at: record.updated_at ? record.updated_at.toISOString() : (record.created_at ? record.created_at.toISOString() : record.sent_at.toISOString()),
        device_id: record.device_id || 'unknown',
        is_deleted: record.is_deleted || false,
        synced_at: record.synced_at ? record.synced_at.toISOString() : null
      }));

      this.logger.info(`Retrieved ${localSendOnceHistory.length} send_once history records from MongoDB`);
      return localSendOnceHistory;
    } catch (error) {
      this.logger.error('Failed to sync send_once history from MongoDB:', error);
      throw error;
    }
  }
}

module.exports = MongoDBService;
