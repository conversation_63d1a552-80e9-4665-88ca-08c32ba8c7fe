import React, { useState, useEffect } from 'react';
import { MessageSquare, Power, Settings, AlertCircle, CheckCircle, Info } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { getApiUrl } from '../config/api';
import toast from 'react-hot-toast';

const AutoMessageControl = () => {
  const { socket, isConnected } = useSocket();
  const [autoMessageSettings, setAutoMessageSettings] = useState({
    enabled: false,
    delayBetweenMessages: 2000, // ms
    maxRetries: 3,
    requireMessengerLogin: true
  });
  const [templates, setTemplates] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [stats, setStats] = useState({
    totalSent: 0,
    successRate: 0,
    lastSent: null
  });
  const [currentMessaging, setCurrentMessaging] = useState({
    isMessaging: false,
    username: null,
    status: null
  });

  useEffect(() => {
    loadAutoMessageSettings();
    loadTemplates();
    loadStats();
  }, []);

  // Listen for template updates via socket
  useEffect(() => {
    if (socket && isConnected) {
      const handleTemplateUpdate = (data) => {
        console.log('Template updated via socket, refreshing active templates:', data);
        loadTemplates();
      };

      const handleMessagingStart = (data) => {
        setCurrentMessaging({
          isMessaging: true,
          username: data.username,
          status: 'Đang gửi tin nhắn...'
        });
      };

      const handleMessagingComplete = (data) => {
        setCurrentMessaging({
          isMessaging: false,
          username: data.username,
          status: data.success ? 'Gửi thành công' : `Gửi thất bại: ${data.error}`
        });

        // Clear status after 3 seconds
        setTimeout(() => {
          setCurrentMessaging({
            isMessaging: false,
            username: null,
            status: null
          });
        }, 3000);
      };

      socket.on('template-updated', handleTemplateUpdate);
      socket.on('messaging-start', handleMessagingStart);
      socket.on('messaging-complete', handleMessagingComplete);

      return () => {
        socket.off('template-updated', handleTemplateUpdate);
        socket.off('messaging-start', handleMessagingStart);
        socket.off('messaging-complete', handleMessagingComplete);
      };
    }
  }, [socket, isConnected]);

  // Listen for template updates via window events (fallback)
  useEffect(() => {
    const handleTemplateUpdate = (event) => {
      console.log('Template updated via window event, refreshing active templates:', event.detail);
      loadTemplates();
    };

    window.addEventListener('template-updated', handleTemplateUpdate);

    return () => {
      window.removeEventListener('template-updated', handleTemplateUpdate);
    };
  }, []);

  const loadAutoMessageSettings = async () => {
    try {
      const response = await fetch(getApiUrl('/api/auto-message-settings'));
      const data = await response.json();
      if (data.success) {
        setAutoMessageSettings(prev => ({ ...prev, ...data.settings }));
      }
    } catch (error) {
      console.error('Failed to load auto message settings:', error);
    }
  };

  const loadTemplates = async () => {
    try {
      console.log('Loading active templates...');
      const response = await fetch(getApiUrl('/api/message-templates?active_only=true'));
      const data = await response.json();
      if (data.success) {
        console.log('Active templates loaded:', data.templates);
        setTemplates(data.templates);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  };

  const loadStats = async () => {
    try {
      const response = await fetch(getApiUrl('/api/auto-message-stats'));
      const data = await response.json();
      if (data.success) {
        setStats(data.stats);
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  };

  const saveAutoMessageSettings = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(getApiUrl('/api/auto-message-settings'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ settings: autoMessageSettings }),
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Cài đặt tin nhắn tự động đã được lưu!');
      } else {
        throw new Error(data.error || 'Failed to save settings');
      }
    } catch (error) {
      toast.error('Lỗi khi lưu cài đặt: ' + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleAutoMessage = async () => {
    const newEnabled = !autoMessageSettings.enabled;

    // Check if there are any active templates
    const activeTemplates = templates.filter(t => t.is_active);
    if (newEnabled && activeTemplates.length === 0) {
      toast.error('Vui lòng kích hoạt ít nhất một template trước khi bật tin nhắn tự động');
      return;
    }

    setAutoMessageSettings(prev => ({ ...prev, enabled: newEnabled }));

    // Auto save when toggling
    try {
      const response = await fetch(getApiUrl('/api/auto-message-settings'), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          settings: { ...autoMessageSettings, enabled: newEnabled }
        }),
      });

      const data = await response.json();
      if (data.success) {
        toast.success(newEnabled ? 'Tin nhắn tự động đã được BẬT' : 'Tin nhắn tự động đã được TẮT');
      } else {
        // Revert on error
        setAutoMessageSettings(prev => ({ ...prev, enabled: !newEnabled }));
        throw new Error(data.error || 'Failed to toggle auto message');
      }
    } catch (error) {
      toast.error('Lỗi khi thay đổi cài đặt: ' + error.message);
    }
  };

  const updateSetting = (key, value) => {
    setAutoMessageSettings(prev => ({ ...prev, [key]: value }));
  };

  const getStatusColor = () => {
    if (!isConnected) return 'text-gray-500';
    return autoMessageSettings.enabled ? 'text-green-600' : 'text-gray-600';
  };

  const getStatusText = () => {
    if (!isConnected) return 'Không có kết nối';
    return autoMessageSettings.enabled ? 'Đang hoạt động' : 'Đã tắt';
  };

  const getStatusIcon = () => {
    if (!isConnected) return AlertCircle;
    return autoMessageSettings.enabled ? CheckCircle : Power;
  };

  const StatusIcon = getStatusIcon();

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <MessageSquare className="h-6 w-6 text-purple-600" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Tin nhắn tự động</h3>
            <p className="text-sm text-gray-500">Tự động gửi tin nhắn khi in bình luận</p>
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Status */}
          <div className={`flex items-center space-x-2 ${getStatusColor()}`}>
            <StatusIcon className="h-5 w-5" />
            <span className="text-sm font-medium">{getStatusText()}</span>
          </div>

          {/* Toggle Switch */}
          <label className="relative inline-flex items-center cursor-pointer">
            <input
              type="checkbox"
              checked={autoMessageSettings.enabled}
              onChange={toggleAutoMessage}
              disabled={!isConnected}
              className="sr-only peer"
            />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600 disabled:opacity-50"></div>
          </label>
        </div>
      </div>

      {/* Current Messaging Status */}
      {(currentMessaging.isMessaging || currentMessaging.status) && (
        <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex items-center space-x-3">
            {currentMessaging.isMessaging ? (
              <div className="spinner w-5 h-5" />
            ) : (
              <CheckCircle className="h-5 w-5 text-green-600" />
            )}
            <div>
              <div className="font-medium text-gray-900">
                {currentMessaging.isMessaging ? 'Đang gửi tin nhắn' : 'Hoàn thành'}
              </div>
              <div className="text-sm text-gray-600">
                {currentMessaging.username && (
                  <span className="font-medium">@{currentMessaging.username}</span>
                )}
                {currentMessaging.status && (
                  <span className="ml-2">{currentMessaging.status}</span>
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Settings */}
      <div className="space-y-6">
        {/* Active Templates Display */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-3">
            Templates đang hoạt động
          </label>

          {/* New Customers Templates - Normal */}
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
              <h4 className="text-sm font-medium text-blue-700">Khách mới - Templates thường</h4>
            </div>
            <div className="space-y-2">
              {templates.filter(t => t.is_active && t.customer_type === 'regular' && (!t.template_type || t.template_type === 'normal')).map(template => (
                <div key={template.id} className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div>
                    <div className="font-medium text-blue-800">{template.name}</div>
                    <div className="text-sm text-blue-600">{template.description}</div>
                  </div>
                  <div className="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                    Thường
                  </div>
                </div>
              ))}
              {templates.filter(t => t.is_active && t.customer_type === 'regular' && (!t.template_type || t.template_type === 'normal')).length === 0 && (
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <div className="text-gray-500 text-sm">Chưa có template thường nào cho khách mới</div>
                </div>
              )}
            </div>
          </div>

          {/* New Customers Templates - Backup */}
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full mr-2"></div>
              <h4 className="text-sm font-medium text-orange-700">Khách mới - Templates dự bị</h4>
            </div>
            <div className="space-y-2">
              {templates.filter(t => t.is_active && t.customer_type === 'regular' && t.template_type === 'backup').map(template => (
                <div key={template.id} className="flex items-center justify-between p-3 bg-orange-50 border border-orange-200 rounded-lg">
                  <div>
                    <div className="font-medium text-orange-800">{template.name}</div>
                    <div className="text-sm text-orange-600">{template.description}</div>
                  </div>
                  <div className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
                    Dự bị
                  </div>
                </div>
              ))}
              {templates.filter(t => t.is_active && t.customer_type === 'regular' && t.template_type === 'backup').length === 0 && (
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <div className="text-gray-500 text-sm">Chưa có template dự bị nào cho khách mới</div>
                </div>
              )}
            </div>
          </div>

          {/* VIP/Regular Customers Templates - Normal */}
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <div className="w-3 h-3 bg-purple-500 rounded-full mr-2"></div>
              <h4 className="text-sm font-medium text-purple-700">Khách cũ - Templates thường</h4>
            </div>
            <div className="space-y-2">
              {templates.filter(t => t.is_active && t.customer_type === 'vip' && (!t.template_type || t.template_type === 'normal')).map(template => (
                <div key={template.id} className="flex items-center justify-between p-3 bg-purple-50 border border-purple-200 rounded-lg">
                  <div>
                    <div className="font-medium text-purple-800">{template.name}</div>
                    <div className="text-sm text-purple-600">{template.description}</div>
                  </div>
                  <div className="text-xs text-purple-600 bg-purple-100 px-2 py-1 rounded">
                    Thường
                  </div>
                </div>
              ))}
              {templates.filter(t => t.is_active && t.customer_type === 'vip' && (!t.template_type || t.template_type === 'normal')).length === 0 && (
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <div className="text-gray-500 text-sm">Chưa có template thường nào cho khách cũ</div>
                </div>
              )}
            </div>
          </div>

          {/* VIP/Regular Customers Templates - Backup */}
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
              <h4 className="text-sm font-medium text-red-700">Khách cũ - Templates dự bị</h4>
            </div>
            <div className="space-y-2">
              {templates.filter(t => t.is_active && t.customer_type === 'vip' && t.template_type === 'backup').map(template => (
                <div key={template.id} className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div>
                    <div className="font-medium text-red-800">{template.name}</div>
                    <div className="text-sm text-red-600">{template.description}</div>
                  </div>
                  <div className="text-xs text-red-600 bg-red-100 px-2 py-1 rounded">
                    Dự bị
                  </div>
                </div>
              ))}
              {templates.filter(t => t.is_active && t.customer_type === 'vip' && t.template_type === 'backup').length === 0 && (
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg text-center">
                  <div className="text-gray-500 text-sm">Chưa có template dự bị nào cho khách cũ</div>
                </div>
              )}
            </div>
          </div>

          {templates.filter(t => t.is_active).length === 0 && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg text-center">
              <div className="text-yellow-700 font-medium">⚠️ Chưa có template nào được kích hoạt</div>
              <div className="text-xs text-yellow-600 mt-1">
                Vào phần "Templates tin nhắn" để kích hoạt templates
              </div>
            </div>
          )}

          <p className="text-xs text-gray-500 mt-3">
            💡 Hệ thống sẽ tự động chọn template phù hợp dựa trên loại khách hàng (mới/cũ)
          </p>
        </div>

        {/* Advanced Settings */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Độ trễ giữa tin nhắn (ms)
            </label>
            <input
              type="number"
              min="1000"
              max="10000"
              step="500"
              value={autoMessageSettings.delayBetweenMessages}
              onChange={(e) => updateSetting('delayBetweenMessages', parseInt(e.target.value))}
              className="input"
              disabled={!isConnected}
            />
            <p className="text-xs text-gray-500 mt-1">
              Thời gian chờ giữa các tin nhắn (tránh spam)
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Số lần thử lại
            </label>
            <input
              type="number"
              min="1"
              max="5"
              value={autoMessageSettings.maxRetries}
              onChange={(e) => updateSetting('maxRetries', parseInt(e.target.value))}
              className="input"
              disabled={!isConnected}
            />
            <p className="text-xs text-gray-500 mt-1">
              Số lần thử lại khi gửi tin nhắn thất bại
            </p>
          </div>
        </div>

        {/* Options */}
        <div className="space-y-3">

          <div className="flex items-center">
            <input
              type="checkbox"
              id="requireMessengerLogin"
              checked={autoMessageSettings.requireMessengerLogin}
              onChange={(e) => updateSetting('requireMessengerLogin', e.target.checked)}
              disabled={!isConnected}
              className="mr-3"
            />
            <label htmlFor="requireMessengerLogin" className="text-sm font-medium text-gray-700">
              Yêu cầu đăng nhập Messenger
            </label>
          </div>
        </div>

        {/* Save Button */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-200">
          <button
            onClick={saveAutoMessageSettings}
            disabled={!isConnected || isLoading}
            className="btn-primary"
          >
            {isLoading ? (
              <div className="spinner mr-2" />
            ) : (
              <Settings className="h-4 w-4 mr-2" />
            )}
            Lưu cài đặt
          </button>

          {/* Stats */}
          <div className="text-sm text-gray-500">
            <div className="flex items-center space-x-4">
              <span>Đã gửi: {stats.totalSent}</span>
              <span>Thành công: {stats.successRate}%</span>
              {stats.lastSent && (
                <span>Lần cuối: {new Date(stats.lastSent).toLocaleString('vi-VN')}</span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Info */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <div className="flex items-start space-x-3">
          <Info className="h-5 w-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">Cách hoạt động:</p>
            <ul className="list-disc list-inside space-y-1 text-xs">
              <li>Khi bạn in một bình luận, hệ thống sẽ tự động gửi tin nhắn đến người comment</li>
              <li>Hệ thống tự động phát hiện khách mới/cũ dựa trên lịch sử in và chọn template phù hợp</li>
              <li>Template sẽ được điền với thông tin thực tế (tên user, nội dung comment)</li>
              <li>Cần đăng nhập Instagram Messenger trước khi sử dụng</li>
              <li>Chỉ gửi tin nhắn cho templates đã được kích hoạt</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoMessageControl;
