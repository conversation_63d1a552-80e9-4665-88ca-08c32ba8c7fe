# New Database Schema Design for API-Based Comment Flow

## Current Issues
1. **Duplicate Records**: 6278 local vs 2013 MongoDB (4x duplication)
2. **Inconsistent Device IDs**: Old records missing device_id causing sync conflicts
3. **Legacy Schema**: Still designed for DOM scraping, not API flow
4. **Complex Sync Logic**: `local_id + device_id` key causing confusion

## Proposed Solution: Complete Reset & Restructure

### 1. **COMMENTS (Session Only - No Database Table)**
```javascript
// In-memory session storage only
sessionComments = [
  {
    pk: "17841234567890123456",        // Instagram API pk (unique)
    username: "user123",
    text: "Hello world!",
    timestamp: "2024-06-30T10:30:00Z",
    source: "instagram_api",
    user_id: 12345,
    full_name: "User Name",
    profile_pic_url: "...",
    raw_api_data: {...}               // Full API response
  }
]
```

### 2. **PRINTED_HISTORY (Simplified)**
```sql
CREATE TABLE printed_history (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  comment_pk TEXT NOT NULL,           -- Instagram pk (direct from API)
  username TEXT NOT NULL,
  comment_text TEXT NOT NULL,
  print_type TEXT DEFAULT 'comment', -- 'comment' or 'backup'
  printed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  device_id TEXT NOT NULL,           -- Always required, no fallback
  is_deleted INTEGER DEFAULT 0,
  synced_at DATETIME DEFAULT NULL
);
```

### 3. **MONGODB SYNC (Simplified)**
```javascript
// MongoDB collection: printed_history
{
  _id: ObjectId,
  local_id: 123,                     // From SQLite id
  comment_pk: "17841234567890123456", // Instagram pk
  username: "user123",
  comment_text: "Hello world!",
  print_type: "comment",
  printed_at: ISODate,
  device_id: "device_123456789_abc", // Always present
  is_deleted: false,                 // Boolean
  synced_at: ISODate
}
```

## Key Changes

### 1. **Unified ID System**
- **API**: Use Instagram `pk` directly
- **Database**: Store `comment_pk` (not `comment_id`)
- **Sync**: Use `local_id` only (device_id always same per device)

### 2. **Device ID Strategy**
- **Single Device ID**: One device_id per installation
- **Always Required**: No fallback to 'unknown'
- **Consistent**: Same device_id for all records

### 3. **Simplified Sync Logic**
```javascript
// OLD (problematic)
const key = `${record.id}_${record.device_id}`;

// NEW (simplified)
const key = record.id; // local_id is unique per device
```

### 4. **Clean Data Flow**
```
Instagram API (pk) 
  ↓
Session Storage (pk)
  ↓
Print Process (pk)
  ↓
Database (comment_pk)
  ↓
MongoDB (comment_pk + local_id)
```

## Migration Plan

### Phase 1: Backup Important Data
- Export customers
- Export templates & settings
- Export thread IDs
- Export send_once history

### Phase 2: Clean Reset
- DROP printed_history table
- RECREATE with new schema
- CLEAR MongoDB printed_history collection
- RESET device_id if needed

### Phase 3: Update Code
- Update `markCommentAsPrinted()` to use `comment_pk`
- Update MongoDB sync to use simplified logic
- Update frontend to handle new schema

### Phase 4: Test & Verify
- Test API comment flow
- Test print process
- Test MongoDB sync
- Verify no duplicates

## Benefits
1. **No More Duplicates**: Clean sync logic
2. **Consistent IDs**: pk throughout entire flow
3. **Simplified Logic**: Single device per installation
4. **Better Performance**: No complex key matching
5. **Future Proof**: Designed for API-only flow

## Risk Mitigation
- **Backup before reset**: All important data preserved
- **Gradual rollout**: Test each phase thoroughly
- **Rollback plan**: Keep backup until verified working
- **Monitor closely**: Watch for any new issues
