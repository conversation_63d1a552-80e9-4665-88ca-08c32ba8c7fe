/**
 * <PERSON><PERSON><PERSON> để kill tất cả Chrome Testing processes ngay lập tức
 * <PERSON><PERSON> dụng khi cần cleanup manual hoặc test cleanup logic
 */

const { exec, execSync } = require('child_process');

async function listChromeTestingProcesses() {
  console.log('🔍 Scanning for Chrome Testing processes...\n');
  
  if (process.platform !== 'win32') {
    console.log('❌ This script is designed for Windows only');
    return;
  }

  const scanCommands = [
    {
      name: 'Chrome for Testing',
      cmd: 'wmic process where "name=\'chrome.exe\' and commandline like \'%Google Chrome for Testing%\'" get ProcessId,CommandLine /format:csv'
    },
    {
      name: 'Puppeteer Chrome',
      cmd: 'wmic process where "name=\'chrome.exe\' and commandline like \'%puppeteer%\'" get ProcessId,CommandLine /format:csv'
    },
    {
      name: 'Chrome with Testing Flags',
      cmd: 'wmic process where "name=\'chrome.exe\' and (commandline like \'%test-type%\' or commandline like \'%automation%\' or commandline like \'%headless%\')" get ProcessId,CommandLine /format:csv'
    },
    {
      name: 'Chrome with Puppeteer Flags',
      cmd: 'wmic process where "name=\'chrome.exe\' and (commandline like \'%--no-sandbox%\' or commandline like \'%--disable-setuid-sandbox%\')" get ProcessId,CommandLine /format:csv'
    }
  ];

  let totalFound = 0;

  for (const { name, cmd } of scanCommands) {
    console.log(`🔍 Scanning: ${name}`);
    
    await new Promise((resolve) => {
      exec(cmd, (error, stdout) => {
        if (error) {
          console.log(`   ❌ Error: ${error.message}`);
        } else {
          const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node'));
          const processes = lines.map(line => {
            const parts = line.split(',');
            if (parts.length >= 3) {
              return {
                pid: parts[1],
                commandLine: parts[2] || ''
              };
            }
            return null;
          }).filter(p => p && p.pid);
          
          if (processes.length === 0) {
            console.log(`   ✅ No processes found`);
          } else {
            console.log(`   🎯 Found ${processes.length} processes:`);
            processes.forEach((proc, index) => {
              console.log(`      ${index + 1}. PID: ${proc.pid}`);
              console.log(`         Command: ${proc.commandLine.substring(0, 80)}...`);
            });
            totalFound += processes.length;
          }
        }
        resolve();
      });
    });
    console.log('');
  }

  return totalFound;
}

async function killAllChromeTestingProcesses() {
  console.log('🚨 KILLING ALL CHROME TESTING PROCESSES...\n');
  
  if (process.platform !== 'win32') {
    console.log('❌ This script is designed for Windows only');
    return;
  }

  const killCommands = [
    {
      name: 'Chrome for Testing',
      cmd: 'wmic process where "name=\'chrome.exe\' and commandline like \'%Google Chrome for Testing%\'" delete'
    },
    {
      name: 'Puppeteer Chrome',
      cmd: 'wmic process where "name=\'chrome.exe\' and commandline like \'%puppeteer%\'" delete'
    },
    {
      name: 'Chrome Testing Flags',
      cmd: 'wmic process where "name=\'chrome.exe\' and (commandline like \'%test-type%\' or commandline like \'%automation%\' or commandline like \'%headless%\')" delete'
    },
    {
      name: 'Chrome Puppeteer Flags',
      cmd: 'wmic process where "name=\'chrome.exe\' and (commandline like \'%--no-sandbox%\' or commandline like \'%--disable-setuid-sandbox%\' or commandline like \'%--disable-dev-shm-usage%\')" delete'
    },
    {
      name: 'Chrome Remote Debugging',
      cmd: 'wmic process where "name=\'chrome.exe\' and commandline like \'%--remote-debugging-port%\'" delete'
    },
    {
      name: 'Chrome Temp Directories',
      cmd: 'wmic process where "name=\'chrome.exe\' and (commandline like \'%temp%\' or commandline like \'%tmp%\')" delete'
    }
  ];

  for (const { name, cmd } of killCommands) {
    console.log(`🔫 Killing: ${name}`);
    
    await new Promise((resolve) => {
      exec(cmd, (error) => {
        if (error && !error.message.includes('not found')) {
          console.log(`   ❌ Error: ${error.message}`);
        } else {
          console.log(`   ✅ ${name} processes killed`);
        }
        resolve();
      });
    });
    
    // Small delay between commands
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n✅ Chrome Testing cleanup completed!');
}

async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--kill') || args.includes('-k')) {
    // Kill mode
    const totalBefore = await listChromeTestingProcesses();
    
    if (totalBefore === 0) {
      console.log('✅ No Chrome Testing processes found to kill');
      return;
    }
    
    console.log(`\n🎯 Found ${totalBefore} Chrome Testing processes total`);
    console.log('⚠️  Proceeding with cleanup in 3 seconds...');
    
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    await killAllChromeTestingProcesses();
    
    console.log('\n🔍 Verifying cleanup...');
    const totalAfter = await listChromeTestingProcesses();
    
    if (totalAfter === 0) {
      console.log('🎉 SUCCESS: All Chrome Testing processes have been killed!');
    } else {
      console.log(`⚠️  WARNING: ${totalAfter} Chrome Testing processes still running`);
    }
    
  } else {
    // Scan mode (default)
    console.log('🔍 CHROME TESTING PROCESS SCANNER');
    console.log('================================\n');
    
    const total = await listChromeTestingProcesses();
    
    if (total === 0) {
      console.log('✅ No Chrome Testing processes found');
    } else {
      console.log(`🎯 Total Chrome Testing processes found: ${total}`);
      console.log('\n💡 To kill all Chrome Testing processes, run:');
      console.log('   node kill_all_chrome_testing.js --kill');
    }
  }
}

// Run the script
if (require.main === module) {
  main()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Script failed:', error);
      process.exit(1);
    });
}

module.exports = { listChromeTestingProcesses, killAllChromeTestingProcesses };
