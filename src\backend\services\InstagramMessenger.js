const puppeteer = require('puppeteer');
const EventEmitter = require('events');
const winston = require('winston');

class InstagramMessenger extends EventEmitter {
  constructor(database = null) {
    super();
    this.browser = null;
    this.page = null;
    this.isRunning = false;
    this.isLoggedIn = false;
    this.messageQueue = [];
    this.isProcessingQueue = false;
    this.database = database;
    this.currentUsername = null;

    // Performance monitoring and restart configuration - OPTIMIZED for better performance
    this.messagesProcessed = 0;
    this.maxMessagesBeforeRestart = 2000; // Increased to reduce restart frequency
    this.memoryCheckInterval = null;
    this.maxMemoryUsageMB = 3000; // Increased memory limit to reduce restarts
    this.lastRestartTime = Date.now();
    this.minRestartIntervalMs = 600000; // Increased to 10 minutes to reduce restart frequency
    this.savedCookies = null; // Store cookies for restart
    this.savedCredentials = null; // Store credentials for restart
    this.retryRestartTimeout = null; // Timeout for retry restart

    // Message processing protection
    this.isCurrentlySendingMessage = false; // Flag to protect ongoing message sending
    this.currentMessageData = null; // Store current message being processed
    this.pendingRestart = false; // Flag to indicate restart is needed but waiting
    this.pendingBrowserCrashRecovery = false; // Flag to indicate browser crash recovery is needed

    // Browser connection monitoring
    this.browserConnectionCheckInterval = null;

    // Message loading failure detection
    this.messageLoadFailures = 0;
    this.maxMessageLoadFailures = 3; // Max failures before restart
    this.lastMessageLoadCheck = 0;

    this.logger = winston.createLogger({
      level: 'info',
      format: winston.format.simple(),
      transports: [new winston.transports.Console()]
    });
  }

  async start(credentials, savedCookies = null) {
    try {
      this.logger.info('Starting Instagram Messenger...');
      this.currentUsername = credentials?.username;

      // Load settings from database
      await this.loadSettingsFromDatabase();

      // Store credentials and cookies for potential restart
      this.savedCredentials = credentials;
      this.savedCookies = savedCookies;

      // Launch separate browser for messaging (NO persistent profile)
      this.browser = await puppeteer.launch({
        headless: false, // Keep visible for debugging
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-blink-features=AutomationControlled',
          '--disable-features=VizDisplayCompositor',
          '--disable-web-security',
          '--disable-dev-shm-usage',
          '--test-type', // Add test-type flag to identify messenger processes
          '--automation' // Add automation flag for identification
        ],
        defaultViewport: null
        // NO userDataDir - use fresh browser session each time
      });

      this.page = await this.browser.newPage();

      // Store browser process ID for targeted cleanup
      this.browserProcessId = this.browser.process()?.pid;
      if (this.browserProcessId) {
        this.logger.info(`📋 Messenger browser PID: ${this.browserProcessId}`);
      }

      // Set user agent
      await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

      // Navigate to Instagram
      await this.page.goto('https://www.instagram.com/', { waitUntil: 'networkidle2' });

      // Load saved cookies from database if available and no explicit savedCookies provided
      let cookiesToUse = savedCookies;
      if (!cookiesToUse && this.database) {
        try {
          const cookieData = await this.database.getMessengerSavedCookies();
          if (cookieData && cookieData.cookies) {
            cookiesToUse = cookieData.cookies;
            this.logger.info(`Loaded saved cookies for user: ${cookieData.username}`);
            // Update current username to match saved cookies
            this.currentUsername = cookieData.username;
          }
        } catch (error) {
          this.logger.warn('Failed to load saved cookies from database:', error);
        }
      }

      // Login with cookies or credentials
      const loginResult = await this.login(credentials, cookiesToUse);

      // If using saved cookies, do quick verification
      if (savedCookies) {
        this.logger.info('Using saved cookies - doing quick login verification...');
        const loginVerified = await this.quickLoginVerification(15000); // Wait up to 15 seconds only

        if (!loginVerified) {
          throw new Error('Login verification failed with saved cookies');
        }
        this.logger.info('Login verified successfully with saved cookies');
      }

      // Check for notification popup after login
      await this.handleNotificationPopup();

      // Navigate to messages - use fast navigation for cookies
      try {
        if (savedCookies) {
          await this.fastNavigateToMessages();
        } else {
          await this.navigateToMessages();
        }
      } catch (navError) {
        // Handle various navigation errors
        if (navError.message.includes('Navigating frame was detached') ||
          navError.message.includes('frame was detached') ||
          navError.message.includes('Target closed') ||
          navError.message.includes('Session closed')) {

          this.logger.warn('Navigation error detected, attempting recovery...', navError.message);

          // Try to recover by checking if we can still access the page
          try {
            await this.page.waitForTimeout(3000);

            // Check if page is still accessible
            const currentUrl = await this.page.url().catch(() => 'error');
            this.logger.info(`Recovery check - Current URL: ${currentUrl}`);

            if (currentUrl !== 'error' && (currentUrl.includes('/direct/') || currentUrl.includes('instagram.com'))) {
              this.logger.info('Page is still accessible despite navigation error, continuing...');
            } else {
              // Try one more direct navigation
              this.logger.info('Attempting direct navigation recovery...');
              await this.page.goto('https://www.instagram.com/direct/inbox/', {
                waitUntil: 'domcontentloaded',
                timeout: 15000
              });
              await this.page.waitForTimeout(2000);
            }
          } catch (recoveryError) {
            this.logger.error('Recovery failed:', recoveryError.message);
            throw navError; // Re-throw original error
          }
        } else {
          throw navError; // Re-throw other navigation errors
        }
      }

      this.isRunning = true;
      this.emit('connected');
      this.emit('messenger-connected');

      // Start processing message queue
      this.startQueueProcessor();

      // Start memory monitoring
      this.startMemoryMonitoring();

      // Start browser connection monitoring
      this.startBrowserConnectionMonitoring();

      this.logger.info('Instagram Messenger started successfully');
      return { success: true, method: loginResult?.method || 'unknown' };

    } catch (error) {
      this.logger.error('Failed to start Instagram Messenger:', error);

      // Clean up on failure
      if (this.page) {
        await this.page.close().catch(() => { });
        this.page = null;
      }
      if (this.browser) {
        await this.browser.close().catch(() => { });
        this.browser = null;
      }

      this.isRunning = false;
      this.isLoggedIn = false;

      throw error;
    }
  }

  async login(credentials, savedCookies = null) {
    try {
      this.logger.info('Logging into Instagram Messenger...');

      // Try to use saved cookies first
      if (savedCookies && savedCookies.length > 0) {
        this.logger.info('Attempting to login with saved cookies...');

        try {
          // Set cookies
          await this.page.setCookie(...savedCookies);

          // Refresh page to apply cookies - use faster loading
          await this.page.reload({ waitUntil: 'domcontentloaded' });

          // Check if we're logged in using quick check
          const isLoggedIn = await this.quickCheckLoginStatus();

          if (isLoggedIn) {
            this.logger.info('Successfully logged in using saved cookies');
            this.isLoggedIn = true;
            return { success: true, method: 'cookies' };
          } else {
            this.logger.warn('Saved cookies are invalid, falling back to credential login');
          }
        } catch (cookieError) {
          this.logger.warn('Failed to use saved cookies:', cookieError);
        }
      }

      // Fallback to credential login
      if (!credentials || !credentials.username || !credentials.password) {
        throw new Error('No valid credentials provided and cookies failed');
      }

      return await this.loginWithCredentials(credentials);

    } catch (error) {
      this.logger.error('Login failed:', error);
      throw error;
    }
  }

  async loginWithCredentials(credentials) {
    try {
      this.logger.info('Logging in with credentials...');

      // Since we use fresh browser, we should never be "already logged in"
      // This prevents the bug where old cookies persist
      this.logger.info('Using fresh browser session - proceeding with credential login');

      // Always navigate to login page for fresh session
      await this.page.goto('https://www.instagram.com/accounts/login/', { waitUntil: 'networkidle2' });

      // Wait for login form
      await this.page.waitForSelector('input[name="username"]', { timeout: 10000 });

      // Clear any existing input
      await this.page.evaluate(() => {
        const usernameInput = document.querySelector('input[name="username"]');
        const passwordInput = document.querySelector('input[name="password"]');
        if (usernameInput) usernameInput.value = '';
        if (passwordInput) passwordInput.value = '';
      });

      // Fill credentials
      await this.page.type('input[name="username"]', credentials.username);
      await this.page.type('input[name="password"]', credentials.password);

      // Click login button
      await this.page.click('button[type="submit"]');

      // Wait for navigation or 2FA
      await this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 30000 });

      // Check current URL for 2FA
      const currentUrl = this.page.url();
      this.logger.info(`Current URL after login: ${currentUrl}`);

      // Handle 2FA if required
      if (currentUrl.includes('two_factor') || currentUrl.includes('challenge')) {
        this.logger.info('Two-factor authentication detected');
        return await this.handle2FA();
      }

      // Handle notification popup after login
      await this.handleNotificationPopup();

      // Check if we're logged in
      const isLoggedIn = await this.checkLoginStatus();

      if (!isLoggedIn) {
        throw new Error('Login failed - credentials may be incorrect');
      }

      this.isLoggedIn = true;
      this.logger.info('Successfully logged into Instagram with credentials');

      // Save cookies for future use
      const cookies = await this.page.cookies();
      if (this.database) {
        await this.database.saveMessengerCookies(credentials.username, cookies);
      }
      this.emit('cookies-available', { username: credentials.username, cookies });

      return { success: true, method: 'credentials' };

    } catch (error) {
      this.logger.error('Credential login attempt failed:', error);
      throw error;
    }
  }

  async handle2FA() {
    try {
      this.logger.info('Handling 2FA authentication...');

      // Emit event to notify frontend about 2FA requirement
      this.emit('2fa-required', {
        message: 'Two-factor authentication required',
        url: this.page.url()
      });

      // Wait for 2FA input field
      const twoFactorSelectors = [
        'input[name="verificationCode"]',
        'input[aria-label="Security code"]',
        'input[placeholder*="security code"]',
        'input[placeholder*="confirmation code"]'
      ];

      let twoFactorInput = null;
      for (const selector of twoFactorSelectors) {
        try {
          twoFactorInput = await this.page.waitForSelector(selector, { timeout: 5000 });
          if (twoFactorInput) {
            this.logger.info(`Found 2FA input: ${selector}`);
            break;
          }
        } catch (e) {
          continue;
        }
      }

      if (!twoFactorInput) {
        throw new Error('Could not find 2FA input field');
      }

      // Wait for user to complete 2FA manually
      this.logger.info('Waiting for user to complete 2FA...');

      // Poll for login completion
      const maxWaitTime = 300000; // 5 minutes
      const startTime = Date.now();

      while (Date.now() - startTime < maxWaitTime) {
        await this.page.waitForTimeout(2000);

        const isLoggedIn = await this.checkLoginStatus();
        if (isLoggedIn) {
          this.logger.info('2FA completed successfully');
          this.isLoggedIn = true;

          // Save cookies after successful 2FA
          const cookies = await this.page.cookies();
          if (this.database) {
            await this.database.saveMessengerCookies(this.currentUsername, cookies);
          }
          this.emit('cookies-available', { username: this.currentUsername, cookies });

          return { success: true, method: '2fa' };
        }

        // Check if still on 2FA page
        const currentUrl = this.page.url();
        if (!currentUrl.includes('two_factor') && !currentUrl.includes('challenge')) {
          // Moved away from 2FA page, check login status
          const finalCheck = await this.checkLoginStatus();
          if (finalCheck) {
            this.logger.info('2FA completed, login successful');
            this.isLoggedIn = true;

            const cookies = await this.page.cookies();
            if (this.database) {
              await this.database.saveMessengerCookies(this.currentUsername, cookies);
            }
            this.emit('cookies-available', { username: this.currentUsername, cookies });

            return { success: true, method: '2fa' };
          }
        }
      }

      throw new Error('2FA timeout - please try again');

    } catch (error) {
      this.logger.error('Error handling 2FA:', error);
      throw error;
    }
  }

  async quickLoginVerification(maxWaitTime = 10000) {
    try {
      this.logger.info(`Quick login verification (max ${maxWaitTime}ms)...`);
      const startTime = Date.now();

      // For cookies, do immediate check first
      const immediateCheck = await this.quickCheckLoginStatus();
      if (immediateCheck) {
        this.logger.info('Quick login verification successful immediately');
        return true;
      }

      // If immediate check fails, wait a bit and try again
      while (Date.now() - startTime < maxWaitTime) {
        await this.page.waitForTimeout(500); // Very short wait

        const isLoggedIn = await this.quickCheckLoginStatus();
        if (isLoggedIn) {
          this.logger.info('Quick login verification successful');
          return true;
        }

        // Log progress every 3 seconds
        const elapsed = Date.now() - startTime;
        if (elapsed % 3000 < 500) {
          this.logger.info(`Quick verification... (${Math.round(elapsed / 1000)}s elapsed)`);
        }
      }

      this.logger.warn('Quick login verification timeout');
      return false;

    } catch (error) {
      this.logger.error('Error during quick login verification:', error);
      return false;
    }
  }

  async waitForLoginVerification(maxWaitTime = 30000) {
    try {
      this.logger.info(`Waiting for login verification (max ${maxWaitTime}ms)...`);
      const startTime = Date.now();

      while (Date.now() - startTime < maxWaitTime) {
        // Use quick check for faster verification
        const isLoggedIn = await this.quickCheckLoginStatus();
        if (isLoggedIn) {
          this.logger.info('Login verification successful');
          return true;
        }

        // Wait shorter intervals for faster response
        await this.page.waitForTimeout(1000);

        // Log progress every 5 seconds
        const elapsed = Date.now() - startTime;
        if (elapsed % 5000 < 1000) {
          this.logger.info(`Still waiting for login verification... (${Math.round(elapsed / 1000)}s elapsed)`);
        }
      }

      this.logger.warn('Login verification timeout');
      return false;

    } catch (error) {
      this.logger.error('Error during login verification:', error);
      return false;
    }
  }

  async quickCheckLoginStatus() {
    try {
      this.logger.info('Quick login check: Testing access to /direct/inbox/');

      // Try to navigate to inbox directly with minimal waiting
      const response = await this.page.goto('https://www.instagram.com/direct/inbox/', {
        waitUntil: 'domcontentloaded',
        timeout: 10000
      });

      // Check URL immediately after navigation
      let currentUrl = this.page.url();
      this.logger.info(`Quick login check - Immediate URL: ${currentUrl}`);

      // If already on direct page, we're good
      if (currentUrl.includes('/direct/')) {
        // Quick popup dismissal if needed
        await this.quickDismissPopup();
        this.logger.info('Successfully accessed inbox immediately - logged in');
        return true;
      }

      // If redirected to login immediately, we're not logged in
      if (currentUrl.includes('/accounts/login/')) {
        this.logger.info('Redirected to login page immediately - not logged in');
        return false;
      }

      // Wait a short time for potential redirects
      await this.page.waitForTimeout(800);

      // Quick popup dismissal during wait
      await this.quickDismissPopup();

      // Check URL again after short wait
      currentUrl = this.page.url();
      this.logger.info(`Quick login check - Final URL: ${currentUrl}`);

      // Check if we're on 2FA page
      if (currentUrl.includes('two_factor') || currentUrl.includes('challenge')) {
        this.logger.info('On 2FA page, waiting for verification');
        return false;
      }

      // Check if we got redirected to login page
      if (currentUrl.includes('/accounts/login/')) {
        this.logger.info('Redirected to login page - not logged in');
        return false;
      }

      // Check if we're on the inbox page
      if (currentUrl.includes('/direct/inbox/') || currentUrl.includes('/direct/')) {
        this.logger.info('Successfully accessed inbox - logged in');
        return true;
      }

      // If we're on Instagram homepage, that's also a good sign
      if (currentUrl === 'https://www.instagram.com/' || currentUrl === 'https://www.instagram.com') {
        this.logger.info('On Instagram homepage - likely logged in');
        return true;
      }

      this.logger.info(`Unexpected URL after inbox access attempt: ${currentUrl}`);
      return false;

    } catch (error) {
      this.logger.error('Error during quick login check:', error);
      return false;
    }
  }

  async checkLoginStatus() {
    try {
      this.logger.info('Checking login status by testing access to /direct/inbox/');

      // Try to navigate to inbox directly
      const response = await this.page.goto('https://www.instagram.com/direct/inbox/', {
        waitUntil: 'domcontentloaded',
        timeout: 12000
      });

      // Check URL immediately after navigation
      let currentUrl = this.page.url();
      this.logger.info(`Login check - Immediate URL: ${currentUrl}`);

      // If already on direct page, we're good
      if (currentUrl.includes('/direct/')) {
        // Quick popup dismissal if needed
        await this.quickDismissPopup();
        this.logger.info('Successfully accessed inbox immediately - logged in');
        return true;
      }

      // If redirected to login immediately, we're not logged in
      if (currentUrl.includes('/accounts/login/')) {
        this.logger.info('Redirected to login page immediately - not logged in');
        return false;
      }

      // Wait a short time for potential redirects
      await this.page.waitForTimeout(1000);

      // Quick popup dismissal during wait
      await this.quickDismissPopup();

      // Check URL again after short wait
      currentUrl = this.page.url();
      this.logger.info(`Login check - Final URL: ${currentUrl}`);

      // Check if we're on 2FA page
      if (currentUrl.includes('two_factor') || currentUrl.includes('challenge')) {
        this.logger.info('On 2FA page, waiting for verification');
        return false;
      }

      // Check if we got redirected to login page
      if (currentUrl.includes('/accounts/login/')) {
        this.logger.info('Redirected to login page - not logged in');
        return false;
      }

      // Check if we're on the inbox page
      if (currentUrl.includes('/direct/inbox/') || currentUrl.includes('/direct/')) {
        this.logger.info('Successfully accessed inbox - logged in');

        // Try to extract username from page if possible (optional, with short timeout)
        try {
          await this.page.waitForTimeout(300); // Very short wait for page elements

          const accountNameSelectors = [
            'nav span[dir="auto"]', // Navigation username
            'div[role="main"] span[dir="auto"]', // Main content username
            'header span[dir="auto"]' // Header username
          ];

          for (const selector of accountNameSelectors) {
            try {
              const elements = await this.page.$$(selector);
              for (const element of elements) {
                const text = await this.page.evaluate(el => el.textContent?.trim(), element);
                if (text && text.length > 0 && !text.includes('Direct') && !text.includes('Inbox') && !text.includes('Message')) {
                  this.logger.info(`Found account name: "${text}"`);
                  this.currentUsername = text;
                  break;
                }
              }
              if (this.currentUsername) break;
            } catch (e) {
              // Continue to next selector
            }
          }
        } catch (e) {
          // Username extraction is optional, don't fail login check
          this.logger.debug('Could not extract username, but login is confirmed');
        }

        return true;
      }

      // If we're on Instagram homepage, that's also a good sign
      if (currentUrl === 'https://www.instagram.com/' || currentUrl === 'https://www.instagram.com') {
        this.logger.info('On Instagram homepage - likely logged in');
        return true;
      }

      this.logger.info(`Login check failed - unexpected URL: ${currentUrl}`);
      return false;

    } catch (error) {
      this.logger.error('Error checking login status:', error);
      return false;
    }
  }

  async quickDismissPopup() {
    try {
      // Very quick popup dismissal - don't wait long
      const dialogExists = await this.page.$('div[role="dialog"]') ||
        await this.page.$('div[role="presentation"]');

      if (!dialogExists) {
        return false;
      }

      // Try to find and click "Not Now" button immediately
      const buttons = await this.page.$$('button, [role="button"]');
      for (const button of buttons) {
        try {
          const text = await this.page.evaluate(el => el.textContent?.trim(), button);
          if (text && (text.includes('Not Now') || text.includes('Không phải bây giờ') || text.includes('Turn Off') || text.includes('Tắt'))) {
            await button.click();
            this.logger.info('Quick popup dismissed');
            return true;
          }
        } catch (e) {
          continue;
        }
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  async handleNotificationPopup() {
    try {
      this.logger.info('Checking for notification popup...');

      // Wait a shorter time for popup to appear
      await this.page.waitForTimeout(800);

      // Quick check if notification dialog exists
      const dialogExists = await this.page.$('div[role="dialog"]') ||
        await this.page.$('div[role="presentation"]');

      if (!dialogExists) {
        this.logger.info('No notification dialog found');
        return false;
      }

      this.logger.info('Notification dialog detected, looking for "Not Now" button...');

      // Try to find and click "Not Now" button quickly
      let notNowClicked = false;

      // Method 1: Try text-based selection with faster approach
      try {
        const buttons = await this.page.$$('button, [role="button"]');
        for (const button of buttons) {
          try {
            const text = await this.page.evaluate(el => el.textContent?.trim(), button);
            if (text && (text.includes('Not Now') || text.includes('Không phải bây giờ') || text.includes('Turn Off') || text.includes('Tắt'))) {
              // Quick clickability check
              const isVisible = await this.page.evaluate(el => {
                const rect = el.getBoundingClientRect();
                return rect.width > 0 && rect.height > 0;
              }, button);

              if (isVisible) {
                this.logger.info(`Found "Not Now" button with text: "${text}"`);
                await button.click();
                notNowClicked = true;
                break;
              }
            }
          } catch (buttonError) {
            // Skip this button and continue
            continue;
          }
        }
      } catch (e) {
        this.logger.debug('Text-based button search failed:', e.message);
      }

      // Method 2: Try common CSS selectors if text method failed
      if (!notNowClicked) {
        const quickSelectors = [
          'div[role="dialog"] button:last-child', // Usually "Not Now" is the last button
          'div[role="presentation"] button:last-child',
          'button[type="button"]:last-child'
        ];

        for (const selector of quickSelectors) {
          try {
            const element = await this.page.$(selector);
            if (element) {
              const text = await this.page.evaluate(el => el.textContent?.trim(), element);
              if (text && (text.includes('Not Now') || text.includes('Không phải bây giờ') || text.includes('Turn Off') || text.includes('Tắt'))) {
                this.logger.info(`Found "Not Now" button with selector: ${selector}`);
                await element.click();
                notNowClicked = true;
                break;
              }
            }
          } catch (e) {
            continue;
          }
        }
      }

      if (notNowClicked) {
        await this.page.waitForTimeout(500); // Shorter wait after click
        this.logger.info('Notification popup dismissed');
        return true;
      } else {
        this.logger.debug('Could not find "Not Now" button in notification popup');
        return false;
      }
    } catch (error) {
      this.logger.debug('Error handling notification popup:', error);
      return false;
    }
  }

  async fastNavigateToMessages() {
    try {
      this.logger.info('Fast navigation to Instagram Messages...');

      // Direct navigation to messages URL - this is the fastest way
      this.logger.info('Navigating directly to /direct/inbox/...');
      await this.page.goto('https://www.instagram.com/direct/inbox/', {
        waitUntil: 'domcontentloaded',
        timeout: 10000
      });

      // Check URL immediately
      let currentUrl = this.page.url();
      this.logger.info(`Fast nav - Immediate URL: ${currentUrl}`);

      // If already on direct page, we're good
      if (currentUrl.includes('/direct/')) {
        this.logger.info('Fast navigation to direct messages successful immediately');
        return;
      }

      // If redirected to login immediately, fail fast
      if (currentUrl.includes('/accounts/login/')) {
        throw new Error('Redirected to login page - not logged in');
      }

      // Wait a short time for potential redirects
      await this.page.waitForTimeout(800);

      // Check URL again
      currentUrl = this.page.url();
      this.logger.info(`Fast nav - Final URL: ${currentUrl}`);

      // Check if we got redirected to login (means not logged in)
      if (currentUrl.includes('/accounts/login/')) {
        throw new Error('Redirected to login page - not logged in');
      }

      // Check if we're on the inbox page
      if (currentUrl.includes('/direct/')) {
        this.logger.info('Fast navigation to direct messages successful');
        return;
      }

      throw new Error(`Fast navigation failed. Current URL: ${currentUrl}`);

    } catch (error) {
      this.logger.error('Fast navigation failed:', error);
      throw error;
    }
  }

  async navigateToMessages() {
    try {
      this.logger.info('Navigating to Instagram Messages...');

      // Direct navigation to messages URL - simplest and most reliable
      this.logger.info('Navigating directly to /direct/inbox/...');
      await this.page.goto('https://www.instagram.com/direct/inbox/', {
        waitUntil: 'domcontentloaded',
        timeout: 15000
      });

      // Check URL immediately
      let currentUrl = this.page.url();
      this.logger.info(`Navigation - Immediate URL: ${currentUrl}`);

      // If already on direct page, we're good
      if (currentUrl.includes('/direct/')) {
        this.logger.info('Successfully navigated to direct messages page immediately');
        return;
      }

      // If redirected to login immediately, fail fast
      if (currentUrl.includes('/accounts/login/')) {
        throw new Error('Redirected to login page - not logged in');
      }

      // Wait a short time for potential redirects
      await this.page.waitForTimeout(1000);

      // Handle any notification popup that might appear (but don't wait long)
      try {
        await this.handleNotificationPopup();
      } catch (e) {
        // Don't fail navigation if popup handling fails
        this.logger.debug('Popup handling failed, continuing...');
      }

      // Check URL again
      currentUrl = this.page.url();
      this.logger.info(`Navigation result - Final URL: ${currentUrl}`);

      // Check if we got redirected to login (means not logged in)
      if (currentUrl.includes('/accounts/login/')) {
        throw new Error('Redirected to login page - not logged in');
      }

      // Check if we're on the inbox page
      if (currentUrl.includes('/direct/')) {
        this.logger.info('Successfully navigated to direct messages page');
        return;
      }

      throw new Error(`Navigation failed. Current URL: ${currentUrl}`);

    } catch (error) {
      this.logger.error('Failed to navigate to messages:', error);
      throw error;
    }
  }

  // This method is now deprecated - messages are added directly to database queue
  // Keeping for backward compatibility
  async addToQueue(comment, templateName = 'print_notification') {
    this.logger.warn('addToQueue method is deprecated - messages should be added directly to database queue');
    return null;
  }

  // Removed getMessageTemplate - using direct template_type approach instead

  processTemplate(template, variables) {
    let processedMessage = template;

    // Fix encoding issues first
    processedMessage = this.fixEncodingIssues(processedMessage);

    // Replace variables in template
    Object.keys(variables).forEach(key => {
      const placeholder = `{{${key}}}`;
      processedMessage = processedMessage.replace(new RegExp(placeholder, 'g'), variables[key] || '');
    });

    // Clean up any character duplication that might occur
    processedMessage = this.cleanDuplicatedCharacters(processedMessage);

    return processedMessage;
  }

  fixEncodingIssues(text) {
    if (!text || typeof text !== 'string') {
      return text;
    }

    // Common Vietnamese character encoding fixes
    const encodingFixes = {
      'Chß╗ï': 'Chị',
      'vß╗½a': 'vừa',
      'chß╗æt': 'chốt',
      'm├ú': 'mã',
      'Gi├í': 'Giá',
      'l├▓ng': 'lòng',
      'kh├┤ng': 'không',
      'xß║ú': 'xóa',
      '─æ╞ín': 'đơn',
      'Cß║úm': 'Cảm',
      'ß╗ín': 'ơn',
      'b├ín': 'bạn',
      '─æ├ú': 'đã',
      'quan': 'quan',
      't├óm': 'tâm',
      'Chß╗úc': 'Chúc',
      'mß╗½ng': 'mừng',
      'chuß║ún': 'chuẩn',
      'bß╗ï': 'bị',
      'hß║úng': 'hàng',
      'liß╗ên': 'liên',
      'hß╗ç': 'hệ',
      'sß╗¢m': 'sớm',
      'nhß║ąt': 'nhất',
      'Xin': 'Xin',
      'Shop': 'Shop',
      'sß║╜': 'sẽ',
      'tß╗ï': 'tư',
      'vß║ąn': 'vấn',
      'chi': 'chi',
      'tiß║┐t': 'tiết'
    };

    let fixedText = text;

    // Apply encoding fixes
    Object.keys(encodingFixes).forEach(encoded => {
      const decoded = encodingFixes[encoded];
      fixedText = fixedText.replace(new RegExp(encoded, 'g'), decoded);
    });

    // Log if any fixes were applied
    if (fixedText !== text) {
      this.logger.info(`Fixed encoding: "${text}" -> "${fixedText}"`);
    }

    return fixedText;
  }

  cleanDuplicatedCharacters(text) {
    // Function to remove character duplication like "CChhịị vvừừaa cchhốốtt" -> "Chị vừa chốt"
    // This handles cases where each character gets duplicated during input

    if (!text || typeof text !== 'string') {
      return text;
    }

    // Split into lines to preserve line breaks
    const lines = text.split('\n');

    const cleanedLines = lines.map(line => {
      if (line.trim().length === 0) {
        return line; // Keep empty lines as is
      }

      // Check if line has character duplication pattern
      let cleaned = line;

      // Method 1: Check for consecutive duplicate characters
      let potentialCleaned = '';
      for (let i = 0; i < line.length; i += 2) {
        potentialCleaned += line[i];
      }

      // Method 2: More sophisticated pattern detection
      // Look for patterns where every character is duplicated
      let smartCleaned = '';
      let i = 0;
      while (i < line.length) {
        const currentChar = line[i];
        const nextChar = line[i + 1];

        if (currentChar === nextChar) {
          // Characters are the same, likely duplicated
          smartCleaned += currentChar;
          i += 2; // Skip both characters
        } else {
          // Characters are different, keep current and move to next
          smartCleaned += currentChar;
          i += 1;
        }
      }

      // Decide which cleaning method to use
      const repeatedPairs = (line.match(/(.)\1/g) || []).length;
      const totalChars = line.length;

      // If more than 40% of characters are in repeated pairs, likely duplicated
      if (repeatedPairs > totalChars * 0.4 && totalChars > 4) {
        // Use smart cleaning method
        this.logger.info(`Detected character duplication in: "${line}" -> "${smartCleaned}"`);
        cleaned = smartCleaned;
      }

      return cleaned;
    });

    return cleanedLines.join('\n');
  }

  startQueueProcessor() {
    if (this.isProcessingQueue) {
      this.logger.warn('⚠️ Queue processor already running, skipping start to prevent duplicates');
      return;
    }

    this.logger.info('🚀 Starting queue processor...');
    this.isProcessingQueue = true;

    // Log queue status on startup
    this.logQueueStatus();

    // Start the queue processing loop
    this.processQueue();

    this.logger.info('✅ Queue processor started successfully');
  }

  async logQueueStatus() {
    try {
      const stats = await this.database.getMessageQueueStats();
      this.logger.info(`Message queue status on startup:`, stats);

      if (stats.pending > 0) {
        this.logger.info(`📋 Found ${stats.pending} pending messages in queue - will process when messenger starts`);
      }
    } catch (error) {
      this.logger.error('Failed to get queue status:', error);
    }
  }

  async processQueue() {
    const processId = Math.random().toString(36).substr(2, 9);
    this.logger.info(`🔄 Starting processQueue loop [${processId}] - isRunning: ${this.isRunning}, isProcessingQueue: ${this.isProcessingQueue}`);

    while (this.isRunning && this.isProcessingQueue) {
      try {
        // Get pending messages from database (FIFO order)
        const pendingMessages = await this.database.getMessageQueue('pending', 1);

        if (pendingMessages.length === 0) {
          // Log every 30 seconds when waiting for messages
          if (Date.now() % 30000 < 2000) {
            this.logger.info(`📭 No pending messages, waiting... [${processId}]`);
          }
          await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
          continue;
        }

        const message = pendingMessages[0];
        this.logger.info(`📤 Processing next message in FIFO queue: @${message.username} (${message.customer_type})`);

        // Mark message as processing
        await this.database.updateMessageStatus(message.id, 'processing');

        // Convert database message format to internal format
        const messageData = {
          id: message.id,
          username: message.username,
          originalComment: message.original_comment,
          customerType: message.customer_type,
          templateName: message.template_name,
          template_type: message.template_type || 'normal',
          status: 'processing',
          retries: message.retries,
          maxRetries: message.max_retries
        };

        await this.sendMessage(messageData);

        // Increment processed message counter
        this.messagesProcessed++;
        this.logger.info(`Messages processed: ${this.messagesProcessed}/${this.maxMessagesBeforeRestart}`);

        // Check if we need to restart browser due to message count
        if (this.messagesProcessed >= this.maxMessagesBeforeRestart) {
          this.logger.info(`🔄 Reached message limit (${this.maxMessagesBeforeRestart}), checking if safe to restart...`);

          // PROTECTION: Check if currently sending a message
          if (this.isCurrentlySendingMessage) {
            this.logger.warn('⚠️ Cannot restart now - currently sending message to @' + (this.currentMessageData?.username || 'unknown'));
            this.logger.warn('⏳ Marking restart as pending - will execute after current message completes');
            this.pendingRestart = true;
            // Continue processing, restart will happen after current message
            continue;
          }

          this.logger.info('✅ Safe to restart - no message currently being sent');

          // CRITICAL: Stop queue processing first to prevent conflicts
          this.isProcessingQueue = false;
          this.logger.info('⏸️ Queue processing stopped before restart');

          // Wait longer to ensure current message processing is complete and loop exits
          await new Promise(resolve => setTimeout(resolve, 3000));

          // Now restart browser
          await this.restartBrowser();

          // Exit the queue loop since we stopped processing
          break;
        }

        // Wait between messages to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000)); // Reduced from 3000ms

      } catch (error) {
        this.logger.error(`Error processing message queue [${processId}]:`, error);
        await new Promise(resolve => setTimeout(resolve, 5000)); // Wait longer on error
      }
    }

    this.logger.info(`🛑 processQueue loop ended [${processId}] - isRunning: ${this.isRunning}, isProcessingQueue: ${this.isProcessingQueue}`);
  }

  async sendMessage(messageData) {
    try {
      this.logger.info(`🚀 Starting message send process to @${messageData.username}`);

      // PROTECTION: Mark as currently sending message
      this.isCurrentlySendingMessage = true;
      this.currentMessageData = messageData;
      this.logger.info('🛡️ Message sending protection activated');

      // Emit messaging start event
      this.emit('messaging-start', { username: messageData.username });

      // Update status to sending
      messageData.status = 'sending';
      this.emit('messageStatusUpdate', messageData);

      // Check browser health before starting
      const browserHealthy = await this.checkBrowserHealth();
      if (!browserHealthy) {
        throw new Error('Browser is not healthy, cannot send message');
      }

      // OPTIMIZED METHOD: Try cached thread ID first, fallback to profile method
      // Step 1: Check if we have a cached thread ID for this user
      this.logger.info(`🔍 Step 1: Checking for cached thread ID for @${messageData.username}`);
      const cachedThreadId = await this.database.getInstagramThread(messageData.username);

      let usedCachedThread = false;

      if (cachedThreadId) {
        this.logger.info(`🎯 Found cached thread ID for @${messageData.username}: ${cachedThreadId}`);

        // Try to navigate directly to the cached thread using correct URL pattern
        const directUrl = `https://www.instagram.com/direct/t/${cachedThreadId}`;
        this.logger.info(`🚀 Navigating directly to cached thread: ${directUrl}`);

        try {
          await this.page.goto(directUrl, { waitUntil: 'networkidle2', timeout: 15000 });

          // Verify we're in the correct chat by checking for message input
          await this.page.waitForSelector('div[contenteditable="true"][data-testid="message-input"], div[contenteditable="true"]', { timeout: 10000 });

          this.logger.info(`✅ Successfully navigated to cached thread for @${messageData.username}`);
          usedCachedThread = true;

        } catch (cachedError) {
          this.logger.warn(`❌ Failed to use cached thread ID for @${messageData.username}: ${cachedError.message}`);
          this.logger.info(`🔄 Falling back to profile navigation method`);

          // Check if error is due to browser crash
          if (await this.isBrowserCrashed()) {
            throw new Error('Browser crashed during cached thread navigation');
          }

          // Delete the invalid cached thread ID
          await this.database.deleteInstagramThread(messageData.username);
          usedCachedThread = false;
        }
      }

      // If no cached thread ID or cached thread failed, use profile method
      if (!usedCachedThread) {
        this.logger.info(`👤 Step 2: Using profile method for @${messageData.username}`);
        await this.navigateToUserProfile(messageData.username);

        // Check browser health after navigation
        if (await this.isBrowserCrashed()) {
          throw new Error('Browser crashed during profile navigation');
        }

        // Step 3: Try to find direct "Message" button first, fallback to three dots menu
        this.logger.info('💬 Step 3: Looking for Message button or three dots menu');
        const messageButtonFound = await this.tryClickDirectMessageButton();

        if (!messageButtonFound) {
          // Fallback to three dots menu method
          this.logger.info('⚙️ Step 3b: Direct message button not found, trying three dots menu');
          await this.clickThreeDotsMenu();

          this.logger.info('💬 Step 4: Clicking Send message option from menu');
          await this.clickSendMessageOption();
        }

        // Check browser health after UI interactions
        if (await this.isBrowserCrashed()) {
          throw new Error('Browser crashed during UI interactions');
        }

        // Step 4: Wait for Instagram to redirect to chat
        this.logger.info('⏳ Step 4: Waiting for redirect to chat');
        await this.waitForChatRedirect();

        // Step 5: Extract and cache thread ID from URL (only for new conversations)
        await this.extractAndCacheThreadId(messageData.username);
      }

      // Step 6: Check and handle message request if needed (for new users)
      this.logger.info('🔍 Checking for message request dialog...');
      await this.handleMessageRequestIfNeeded(messageData.username);

      // Check message loading health before sending
      this.logger.info('🔍 Checking message loading health...');
      const messageLoadingHealthy = await this.checkMessageLoadingHealth();

      if (!messageLoadingHealthy) {
        // Check if we should restart due to message loading failures
        const shouldRestart = await this.checkAndHandleMessageLoadingFailures();
        if (shouldRestart) {
          throw new Error('Auto-messaging restarted due to message loading failures');
        } else {
          throw new Error('Message loading health check failed');
        }
      }

      // Final Step: Send all active templates for this customer type
      this.logger.info(`📤 Final Step: Sending template messages to @${messageData.username}`);
      await this.sendTemplateMessages(messageData);

      // Final browser health check
      if (await this.isBrowserCrashed()) {
        throw new Error('Browser crashed during message sending');
      }

      // Update status to sent and remove from database queue
      messageData.status = 'sent';
      messageData.sentAt = new Date().toISOString();

      // Remove from database queue on success
      await this.database.removeFromMessageQueue(messageData.id);

      this.emit('messageStatusUpdate', messageData);
      this.emit('message-sent', { username: messageData.username, timestamp: messageData.sentAt });
      this.emit('messaging-complete', { username: messageData.username, success: true });

      this.logger.info(`✅ All messages sent successfully to @${messageData.username}`);

      // PROTECTION: Clear sending protection
      this.isCurrentlySendingMessage = false;
      this.currentMessageData = null;
      this.logger.info('🛡️ Message sending protection deactivated - SUCCESS');

      // Check if browser crash recovery was pending
      if (this.pendingBrowserCrashRecovery) {
        this.logger.info('🔄 Executing pending browser crash recovery after message completion...');
        this.pendingBrowserCrashRecovery = false;
        setTimeout(async () => {
          try {
            await this.handleBrowserCrash();
            this.logger.info('✅ Pending browser crash recovery completed');
          } catch (error) {
            this.logger.error('Pending browser crash recovery failed:', error);
          }
        }, 1000);
      }

      // Check if restart was pending
      if (this.pendingRestart) {
        this.logger.info('🔄 Executing pending restart after message completion...');
        this.pendingRestart = false;
        // Delay restart slightly to ensure cleanup
        setTimeout(() => {
          this.restartBrowser().catch(error => {
            this.logger.error('Pending restart failed:', error);
          });
        }, 2000);
      }

    } catch (error) {
      this.logger.error(`❌ Failed to send message to @${messageData.username}:`, error);

      // Check if error is USER_NOT_FOUND (page isn't available)
      if (error.message.includes('USER_NOT_FOUND')) {
        this.logger.warn(`🚫 User @${messageData.username} not found - skipping message and moving to next`);

        // Save failed message for manual review BEFORE removing from queue
        await this.saveFailedMessage({
          username: messageData.username,
          originalComment: messageData.original_comment,
          customerType: messageData.customer_type,
          error: 'User not found - page isn\'t available',
          failedAt: new Date().toISOString(),
          retries: messageData.retries,
          maxRetries: messageData.maxRetries,
          timestamp: messageData.created_at
        });

        // Remove from message queue after saving to failed_messages
        await this.database.removeFromMessageQueue(messageData.id);
        this.logger.info(`📤 Skipped message (USER_NOT_FOUND) moved to failed_messages table and removed from queue: @${messageData.username}`);

        messageData.status = 'skipped';
        messageData.error = 'User not found - page isn\'t available';
        this.emit('messageStatusUpdate', messageData);
        this.emit('messaging-complete', { username: messageData.username, success: false, error: 'User not found', skipped: true });

        // Clear protection and return - don't retry, just move to next message
        this.isCurrentlySendingMessage = false;
        this.currentMessageData = null;
        this.logger.info('🛡️ Message sending protection deactivated - USER NOT FOUND');
        return;
      }

      // Check if error is due to browser crash
      const isCrashed = await this.isBrowserCrashed();
      if (isCrashed) {
        this.logger.error('🚨 Browser crash detected during message sending!');

        // Force restart browser immediately
        try {
          await this.handleBrowserCrash();

          // Retry the message after browser restart
          if (messageData.retries < messageData.maxRetries) {
            this.logger.info(`🔄 Retrying message after browser crash recovery for @${messageData.username}`);
            messageData.retries++;
            messageData.status = 'pending';
            await this.database.updateMessageStatus(messageData.id, 'pending', 'Browser crash - retrying after recovery');
            this.emit('messageStatusUpdate', messageData);

            // Clear protection and return - message will be retried in next queue cycle
            this.isCurrentlySendingMessage = false;
            this.currentMessageData = null;
            this.logger.info('🛡️ Message sending protection deactivated - BROWSER CRASH RECOVERY');
            return;
          }
        } catch (crashError) {
          this.logger.error('Failed to recover from browser crash:', crashError);
        }
      }

      // Increment retries in database
      await this.database.incrementMessageRetries(messageData.id);

      messageData.retries++;
      messageData.status = 'failed';
      messageData.error = error.message;

      // Retry if under max retries
      if (messageData.retries < messageData.maxRetries) {
        this.logger.info(`🔄 Retrying message to @${messageData.username} (${messageData.retries}/${messageData.maxRetries})`);

        // Update status back to pending in database for retry
        await this.database.updateMessageStatus(messageData.id, 'pending', error.message);

        messageData.status = 'pending';
        this.emit('messageStatusUpdate', messageData);
      } else {
        this.logger.error(`🚫 Max retries reached for message to @${messageData.username}`);

        // Save failed message for manual review BEFORE removing from queue
        await this.saveFailedMessage({
          username: messageData.username,
          originalComment: messageData.original_comment,
          customerType: messageData.customer_type,
          error: error.message,
          failedAt: new Date().toISOString(),
          retries: messageData.retries,
          maxRetries: messageData.maxRetries,
          timestamp: messageData.created_at
        });

        // Remove from message queue after saving to failed_messages
        await this.database.removeFromMessageQueue(messageData.id);
        this.logger.info(`📤 Failed message moved to failed_messages table and removed from queue: @${messageData.username}`);

        this.emit('messageStatusUpdate', messageData);
        this.emit('messaging-complete', { username: messageData.username, success: false, error: error.message });
      }

      // PROTECTION: Clear sending protection even on error
      this.isCurrentlySendingMessage = false;
      this.currentMessageData = null;
      this.logger.info('🛡️ Message sending protection deactivated - ERROR');

      // Check if browser crash recovery was pending
      if (this.pendingBrowserCrashRecovery) {
        this.logger.info('🔄 Executing pending browser crash recovery after message error...');
        this.pendingBrowserCrashRecovery = false;
        setTimeout(async () => {
          try {
            await this.handleBrowserCrash();
            this.logger.info('✅ Pending browser crash recovery completed');
          } catch (error) {
            this.logger.error('Pending browser crash recovery failed:', error);
          }
        }, 1000);
      }

      // Check if restart was pending
      if (this.pendingRestart) {
        this.logger.info('🔄 Executing pending restart after message error...');
        this.pendingRestart = false;
        setTimeout(() => {
          this.restartBrowser().catch(restartError => {
            this.logger.error('Pending restart failed:', restartError);
          });
        }, 2000);
      }
    }
  }

  // NEW METHODS FOR OPTIMIZED MESSAGING WITH THREAD ID CACHING

  async navigateToDirectThread(threadId, username) {
    try {
      const directUrl = `https://www.instagram.com/direct/t/${threadId}`;
      this.logger.info(`🔗 Navigating directly to thread: ${directUrl}`);

      await this.page.goto(directUrl, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Wait for page to load
      await this.page.waitForTimeout(3000);

      // Check if we successfully accessed the direct chat
      const isInChat = await this.isInDirectChat();
      if (isInChat) {
        this.logger.info(`✅ Successfully accessed direct chat for @${username} via thread ID`);
        return true;
      } else {
        this.logger.warn(`❌ Failed to access direct chat via thread ID for @${username}`);
        return false;
      }
    } catch (error) {
      this.logger.error(`Failed to navigate to direct thread for @${username}:`, error);
      return false;
    }
  }

  async isInDirectChat() {
    try {
      const currentUrl = this.page.url();
      // Check for both URL patterns: /direct/inbox/ and /direct/t/
      const isDirectPage = currentUrl.includes('/direct/inbox/') || currentUrl.includes('/direct/t/');

      if (!isDirectPage) {
        this.logger.info(`❌ Not in direct chat - URL: ${currentUrl}`);
        return false;
      }

      // Check for message input to confirm we're in a chat
      const messageInput = await this.page.$('div[contenteditable="true"][data-testid="message-input"]') ||
                          await this.page.$('div[contenteditable="true"]') ||
                          await this.page.$('textarea[placeholder*="Message"]');

      if (messageInput) {
        this.logger.info(`✅ Confirmed in direct chat - message input found`);
        return true;
      } else {
        this.logger.info(`❌ In direct page but no message input found`);
        return false;
      }
    } catch (error) {
      this.logger.error('Error checking if in direct chat:', error);
      return false;
    }
  }

  async extractAndCacheThreadId(username) {
    try {
      const currentUrl = this.page.url();
      this.logger.info(`🔍 Extracting thread ID from URL: ${currentUrl}`);

      // Extract thread ID from URL patterns:
      // Pattern 1: /direct/inbox/{thread_id}/
      // Pattern 2: /direct/t/{thread_id}/
      let threadIdMatch = currentUrl.match(/\/direct\/inbox\/([^\/]+)\/?/);

      if (!threadIdMatch) {
        // Try alternative pattern: /direct/t/{thread_id}/
        threadIdMatch = currentUrl.match(/\/direct\/t\/([^\/]+)\/?/);
      }

      if (threadIdMatch && threadIdMatch[1]) {
        const threadId = threadIdMatch[1];
        this.logger.info(`🎯 Extracted thread ID for @${username}: ${threadId}`);

        // Save to database for future use
        await this.database.saveInstagramThread(username, threadId);
        this.logger.info(`💾 Saved thread ID to database: ${username} → ${threadId}`);

        return threadId;
      } else {
        this.logger.warn(`❌ Could not extract thread ID from URL: ${currentUrl}`);
        this.logger.warn(`❌ Tried patterns: /direct/inbox/{id}/ and /direct/t/{id}/`);
        return null;
      }
    } catch (error) {
      this.logger.error(`Failed to extract thread ID for @${username}:`, error);
      return null;
    }
  }

  // EXISTING METHODS FOR PROFILE-BASED MESSAGING (FALLBACK)

  async navigateToUserProfile(username) {
    try {
      // Clean username (remove @ if present)
      const cleanUsername = username.replace('@', '');
      const profileUrl = `https://www.instagram.com/${cleanUsername}/`;

      this.logger.info(`Navigating to profile: ${profileUrl}`);

      await this.page.goto(profileUrl, {
        waitUntil: 'networkidle0',
        timeout: 30000
      });

      // Wait for profile to load
      await this.page.waitForTimeout(2000);

      // Check if profile loaded successfully
      let profileInfo;
      try {
        profileInfo = await this.page.evaluate(() => {
          try {
            // Check for profile indicators
            const hasProfilePic = document.querySelector('img[alt*="profile picture"]') ||
              document.querySelector('[data-testid="user-avatar"]') ||
              document.querySelector('img[alt*="profile"]');

            const hasUsername = document.querySelector('h1') ||
              document.querySelector('h2') ||
              document.querySelector('[data-testid="user-name"]');

            // Check for "page not available" messages
            const bodyText = document.body.textContent || '';
            const notFound = bodyText.includes("Sorry, this page isn't available") ||
              bodyText.includes("User not found") ||
              bodyText.includes("This account doesn't exist");

            // Check for private account indicators
            const isPrivate = bodyText.includes("This Account is Private") ||
              bodyText.includes("Follow to see their photos and videos");

            const loaded = !notFound && (hasProfilePic || hasUsername || isPrivate);

            return {
              loaded: loaded,
              hasProfilePic: !!hasProfilePic,
              hasUsername: !!hasUsername,
              notFound: notFound,
              isPrivate: isPrivate,
              currentUrl: window.location.href,
              pageTitle: document.title,
              bodyTextSample: bodyText.substring(0, 200)
            };
          } catch (error) {
            return {
              loaded: false,
              error: error.message,
              currentUrl: window.location.href,
              pageTitle: document.title
            };
          }
        });
      } catch (error) {
        this.logger.error(`Error evaluating profile for @${cleanUsername}:`, error);
        profileInfo = { loaded: false, error: error.message };
      }

      this.logger.info(`Profile load check for @${cleanUsername}:`, profileInfo);

      // Check if page is not available (user doesn't exist)
      if (profileInfo && profileInfo.notFound) {
        this.logger.warn(`🚫 User @${cleanUsername} not found - page isn't available. Skipping message.`);
        throw new Error(`USER_NOT_FOUND: @${cleanUsername} - page isn't available`);
      }

      if (!profileInfo || !profileInfo.loaded) {
        // Try a simple fallback check - just verify we're on the right URL
        const currentUrl = this.page.url();
        if (currentUrl.includes(cleanUsername)) {
          this.logger.warn(`Profile check failed but URL contains username, proceeding anyway for @${cleanUsername}`);
        } else {
          throw new Error(`Failed to load profile for @${cleanUsername} - profile may not exist, be private, or page not available. Details: ${JSON.stringify(profileInfo)}`);
        }
      } else {
        this.logger.info(`✅ Successfully loaded profile for @${cleanUsername}`);
      }

    } catch (error) {
      this.logger.error(`Failed to navigate to user profile @${username}:`, error);
      throw error;
    }
  }

  async tryClickDirectMessageButton() {
    try {
      this.logger.info('🔍 Looking for direct Message button in header area...');

      // Wait a bit for profile to fully load
      await this.page.waitForTimeout(1000);

      // Strategy 1: Look for Message button in header area (same row as Follow button)
      const headerMessageButton = await this.page.evaluate(() => {
        // First, find the Follow button to locate the header area
        const followButtons = document.querySelectorAll('button, [role="button"]');
        let headerArea = null;

        for (const btn of followButtons) {
          const text = btn.textContent?.trim().toLowerCase();
          if (text === 'follow' || text === 'theo dõi' || text === 'following' || text === 'đang theo dõi') {
            // Found Follow button, get its parent container (header area)
            headerArea = btn.closest('div, section, header') || btn.parentElement;
            break;
          }
        }

        if (!headerArea) {
          // Fallback: look for username area
          const usernameElements = document.querySelectorAll('h1, h2, [data-testid*="username"]');
          for (const el of usernameElements) {
            if (el.textContent && el.textContent.trim().length > 0) {
              headerArea = el.closest('div, section, header') || el.parentElement;
              break;
            }
          }
        }

        if (headerArea) {
          // Look for Message button within the header area
          const buttons = headerArea.querySelectorAll('button, [role="button"], div[role="button"], a');

          for (let i = 0; i < buttons.length; i++) {
            const btn = buttons[i];
            const text = btn.textContent?.trim();

            if (text) {
              const lowerText = text.toLowerCase();
              // Check for message button text (exact matches)
              if (lowerText === 'nhắn tin' || lowerText === 'message' ||
                lowerText === 'tin nhắn' || lowerText === 'send message') {

                // Make sure it's visible and not the Follow button
                if (btn.offsetParent !== null &&
                  !lowerText.includes('follow') &&
                  !lowerText.includes('theo dõi')) {

                  btn.click();
                  return {
                    found: true,
                    text: text,
                    method: 'header area message button',
                    tagName: btn.tagName,
                    className: btn.className.substring(0, 100)
                  };
                }
              }
            }
          }
        }

        return { found: false };
      });

      if (headerMessageButton.found) {
        await this.page.waitForTimeout(2000);
        this.logger.info(`✅ Successfully clicked direct Message button in header: "${headerMessageButton.text}"`);
        return true;
      }

      // Strategy 2: Look for Message button anywhere on page (fallback)
      const exactTextButtons = await this.page.evaluate(() => {
        const buttons = document.querySelectorAll('button, [role="button"], div[role="button"], a');
        const messageButtons = [];

        buttons.forEach((btn, index) => {
          const text = btn.textContent?.trim();
          if (text) {
            // Check for exact matches (case insensitive)
            const lowerText = text.toLowerCase();
            if (lowerText === 'nhắn tin' || lowerText === 'message' ||
              lowerText === 'tin nhắn' || lowerText === 'send message') {
              messageButtons.push({
                index: index,
                text: text,
                tagName: btn.tagName,
                className: btn.className.substring(0, 100),
                isVisible: btn.offsetParent !== null
              });
            }
          }
        });

        return messageButtons;
      });

      if (exactTextButtons.length > 0) {
        this.logger.info(`Found ${exactTextButtons.length} potential message buttons outside header:`, exactTextButtons);

        // Try to click the first visible one
        const visibleButton = exactTextButtons.find(btn => btn.isVisible);
        if (visibleButton) {
          const success = await this.page.evaluate((buttonIndex) => {
            const buttons = document.querySelectorAll('button, [role="button"], div[role="button"], a');
            const targetButton = buttons[buttonIndex];
            if (targetButton) {
              targetButton.click();
              return true;
            }
            return false;
          }, visibleButton.index);

          if (success) {
            await this.page.waitForTimeout(2000);
            this.logger.info(`✅ Successfully clicked direct Message button (fallback): "${visibleButton.text}"`);
            return true;
          }
        }
      }

      this.logger.info('ℹ️ Direct Message button not found, will try three dots menu');
      return false;

    } catch (error) {
      this.logger.warn('Error while looking for direct message button:', error.message);
      return false;
    }
  }

  async clickThreeDotsMenu() {
    try {
      this.logger.info('🔍 Looking for three dots menu...');

      // Strategy 1: Look for three dots menu using exact HTML structure from your example
      const exactStructureFound = await this.page.evaluate(() => {
        // Look for the exact structure: div.x1q0g3np.x2lah0s containing div[role="button"] with SVG
        const containers = document.querySelectorAll('div.x1q0g3np.x2lah0s');

        for (const container of containers) {
          const buttonDiv = container.querySelector('div[role="button"]');
          if (buttonDiv) {
            const svg = buttonDiv.querySelector('svg[aria-label="Options"]');
            if (svg) {
              const circles = svg.querySelectorAll('circle');
              const title = svg.querySelector('title')?.textContent;

              // Verify this is the three dots menu (3 circles + Options)
              if (circles.length === 3 && title?.toLowerCase().includes('option')) {
                buttonDiv.click();
                return {
                  found: true,
                  method: 'exact structure (x1q0g3np x2lah0s)',
                  containerClass: container.className.substring(0, 50),
                  buttonClass: buttonDiv.className.substring(0, 100),
                  svgTitle: title,
                  svgAriaLabel: svg.getAttribute('aria-label'),
                  circleCount: circles.length
                };
              }
            }
          }
        }

        // Fallback: Look for any div[role="button"] with Options SVG
        const allButtonDivs = document.querySelectorAll('div[role="button"]');
        for (const buttonDiv of allButtonDivs) {
          const svg = buttonDiv.querySelector('svg[aria-label="Options"]');
          if (svg) {
            const circles = svg.querySelectorAll('circle');
            const title = svg.querySelector('title')?.textContent;

            if (circles.length === 3 && title?.toLowerCase().includes('option')) {
              buttonDiv.click();
              return {
                found: true,
                method: 'fallback div[role="button"]',
                buttonClass: buttonDiv.className.substring(0, 100),
                svgTitle: title,
                svgAriaLabel: svg.getAttribute('aria-label'),
                circleCount: circles.length
              };
            }
          }
        }

        return { found: false };
      });

      if (exactStructureFound.found) {
        await this.page.waitForTimeout(1000);
        this.logger.info(`✅ Successfully clicked three dots menu using ${exactStructureFound.method}`);
        this.logger.info(`Menu details:`, exactStructureFound);

        // Debug: Check if menu actually opened
        const menuOpened = await this.page.evaluate(() => {
          // Look for menu items that might have appeared
          const menuItems = document.querySelectorAll('[role="menuitem"], [role="button"]');
          const visibleItems = [];
          menuItems.forEach((item, index) => {
            if (item.offsetParent !== null && item.textContent?.trim()) {
              visibleItems.push({
                index: index,
                text: item.textContent.trim(),
                role: item.getAttribute('role')
              });
            }
          });
          return visibleItems.slice(0, 10); // First 10 items
        });

        this.logger.info(`Menu items after clicking three dots:`, menuOpened);
        return;
      }

      // Strategy 2: Use CSS selectors based on exact structure
      const menuSelectors = [
        // Based on exact HTML structure
        'div.x1q0g3np.x2lah0s div[role="button"]',
        'div[role="button"]:has(svg[aria-label="Options"])',
        'svg[aria-label="Options"]',
        // More specific patterns
        'div.x1q0g3np div[role="button"] svg[aria-label="Options"]',
        'div.x2lah0s div[role="button"] svg[aria-label="Options"]',
        // Generic fallbacks
        'div[role="button"]:has(svg[title="Options"])',
        '[aria-label="Options"]',
        '[title="Options"]'
      ];

      let menuButton = null;

      for (const selector of menuSelectors) {
        try {
          menuButton = await this.page.waitForSelector(selector, { timeout: 2000 });
          if (menuButton) {
            // Verify this is actually a menu button and prioritize header area elements
            const isMenuButton = await this.page.evaluate((el) => {
              let targetElement = el;
              let svg = null;

              // Handle different element types
              if (el.tagName === 'SVG') {
                svg = el;
                targetElement = el.parentElement;
              } else if (el.getAttribute('role') === 'button') {
                // This is the div[role="button"] - look for SVG inside
                svg = el.querySelector('svg[aria-label="Options"]') ||
                  el.querySelector('svg[title="Options"]') ||
                  el.querySelector('svg');
                targetElement = el;
              } else {
                // Generic element - look for SVG inside
                svg = el.querySelector('svg') || (el.tagName === 'SVG' ? el : null);
              }

              if (!svg) return false;

              // Check for three dots pattern (3 circles) AND Options label
              const circles = svg.querySelectorAll('circle');
              const ariaLabel = svg.getAttribute('aria-label');
              const title = svg.querySelector('title')?.textContent;

              // Must have exactly 3 circles AND Options label for Instagram three dots menu
              const hasThreeDots = circles.length === 3;
              const hasOptionsLabel = ariaLabel && ariaLabel.toLowerCase().includes('option');
              const hasOptionsTitle = title && title.toLowerCase().includes('option');

              if (!hasThreeDots || (!hasOptionsLabel && !hasOptionsTitle)) {
                return false;
              }

              // Check if this is in the correct container (x1q0g3np x2lah0s)
              const isInCorrectContainer = (() => {
                const container = targetElement.closest('div.x1q0g3np.x2lah0s');
                return !!container;
              })();

              return { isValid: true, isInCorrectContainer: isInCorrectContainer };
            }, menuButton);

            if (isMenuButton && isMenuButton.isValid) {
              this.logger.info(`Found menu button using selector: ${selector} (correct container: ${isMenuButton.isInCorrectContainer})`);
              // Prioritize buttons in correct container, but accept any valid three dots menu
              if (isMenuButton.isInCorrectContainer) {
                break; // Perfect match - in correct container
              } else if (!menuButton || !menuButton.isInCorrectContainer) {
                // Keep this as backup if no correct container button found
                menuButton.isInCorrectContainer = false;
              }
            }
          }
        } catch (e) {
          continue;
        }
      }

      if (!menuButton) {
        // Debug: Log all potential menu elements with header area info
        const debugInfo = await this.page.evaluate(() => {
          const potentialMenus = [];

          // First, identify the header area by finding Follow button
          let headerArea = null;
          const followButtons = document.querySelectorAll('button, [role="button"]');
          for (const btn of followButtons) {
            const text = btn.textContent?.trim().toLowerCase();
            if (text === 'follow' || text === 'theo dõi' || text === 'following' || text === 'đang theo dõi') {
              headerArea = btn.closest('div, section, header') || btn.parentElement;
              break;
            }
          }

          // Look for all SVGs with Options or 3 circles
          const allSvgs = document.querySelectorAll('svg');
          allSvgs.forEach((svg, index) => {
            const ariaLabel = svg.getAttribute('aria-label');
            const circles = svg.querySelectorAll('circle');
            const title = svg.querySelector('title')?.textContent;

            // Only log SVGs that might be three dots menu
            if ((ariaLabel?.toLowerCase().includes('option') || title?.toLowerCase().includes('option')) ||
              circles.length >= 3) {

              const parentButton = svg.closest('div[role="button"], button');
              const isInHeaderArea = headerArea && headerArea.contains(svg);

              potentialMenus.push({
                type: 'svg',
                index: index,
                ariaLabel: ariaLabel,
                title: title,
                circleCount: circles.length,
                isInHeaderArea: isInHeaderArea,
                parentButtonTag: parentButton?.tagName,
                parentButtonRole: parentButton?.getAttribute('role'),
                parentButtonClass: parentButton?.className?.substring(0, 100),
                isVisible: svg.offsetParent !== null
              });
            }
          });

          return {
            headerAreaFound: !!headerArea,
            headerAreaInfo: headerArea ? {
              tagName: headerArea.tagName,
              className: headerArea.className.substring(0, 100)
            } : null,
            potentialMenus: potentialMenus
          };
        });

        this.logger.error('Could not find three dots menu button. Debug info:', debugInfo);
        throw new Error('Could not find three dots menu button');
      }

      // Click the found element (could be div or svg)
      await menuButton.click();
      await this.page.waitForTimeout(1000);

      this.logger.info('✅ Successfully clicked three dots menu');

    } catch (error) {
      this.logger.error('Failed to click three dots menu:', error);
      throw error;
    }
  }

  async clickSendMessageOption() {
    try {
      this.logger.info('🔍 Looking for Send message option in menu...');

      // Wait for menu to appear
      await this.page.waitForTimeout(1500);

      // Strategy 1: Look for exact text matches in menu items
      const exactTextFound = await this.page.evaluate(() => {
        const menuItems = document.querySelectorAll('div, button, [role="button"], [role="menuitem"], a');

        for (let i = 0; i < menuItems.length; i++) {
          const item = menuItems[i];
          const text = item.textContent?.trim();

          if (text) {
            const lowerText = text.toLowerCase();
            // Check for exact matches
            if (lowerText === 'send message' || lowerText === 'gửi tin nhắn' ||
              lowerText === 'tin nhắn' || lowerText === 'message') {

              // Make sure it's visible and clickable
              if (item.offsetParent !== null) {
                item.click();
                return {
                  found: true,
                  text: text,
                  tagName: item.tagName,
                  className: item.className.substring(0, 100)
                };
              }
            }
          }
        }

        return { found: false };
      });

      if (exactTextFound.found) {
        await this.page.waitForTimeout(2000);
        this.logger.info(`✅ Successfully clicked Send message option: "${exactTextFound.text}"`);
        return;
      } else {
        this.logger.warn('Send message option not found with exact text matching');
      }

      // Strategy 2: Use Puppeteer selectors as fallback
      const sendMessageSelectors = [
        // Exact text matches
        'text=Send message',
        'text=Gửi tin nhắn',
        'text=Message',
        // Button/element selectors
        'button:has-text("Send message")',
        '[role="button"]:has-text("Send message")',
        'div:has-text("Send message")',
        'a:has-text("Send message")',
        // Vietnamese
        'button:has-text("Gửi tin nhắn")',
        '[role="button"]:has-text("Gửi tin nhắn")',
        'div:has-text("Gửi tin nhắn")',
        // Partial matches
        '[role="menuitem"]:has-text("message")',
        '[role="menuitem"]:has-text("Message")'
      ];

      let sendMessageButton = null;

      for (const selector of sendMessageSelectors) {
        try {
          sendMessageButton = await this.page.waitForSelector(selector, { timeout: 2000 });
          if (sendMessageButton) {
            this.logger.info(`Found send message button using selector: ${selector}`);
            await sendMessageButton.click();
            await this.page.waitForTimeout(2000);
            this.logger.info('✅ Successfully clicked Send message option (fallback method)');
            return;
          }
        } catch (e) {
          continue;
        }
      }

      // Debug: Log all available menu options
      const menuOptions = await this.page.evaluate(() => {
        const options = [];
        const allElements = document.querySelectorAll('div, button, [role="button"], [role="menuitem"], a, span');

        allElements.forEach((el, index) => {
          const text = el.textContent?.trim();
          if (text && text.length > 0 && text.length < 50 && el.offsetParent !== null) {
            options.push({
              index: index,
              text: text,
              tagName: el.tagName,
              role: el.getAttribute('role'),
              className: el.className.substring(0, 50)
            });
          }
        });

        return options.slice(0, 20); // Limit to first 20 for readability
      });

      this.logger.error('Could not find "Send message" option. Available menu options:', menuOptions);
      throw new Error('Could not find "Send message" option in menu');

    } catch (error) {
      this.logger.error('Failed to click Send message option:', error);
      throw error;
    }
  }

  async waitForChatRedirect() {
    try {
      // Wait for Instagram to redirect to the chat page
      await this.page.waitForFunction(() => {
        return window.location.href.includes('/direct/t/') ||
          window.location.href.includes('/direct/inbox/');
      }, { timeout: 15000 });

      // Wait a bit more for chat interface to load
      await this.page.waitForTimeout(3000);

      const currentUrl = this.page.url();
      this.logger.info(`✅ Successfully redirected to chat: ${currentUrl}`);

      // Verify we're in a chat interface
      const isChatInterface = await this.page.evaluate(() => {
        // Look for message input or chat indicators
        const messageInput = document.querySelector('textarea[placeholder*="Message"]') ||
          document.querySelector('div[contenteditable="true"]') ||
          document.querySelector('input[placeholder*="Message"]');

        return !!messageInput;
      });

      if (!isChatInterface) {
        throw new Error('Redirected but chat interface not detected');
      }

      this.logger.info('✅ Chat interface confirmed ready');

    } catch (error) {
      this.logger.error('Failed to wait for chat redirect:', error);
      throw error;
    }
  }

  // LEGACY METHODS (keeping for fallback if needed)

  async clickNewMessageButton() {
    try {
      // Try multiple selectors for new message button
      const newMessageSelectors = [
        'div.x1i10hfl.x972fbf.x10w94by.x1qhh985.x14e42zd.x9f619.xe8uvvx.xdj266r.x14z9mp.xat24cr.x1lziwak.x16tdsg8.x1hl2dhg.xggy1nq.x1a2a7pz.x6s0dn4.xjbqb8w.x1ejq31n.x18oe1m7.x1sy0etr.xstzfhl.x1ypdohk.x78zum5.xl56j7k.x1y1aw1k.xf159sx.xwib8y2.xmzvs34.xcdnw81[role="button"]',
        '[aria-label="New message"]',
        'svg[aria-label="New message"]',
        'div[role="button"]:has(svg[aria-label="New message"])',
        'button:has(svg[aria-label="New message"])'
      ];

      let newMessageButton;
      for (const selector of newMessageSelectors) {
        try {
          newMessageButton = await this.page.waitForSelector(selector, { timeout: 2000 }); // Reduced from 3000ms
          if (newMessageButton) break;
        } catch (e) {
          continue;
        }
      }

      if (!newMessageButton) {
        throw new Error('Could not find New Message button');
      }

      await newMessageButton.click();
      await this.page.waitForTimeout(500); // Reduced from 800ms

    } catch (error) {
      this.logger.error('Failed to click new message button:', error);
      throw error;
    }
  }

  async searchUserInNewMessage(username) {
    try {
      // Search input selector from your description
      const searchSelector = 'input.x5ur3kl.x6usi7g.x1bs97v6.x18dxpii.x1j8ye7u.x1rjkts5.x13z9klp.xjc6cxp.x178xt8z.x1lun4ml.xso031l.xpilrb4.x5n08af.x1iyjqo2.xvs91rp.xklk4pu.xdj266r.x14z9mp.xat24cr.x1lziwak.x1plvlek.xryxfnj.x1iorvi4.xpdmqnj.xjkvuk6.x1s3xk63.xlqc9nw.x8tigb1.x1ad04t7.x1glnyev.xs3hnx8.x7xwk5j.x1rheh84.x1ck6gwh.xm4o3d5.x1meze4m.x1t1bzth.x1qt4tve.x1s07b3s.xkq2eht.x1rvh84u.x1ejq31n.x18oe1m7.x1sy0etr.xstzfhl.x106a9eq.xjbqb8w.xzd0ubt[placeholder="Search..."]';

      let searchInput;
      try {
        searchInput = await this.page.waitForSelector(searchSelector, { timeout: 10000 });
      } catch {
        // Fallback search selectors
        const fallbackSearchSelectors = [
          'input[placeholder*="Search"]',
          'input[name="queryBox"]',
          'input[type="text"]'
        ];

        for (const selector of fallbackSearchSelectors) {
          try {
            searchInput = await this.page.waitForSelector(selector, { timeout: 3000 });
            break;
          } catch (e) {
            continue;
          }
        }
      }

      if (!searchInput) {
        throw new Error('Could not find search input');
      }

      // Clear input first, then type username
      await searchInput.click({ clickCount: 3 });
      await this.page.keyboard.press('Backspace');
      await searchInput.type(username, { delay: 100 });

      // Wait longer for search results to load
      await this.page.waitForTimeout(3000);

      this.logger.info(`Search completed for username: ${username}`);

    } catch (error) {
      this.logger.error(`Failed to search for user ${username}:`, error);
      throw error;
    }
  }

  async debugSearchResults() {
    try {
      this.logger.info('🔍 DEBUG: Analyzing search results...');

      const debugInfo = await this.page.evaluate(() => {
        const results = [];

        // Find all potential user result elements
        const buttons = document.querySelectorAll('[role="button"]');

        buttons.forEach((button, index) => {
          if (index < 5) { // Only check first 5 buttons
            const hasImage = button.querySelector('img[alt]') || button.querySelector('span[style*="background-image"]');
            const textContent = button.textContent ? button.textContent.trim() : '';
            const isVisible = button.offsetParent !== null;

            if (hasImage && textContent && isVisible) {
              results.push({
                index: index,
                text: textContent.substring(0, 100), // First 100 chars
                hasImage: !!hasImage,
                isVisible: isVisible,
                className: button.className.substring(0, 200) // First 200 chars of class
              });
            }
          }
        });

        return {
          totalButtons: buttons.length,
          userResults: results,
          pageTitle: document.title,
          currentUrl: window.location.href
        };
      });

      this.logger.info('DEBUG Results:', JSON.stringify(debugInfo, null, 2));

      if (debugInfo.userResults.length === 0) {
        this.logger.warn('⚠️ No user results found in search!');
      } else {
        this.logger.info(`✅ Found ${debugInfo.userResults.length} potential user results`);
        debugInfo.userResults.forEach((result, i) => {
          this.logger.info(`  Result ${i + 1}: "${result.text}"`);
        });
      }

    } catch (error) {
      this.logger.error('Failed to debug search results:', error);
    }
  }

  async clickFirstUserResult() {
    try {
      this.logger.info('Looking for user search results...');

      // Wait a bit more for results to load
      await this.page.waitForTimeout(2000);

      // Multiple strategies to find and click the first user result
      const strategies = [
        // Strategy 1: Based on your HTML - specific user result selector
        async () => {
          const userResults = await this.page.$$('div.x1i10hfl.x1qjc9v5.xjbqb8w.xjqpnuy.xc5r6h4.xqeqjp1.x1phubyo.x13fuv20.x18b5jzi.x1q0q8m5.x1t7ytsu.x972fbf.x10w94by.x1qhh985.x14e42zd.x9f619.x1ypdohk.xdl72j9.x2lah0s.xe8uvvx.xdj266r.x14z9mp.xat24cr.x1lziwak.x2lwn1j.xeuugli.xexx8yu.xyri2b.x18d9i69.x1c1uobl.x1n2onr6.x16tdsg8.x1hl2dhg.xggy1nq.x1ja2u2z.x1t137rt.x1q0g3np.x87ps6o.x1lku1pv.x1a2a7pz.x1dm5mii.x16mil14.xiojian.x1yutycm.x1lliihq.x193iq5w.xh8yej3[role="button"]');
          if (userResults && userResults.length > 0) {
            this.logger.info(`Strategy 1: Found ${userResults.length} user results with specific selector`);
            return userResults[0];
          }
          return null;
        },

        // Strategy 2: Look for any role="button" that contains user info
        async () => {
          const allButtons = await this.page.$$('[role="button"]');
          for (const button of allButtons) {
            const hasUserInfo = await this.page.evaluate((el) => {
              // Check if button contains user-like content (avatar + name)
              const hasImage = el.querySelector('img[alt]') || el.querySelector('span[style*="background-image"]');
              const hasText = el.textContent && el.textContent.trim().length > 0;
              const isVisible = el.offsetParent !== null;
              return hasImage && hasText && isVisible;
            }, button);

            if (hasUserInfo) {
              this.logger.info('Strategy 2: Found user result with avatar and text');
              return button;
            }
          }
          return null;
        },

        // Strategy 3: Look for elements containing user display names
        async () => {
          const textElements = await this.page.$$('*');
          for (const element of textElements) {
            const isUserResult = await this.page.evaluate((el) => {
              const text = el.textContent;
              const isClickable = el.getAttribute('role') === 'button' || el.onclick || el.style.cursor === 'pointer';
              const hasUserPattern = text && (text.includes('LET HIM COOK') || text.length > 3);
              const isVisible = el.offsetParent !== null;
              return isClickable && hasUserPattern && isVisible;
            }, element);

            if (isUserResult) {
              this.logger.info('Strategy 3: Found clickable element with user text');
              return element;
            }
          }
          return null;
        }
      ];

      let userResult = null;
      for (let i = 0; i < strategies.length; i++) {
        try {
          userResult = await strategies[i]();
          if (userResult) {
            this.logger.info(`Successfully found user result using strategy ${i + 1}`);
            break;
          }
        } catch (error) {
          this.logger.warn(`Strategy ${i + 1} failed:`, error.message);
        }
      }

      if (!userResult) {
        // Log page content for debugging
        const pageContent = await this.page.evaluate(() => {
          return document.body.innerHTML.substring(0, 2000);
        });
        this.logger.error('No user results found. Page content sample:', pageContent);
        throw new Error('Could not find any user results to click');
      }

      // Click the found user result
      await userResult.click();
      await this.page.waitForTimeout(1000);
      this.logger.info('Successfully clicked first user result');

    } catch (error) {
      this.logger.error('Failed to click first user result:', error);
      throw error;
    }
  }

  async clickChatButton() {
    try {
      let chatButton;
      const chatSelectors = [
        'text=Chat',
        'button:has-text("Chat")',
        '[role="button"]:has-text("Chat")',
        'div:has-text("Chat")'
      ];

      for (const selector of chatSelectors) {
        try {
          chatButton = await this.page.waitForSelector(selector, { timeout: 5000 });
          break;
        } catch (e) {
          continue;
        }
      }

      if (!chatButton) {
        throw new Error('Could not find Chat button');
      }

      await chatButton.click();
      await this.page.waitForTimeout(800); // Reduced from 1200ms

    } catch (error) {
      this.logger.error('Failed to click chat button:', error);
      throw error;
    }
  }

  async verifyCorrectUser(expectedUsername) {
    try {
      this.logger.info(`🔍 Verifying we're messaging the correct user: @${expectedUsername}`);

      // Wait for chat header to load
      await this.page.waitForTimeout(2000);

      // Extract username using multiple strategies
      const userInfo = await this.page.evaluate(() => {
        const results = {
          displayName: '',
          username: '',
          foundElements: [],
          strategies: []
        };

        // Strategy 1: Look for username in chat header (for users with chat history)
        // This handles the case where username appears in the header with call/video buttons
        const chatHeaderArea = document.querySelector('[role="main"]');
        if (chatHeaderArea) {
          const headerUsernameSpans = chatHeaderArea.querySelectorAll('span.x1lliihq.x193iq5w.x6ikm8r.x10wlt62.xlyipyv.xuxw1ft');
          for (const span of headerUsernameSpans) {
            const text = span.textContent.trim();
            if (text && text.length > 0 && !text.includes(' ') && text !== 'gior.lin') {
              // Check if this span is in the actual chat header (not message content)
              const isInMessageArea = span.closest('[data-testid*="message"]') ||
                span.closest('.message') ||
                span.textContent.includes('is on Instagram');

              if (!isInMessageArea) {
                results.username = text;
                results.strategies.push(`Strategy 1 (chat header span): Found "${text}"`);
                break;
              }
            }
          }
        }

        // Strategy 1.5: Look for username in "is on Instagram" text (for users without chat history)
        // This handles the case where username appears in the middle of chat area
        if (!results.username) {
          const isOnInstagramElements = document.querySelectorAll('*');
          for (const element of isOnInstagramElements) {
            const text = element.textContent;
            if (text && text.includes('is on Instagram')) {
              // Look for username pattern before "is on Instagram"
              const usernameMatch = text.match(/([a-zA-Z0-9._]+)\s+is on Instagram/);
              if (usernameMatch && usernameMatch[1] && usernameMatch[1] !== 'gior.lin') {
                results.username = usernameMatch[1];
                results.strategies.push(`Strategy 1.5 (is on Instagram): Found "${usernameMatch[1]}" in "${text}"`);
                break;
              }
            }
          }
        }

        // Strategy 1.7: Look for username in specific HTML structure from your screenshots
        if (!results.username) {
          // Based on your HTML, look for spans with specific classes that contain usernames
          const specificSelectors = [
            'span.x1lliihq.x193iq5w.x6ikm8r.x10wlt62.xlyipyv.xuxw1ft',
            'span.x1lliihq.x6ikm8r.x10wlt62.xlyipyv.xuxw1ft',
            'span[dir="auto"]'
          ];

          for (const selector of specificSelectors) {
            const spans = document.querySelectorAll(selector);
            for (const span of spans) {
              const text = span.textContent.trim();
              // Check if this looks like a username and is not the sender
              if (text && text.length >= 3 && text.length <= 30 &&
                /^[a-zA-Z0-9._]+$/.test(text) &&
                text !== 'gior.lin' &&
                !text.includes(' ')) {

                // Check if this span is in the main chat area (not sidebar)
                const isInSidebar = span.closest('[role="navigation"]') ||
                  span.closest('nav') ||
                  span.closest('[aria-label*="Navigation"]');

                if (!isInSidebar) {
                  results.username = text;
                  results.strategies.push(`Strategy 1.7 (specific HTML structure): Found "${text}" using selector "${selector}"`);
                  break;
                }
              }
            }
            if (results.username) break;
          }
        }

        // Strategy 2: Look specifically for recipient username in chat conversation header
        if (!results.username) {
          // Look for the conversation header (top right area)
          const conversationHeaders = document.querySelectorAll('[role="main"] header, [role="main"] [role="banner"]');
          for (const header of conversationHeaders) {
            const usernameSpans = header.querySelectorAll('span.x1lliihq.x193iq5w.x6ikm8r.x10wlt62.xlyipyv.xuxw1ft');
            for (const span of usernameSpans) {
              const text = span.textContent.trim();
              if (text && text.length > 0 && !text.includes(' ') && /^[a-zA-Z0-9._]+$/.test(text)) {
                results.username = text;
                results.strategies.push(`Strategy 2 (conversation header span): Found "${text}"`);
                break;
              }
            }
            if (results.username) break;
          }
        }

        // Strategy 3: Look in chat header area for username patterns (fallback)
        if (!results.username) {
          const headerElements = document.querySelectorAll('header, [role="banner"], div[style*="flex-direction"]');
          for (const element of headerElements) {
            const text = element.textContent;
            if (text) {
              // Look for username patterns (alphanumeric + dots/underscores, no spaces)
              const usernameMatch = text.match(/([a-zA-Z0-9._]{3,30})(?!\s*[a-zA-Z])/g);
              if (usernameMatch) {
                for (const match of usernameMatch) {
                  // Filter out common non-username patterns and sender username
                  if (!match.includes('You:') && !match.includes('sent') && !match.includes('photo') &&
                    match.length >= 3 && match.length <= 30 && match !== 'gior.lin') {
                    results.username = match;
                    results.strategies.push(`Strategy 3 (header pattern): Found "${match}" in "${text.substring(0, 100)}"`);
                    break;
                  }
                }
                if (results.username) break;
              }
            }
          }
        }

        // Strategy 4: Look for any element containing username-like text (but exclude sender username)
        if (!results.username) {
          const allElements = document.querySelectorAll('*');
          for (const element of allElements) {
            if (element.children.length === 0) { // Text-only elements
              const text = element.textContent.trim();
              if (text && text.length >= 3 && text.length <= 30 &&
                /^[a-zA-Z0-9._]+$/.test(text) &&
                !text.includes(' ') && text !== 'gior.lin') { // Exclude sender username
                // Check if this element is not in the sidebar/navigation
                const isInSidebar = element.closest('[role="navigation"]') ||
                  element.closest('nav') ||
                  element.closest('[aria-label*="Navigation"]');

                if (!isInSidebar) {
                  results.username = text;
                  results.strategies.push(`Strategy 4 (text element): Found "${text}"`);
                  break;
                }
              }
            }
          }
        }

        // Strategy 5: Extract username from URL (if in direct message thread)
        if (!results.username) {
          const currentUrl = window.location.href;
          if (currentUrl.includes('/direct/t/')) {
            // URL format: https://www.instagram.com/direct/t/[thread_id]/
            // This doesn't contain username, so skip this strategy
            results.strategies.push(`Strategy 5 (URL): Skipped - URL doesn't contain username`);
          } else {
            results.strategies.push(`Strategy 5 (URL): Current URL: ${currentUrl}`);
          }
        }

        // Strategy 6: Look for display name in header
        const headerText = document.querySelector('header')?.textContent ||
          document.querySelector('[role="banner"]')?.textContent || '';
        if (headerText) {
          const lines = headerText.split('\n').filter(line => line.trim());
          if (lines.length > 0) {
            results.displayName = lines[0].trim();
          }
        }

        // Log all found elements for debugging
        const debugElements = document.querySelectorAll('span, div, header');
        for (let i = 0; i < Math.min(debugElements.length, 10); i++) {
          const element = debugElements[i];
          const text = element.textContent?.trim();
          if (text && text.length > 0 && text.length < 100) {
            results.foundElements.push({
              tagName: element.tagName,
              className: element.className.substring(0, 100),
              text: text.substring(0, 50)
            });
          }
        }

        // Debug: Log all spans with the specific username class
        const allUsernameSpans = document.querySelectorAll('span.x1lliihq.x193iq5w.x6ikm8r.x10wlt62.xlyipyv.xuxw1ft');
        results.allUsernameSpans = [];
        allUsernameSpans.forEach((span, index) => {
          const text = span.textContent?.trim();
          const isInSidebar = span.closest('[role="navigation"]') || span.closest('nav');
          const isInMain = span.closest('[role="main"]');
          const isInMessageArea = span.closest('[data-testid*="message"]') || span.closest('.message');
          const parentText = span.parentElement?.textContent?.substring(0, 100) || '';

          results.allUsernameSpans.push({
            index: index,
            text: text,
            isInSidebar: !!isInSidebar,
            isInMain: !!isInMain,
            isInMessageArea: !!isInMessageArea,
            parentElement: span.parentElement?.tagName || 'unknown',
            parentText: parentText,
            hasCallButtons: parentText.includes('Call') || parentText.includes('Video'),
            hasIsOnInstagram: parentText.includes('is on Instagram')
          });
        });

        // Final fallback: If no username found, try to get the most likely candidate
        if (!results.username && results.allUsernameSpans.length > 0) {
          // Prioritize spans that are:
          // 1. In main area but not in message area
          // 2. Not the sender username
          // 3. In areas with call buttons (chat header)
          const candidates = results.allUsernameSpans.filter(span =>
            span.text &&
            span.text !== 'gior.lin' &&
            span.isInMain &&
            !span.isInSidebar &&
            span.text.length >= 3 &&
            /^[a-zA-Z0-9._]+$/.test(span.text)
          );

          if (candidates.length > 0) {
            // Prefer spans with call buttons (header area)
            const headerCandidate = candidates.find(c => c.hasCallButtons);
            const chosenCandidate = headerCandidate || candidates[0];

            results.username = chosenCandidate.text;
            results.strategies.push(`Strategy Final (best candidate): Found "${chosenCandidate.text}" (hasCallButtons: ${chosenCandidate.hasCallButtons}, isInMessageArea: ${chosenCandidate.isInMessageArea})`);
          }
        }

        return results;
      });

      this.logger.info('User verification strategies used:', userInfo.strategies);
      this.logger.info('User verification result:', {
        foundUsername: userInfo.username,
        displayName: userInfo.displayName,
        expectedUsername: expectedUsername
      });

      // Log all username spans found for debugging
      if (userInfo.allUsernameSpans && userInfo.allUsernameSpans.length > 0) {
        this.logger.info('All username spans found:');
        userInfo.allUsernameSpans.forEach((span, index) => {
          this.logger.info(`  Span ${index + 1}: "${span.text}"`);
          this.logger.info(`    - sidebar: ${span.isInSidebar}, main: ${span.isInMain}, messageArea: ${span.isInMessageArea}`);
          this.logger.info(`    - parent: ${span.parentElement}, hasCallButtons: ${span.hasCallButtons}`);
          this.logger.info(`    - hasIsOnInstagram: ${span.hasIsOnInstagram}`);
          this.logger.info(`    - parentText: "${span.parentText}"`);
        });
      }

      // Clean and compare usernames
      const foundUsername = userInfo.username.toLowerCase().replace('@', '');
      const expectedUsernameClean = expectedUsername.toLowerCase().replace('@', '');

      if (foundUsername === expectedUsernameClean) {
        this.logger.info(`✅ User verification SUCCESS: Found "${foundUsername}" matches expected "${expectedUsernameClean}"`);
        return true;
      } else {
        this.logger.error(`❌ User verification FAILED:`);
        this.logger.error(`  Expected: "${expectedUsernameClean}"`);
        this.logger.error(`  Found: "${foundUsername}"`);
        this.logger.error(`  Display name: "${userInfo.displayName}"`);

        // Log debugging info
        this.logger.error('  Strategies tried:', userInfo.strategies);
        this.logger.error('  Sample elements found:');
        userInfo.foundElements.slice(0, 5).forEach((element, index) => {
          this.logger.error(`    ${index + 1}. ${element.tagName}: "${element.text}"`);
        });

        return false;
      }

    } catch (error) {
      this.logger.error('Failed to verify user:', error);
      // If verification fails due to error, we should not proceed
      return false;
    }
  }

  async saveFailedMessage(messageData) {
    try {
      if (!this.database) {
        this.logger.warn('Database not available for saving failed message');
        return;
      }

      // Determine status based on error type
      let status = 'messaging_failed';
      let verificationFailed = false;

      if (messageData.error && messageData.error.includes('verification')) {
        status = 'verification_failed';
        verificationFailed = true;
      } else if (messageData.error && messageData.error.includes('User not found')) {
        status = 'user_not_found';
      } else if (messageData.error && messageData.error.includes('Max retries reached')) {
        status = 'max_retries_reached';
      }

      // Save failed message with detailed info
      const failedMessage = {
        username: messageData.username,
        original_comment: messageData.originalComment,
        customer_type: messageData.customerType,
        status: status,
        error_message: messageData.error,
        failed_at: messageData.failedAt,
        retry_count: messageData.retries || 0,
        max_retries: messageData.maxRetries || 3,
        comment_timestamp: messageData.timestamp,
        // Additional context for manual review
        search_attempted: messageData.username,
        verification_failed: verificationFailed
      };

      await this.database.saveFailedMessage(failedMessage);
      this.logger.info(`💾 Saved failed message (${status}) for manual review: @${messageData.username}`);

      // Emit event for UI to show failed message
      this.emit('messageVerificationFailed', {
        username: messageData.username,
        comment: messageData.originalComment,
        error: messageData.error,
        timestamp: messageData.failedAt,
        status: status
      });

    } catch (error) {
      this.logger.error('Failed to save failed message:', error);
    }
  }

  async sendTemplateMessages(messageData) {
    try {
      let messagesSent = 0;

      // Use template_type directly from messageData
      const templateType = messageData.template_type || 'normal';

      this.logger.info(`=== SEND TEMPLATE MESSAGES ===`);
      this.logger.info(`Customer: ${messageData.username} (${messageData.customerType})`);
      this.logger.info(`Template type: ${templateType}`);

      // Get templates directly with template_type filter
      const templates = await this.getActiveTemplatesForCustomerTypeAndTemplateType(messageData.customerType, templateType);

      if (!templates || templates.length === 0) {
        this.logger.info(`No active ${templateType} templates found for customer type: ${messageData.customerType}`);
        return;
      }

      this.logger.info(`Found ${templates.length} active ${templateType} templates for ${messageData.customerType} customer`);

      // Send each template as a separate message, but each template (even with line breaks) should be one message
      for (let i = 0; i < templates.length; i++) {
        const template = templates[i];

        // Check if this is a send_once template and user has already received it
        if (template.send_once) {
          this.logger.info(`🔍 Checking send_once template "${template.name}" for @${messageData.username}...`);
          const hasReceived = await this.database.hasReceivedSendOnceTemplate(
            messageData.username,
            template.name,
            messageData.customer_type,
            messageData.template_type || 'normal'
          );
          this.logger.info(`📋 Send_once check result for "${template.name}" -> hasReceived: ${hasReceived}`);

          if (hasReceived) {
            this.logger.info(`⏭️ Skipping send_once template "${template.name}" for @${messageData.username} - already sent before`);
            continue;
          } else {
            this.logger.info(`🆕 Sending send_once template "${template.name}" for @${messageData.username} - first time`);
          }
        } else {
          this.logger.info(`📤 Sending regular template "${template.name}" for @${messageData.username}`);
        }

        // Extract price from comment using price mappings
        const extractPrice = async (text) => {
          if (!text) return '';

          try {
            // Get active price mappings from database
            const priceMappings = await this.database.getActivePriceMappings();

            // Check for prefix-based price mapping first
            for (const mapping of priceMappings) {
              const prefixPattern = new RegExp(`${mapping.prefix}(\\d+)`, 'i');
              const match = text.match(prefixPattern);
              if (match) {
                this.logger.info(`🏷️ Found price mapping: ${match[0]} → ${mapping.price} (prefix: ${mapping.prefix})`);
                return mapping.price;
              }
            }

            // Fallback to original price extraction patterns
            const patterns = [
              /[t@#](\d{2,})/i,        // t100, @180, #200 (at least 2 digits)
              /(\d{2,})[sk]/i,         // 190s, 50k (at least 2 digits)
              /(\d{2,})(?:vnd|đ|d)/i,  // 200vnd, 150đ, 100d (at least 2 digits)
              /(\d{2,})/               // fallback: any number with at least 2 digits
            ];

            for (const pattern of patterns) {
              const match = text.match(pattern);
              if (match) {
                const number = match[1] || match[0];
                // Double check that the extracted number has at least 2 digits
                if (number && number.length >= 2) {
                  return number;
                }
              }
            }

            return '';
          } catch (error) {
            this.logger.error('Error extracting price:', error);
            return '';
          }
        };

        // Process template with variables
        const extractedPrice = await extractPrice(messageData.originalComment);
        const processedMessage = this.processTemplate(template.template, {
          username: messageData.username,
          content: messageData.originalComment,
          price: extractedPrice,
          timestamp: new Date().toLocaleString('vi-VN', {
            timeZone: 'Asia/Ho_Chi_Minh',
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
          }),
          shop_name: 'Shop của bạn'
        });

        this.logger.info(`Sending template ${i + 1}/${templates.length}: ${template.name}${template.send_once ? ' (send_once)' : ''}`);
        this.logger.info(`Template content: ${processedMessage}`);
        this.logger.info(`Template content length: ${processedMessage.length} characters`);

        // Clean the processed message to prevent character duplication
        const cleanedMessage = this.cleanDuplicatedCharacters(processedMessage);

        this.logger.info(`Final message to send: "${cleanedMessage}"`);
        this.logger.info(`Message length: ${cleanedMessage.length} characters`);

        // Send this template as one complete message (preserving line breaks within the template)
        await this.typeAndSendMessage(cleanedMessage);

        // Record send_once template if applicable
        if (template.send_once) {
          await this.database.recordSendOnceTemplate(
            messageData.username,
            template.name,
            messageData.customer_type,
            messageData.template_type || 'normal'
          );
          this.logger.info(`✅ Recorded send_once template "${template.name}" for @${messageData.username}`);
        }

        messagesSent++;

        // Wait between different template messages
        if (i < templates.length - 1) {
          await this.page.waitForTimeout(500); // Reduced from 1000ms
        }
      }

      this.logger.info(`✅ Successfully sent ${messagesSent} messages to @${messageData.username}`);

    } catch (error) {
      this.logger.error('Failed to send template messages:', error);
      throw error;
    }
  }

  async getActiveTemplatesForCustomerType(customerType) {
    try {
      if (!this.database) {
        this.logger.warn('Database not available for template lookup');
        return [];
      }

      const templates = await this.database.getActiveTemplatesByCustomerType(customerType);
      return templates || [];
    } catch (error) {
      this.logger.error('Failed to get active templates:', error);
      return [];
    }
  }

  async getActiveTemplatesForCustomerTypeAndTemplateType(customerType, templateType) {
    try {
      if (!this.database) {
        this.logger.warn('Database not available for template lookup');
        return [];
      }

      // Use the database method that supports both customer_type and template_type filtering
      const templates = await this.database.getMessageTemplates(customerType, templateType);

      // Filter only active templates
      const activeTemplates = templates.filter(template => template.is_active === 1);

      this.logger.info(`Found ${activeTemplates.length} active ${templateType} templates for ${customerType} customers`);

      return activeTemplates || [];
    } catch (error) {
      this.logger.error('Failed to get active templates by type:', error);
      return [];
    }
  }

  // REMOVED: recordSentMessage method - messages table no longer exists
  // Message history is not tracked anymore for performance optimization

  async typeAndSendMessage(content) {
    try {
      this.logger.info('Starting to type and send message...');
      this.logger.info(`Message content length: ${content.length} characters`);
      this.logger.info(`Message preview: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`);

      // Find message input
      const messageSelectors = [
        'div[contenteditable="true"][data-testid="message-input"]',
        'div[contenteditable="true"]',
        'textarea[placeholder*="Message"]'
      ];

      let messageInput = null;
      for (const selector of messageSelectors) {
        messageInput = await this.page.$(selector);
        if (messageInput) {
          this.logger.info(`Found message input with selector: ${selector}`);
          break;
        }
      }

      if (!messageInput) {
        throw new Error('Message input not found');
      }

      // Use simple typing method for better reliability
      this.logger.info('Using typing method to input message content');
      await this.typeMessageWithSpecialHandling(messageInput, content);
      this.logger.info('Successfully typed message content');

      // Wait a moment for content to be set
      await this.page.waitForTimeout(300); // Reduced from 500ms

      // Send message by clicking Send button or using keyboard
      try {
        // First try to find Send button using more specific selectors
        const sendButtonFound = await this.page.evaluate(() => {
          // Look for Send button with various selectors
          const selectors = [
            'button[type="submit"]',
            'button:contains("Send")',
            'button:contains("Gửi")',
            'div[role="button"]:contains("Send")',
            'div[role="button"]:contains("Gửi")',
            'button[aria-label*="Send"]',
            'button[aria-label*="Gửi"]',
            // Instagram specific selectors
            'button svg[aria-label*="Send"]',
            'div[role="button"] svg[aria-label*="Send"]'
          ];

          for (const selector of selectors) {
            try {
              let elements;
              if (selector.includes(':contains')) {
                // Handle :contains pseudo-selector manually
                const baseSelector = selector.split(':contains')[0];
                const text = selector.match(/\("([^"]+)"\)/)[1];
                elements = Array.from(document.querySelectorAll(baseSelector))
                  .filter(el => el.textContent.includes(text));
              } else {
                elements = document.querySelectorAll(selector);
              }

              for (const element of elements) {
                if (element.offsetParent !== null) { // Check if visible
                  element.click();
                  return true;
                }
              }
            } catch (e) {
              continue;
            }
          }
          return false;
        });

        if (sendButtonFound) {
          this.logger.info('Message sent using Send button');
        } else {
          // Fallback: Use Ctrl+Enter to send message (works in most messaging platforms)
          await this.page.keyboard.down('Control');
          await this.page.keyboard.press('Enter');
          await this.page.keyboard.up('Control');
          this.logger.info('Message sent using Ctrl+Enter');
        }
      } catch (error) {
        // Final fallback to simple Enter key
        this.logger.warn('Send button method failed, using Enter key:', error.message);
        await this.page.keyboard.press('Enter');
        this.logger.info('Message sent using Enter key (fallback)');
      }

      // Wait for message to be sent
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Log completion without strict verification (Instagram may not clear input immediately)
      this.logger.info('✅ Message send process completed');

    } catch (error) {
      this.logger.error('Failed to type and send message:', error);
      throw error;
    }
  }

  // Removed setMessageContentDirectly - using simple typing method instead

  // Handle message request dialog for new users
  async handleMessageRequestIfNeeded(username) {
    try {
      this.logger.info(`🔍 Checking for message request dialog for @${username}...`);

      // Wait a moment for any dialogs to appear
      await this.page.waitForTimeout(2000);

      let acceptButtonFound = false;
      let primaryButtonFound = false;

      // Method 1: Look for Accept button by text content
      this.logger.info('🔍 Method 1: Looking for Accept button by text...');
      const buttons = await this.page.$$('button, [role="button"]');

      for (const button of buttons) {
        try {
          const text = await this.page.evaluate(el => el.textContent?.trim(), button);
          if (text && (
            text.includes('Accept') ||
            text.includes('Chấp nhận') ||
            text.includes('Accept Request') ||
            text.includes('Chấp nhận yêu cầu')
          )) {
            this.logger.info(`✅ Found Accept button with text: "${text}"`);

            // Check if button is visible and clickable
            const isVisible = await this.page.evaluate(el => {
              const rect = el.getBoundingClientRect();
              return rect.width > 0 && rect.height > 0;
            }, button);

            if (isVisible) {
              this.logger.info('🔘 Clicking Accept button...');
              await button.click();
              acceptButtonFound = true;
              await this.page.waitForTimeout(1500);
              break;
            }
          }
        } catch (buttonError) {
          // Skip this button and continue
          continue;
        }
      }

      // Method 2: If Accept button was clicked, look for Primary/General option
      if (acceptButtonFound) {
        this.logger.info('🔍 Method 2: Looking for Primary/General option after Accept...');
        await this.page.waitForTimeout(1000);

        const optionButtons = await this.page.$$('button, [role="button"]');

        for (const button of optionButtons) {
          try {
            const text = await this.page.evaluate(el => el.textContent?.trim(), button);
            if (text && (
              text.includes('Primary') ||
              text.includes('General') ||
              text.includes('Chính') ||
              text.includes('Tổng quát') ||
              text.includes('Move to Primary') ||
              text.includes('Chuyển về Chính')
            )) {
              this.logger.info(`✅ Found Primary/General option with text: "${text}"`);

              // Check if button is visible and clickable
              const isVisible = await this.page.evaluate(el => {
                const rect = el.getBoundingClientRect();
                return rect.width > 0 && rect.height > 0;
              }, button);

              if (isVisible) {
                this.logger.info('🔘 Clicking Primary/General option...');
                await button.click();
                primaryButtonFound = true;
                await this.page.waitForTimeout(1500);
                break;
              }
            }
          } catch (buttonError) {
            // Skip this button and continue
            continue;
          }
        }
      }

      if (acceptButtonFound) {
        this.logger.info(`✅ Message request handled successfully for @${username}`);
        if (primaryButtonFound) {
          this.logger.info(`✅ Message moved to Primary folder for @${username}`);
        }
      } else {
        this.logger.info(`ℹ️ No message request dialog found for @${username} - proceeding normally`);
      }

    } catch (error) {
      this.logger.warn(`⚠️ Error handling message request for @${username}:`, error.message);
      // Don't throw error - this is not critical, continue with normal flow
    }
  }

  async typeMessageWithSpecialHandling(messageInput, content) {
    try {
      // Clear input first
      await messageInput.click();
      await this.page.keyboard.down('Control');
      await this.page.keyboard.press('KeyA');
      await this.page.keyboard.up('Control');
      await this.page.keyboard.press('Delete');

      // Wait a moment for clearing
      await this.page.waitForTimeout(200);

      this.logger.info(`Typing content: "${content}"`);

      // Split content by lines to handle line breaks properly
      const lines = content.split('\n');

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i];

        if (line.trim()) {
          // Type each line with 30ms delay
          await messageInput.type(line, { delay: 30 });
        }

        // Add line break if not the last line (using Shift+Enter for Instagram)
        if (i < lines.length - 1) {
          await this.page.keyboard.down('Shift');
          await this.page.keyboard.press('Enter');
          await this.page.keyboard.up('Shift');
          await this.page.waitForTimeout(100); // Wait after line break
        }
      }

      this.logger.info('Content typed successfully with line breaks');

      // Trigger input events to ensure content is recognized
      await this.page.evaluate(() => {
        const messageInput = document.querySelector('div[contenteditable="true"][data-testid="message-input"]') ||
          document.querySelector('div[contenteditable="true"]') ||
          document.querySelector('textarea[placeholder*="Message"]');

        if (messageInput) {
          messageInput.dispatchEvent(new Event('input', { bubbles: true }));
          messageInput.dispatchEvent(new Event('change', { bubbles: true }));
          messageInput.focus();
        }
      });

    } catch (error) {
      this.logger.error('Error in typeMessageWithSpecialHandling:', error);
      throw error;
    }
  }

  getQueueStatus() {
    return {
      queueLength: this.messageQueue.length,
      isProcessing: this.isProcessingQueue,
      isRunning: this.isRunning,
      isLoggedIn: this.isLoggedIn
    };
  }

  async clearBrowserCookies() {
    try {
      this.logger.info('=== CLEARING BROWSER COOKIES ===');

      if (!this.page) {
        this.logger.warn('No page available to clear cookies');
        return false;
      }

      // Get all cookies before clearing
      const cookiesBefore = await this.page.cookies();
      this.logger.info(`Browser cookies before clear: ${cookiesBefore.length} cookies`);

      // Clear all cookies from the browser
      const client = await this.page.target().createCDPSession();
      await client.send('Network.clearBrowserCookies');

      // Also clear storage
      await client.send('Storage.clearDataForOrigin', {
        origin: 'https://www.instagram.com',
        storageTypes: 'all'
      });

      // Verify cookies are cleared
      const cookiesAfter = await this.page.cookies();
      this.logger.info(`Browser cookies after clear: ${cookiesAfter.length} cookies`);

      if (cookiesAfter.length === 0) {
        this.logger.info('Browser cookies successfully cleared');
        return true;
      } else {
        this.logger.warn(`Warning: ${cookiesAfter.length} cookies still remain in browser`);
        return false;
      }
    } catch (error) {
      this.logger.error('Failed to clear browser cookies:', error);
      return false;
    }
  }

  async logout() {
    try {
      this.logger.info('=== LOGGING OUT MESSENGER ===');

      // Clear browser cookies first
      const browserCleared = await this.clearBrowserCookies();

      // Reset login state
      this.isLoggedIn = false;
      this.currentUsername = null;

      // Navigate to login page to ensure logout
      if (this.page) {
        try {
          await this.page.goto('https://www.instagram.com/accounts/login/', { waitUntil: 'networkidle2' });
          this.logger.info('Navigated to login page after logout');
        } catch (error) {
          this.logger.warn('Failed to navigate to login page:', error);
        }
      }

      this.logger.info(`Logout completed. Browser cookies cleared: ${browserCleared}`);
      return browserCleared;
    } catch (error) {
      this.logger.error('Failed to logout:', error);
      throw error;
    }
  }

  async stop() {
    try {
      this.logger.info('Stopping Instagram Messenger...');

      this.isRunning = false;
      this.isProcessingQueue = false;

      // Stop memory monitoring
      this.stopMemoryMonitoring();

      // Stop browser connection monitoring
      this.stopBrowserConnectionMonitoring();

      // Clear retry restart timeout
      if (this.retryRestartTimeout) {
        clearTimeout(this.retryRestartTimeout);
        this.retryRestartTimeout = null;
        this.logger.info('🧹 Cleared retry restart timeout');
      }

      // Force close all pages first
      if (this.browser) {
        try {
          const pages = await this.browser.pages();
          this.logger.info(`🧹 Closing ${pages.length} browser pages during stop...`);
          await Promise.all(pages.map(page => page.close().catch(() => {})));
        } catch (error) {
          this.logger.warn('Error closing browser pages during stop:', error);
        }
      }

      if (this.page) {
        await this.page.close().catch(() => {});
        this.page = null;
      }

      if (this.browser) {
        try {
          // Force disconnect first to prevent hanging
          await this.browser.disconnect().catch(() => {});
          // Then close
          await this.browser.close().catch(() => {});
        } catch (error) {
          this.logger.warn('Error during browser close:', error);
        }
        this.browser = null;
      }

      // Force kill Chrome processes to prevent accumulation
      await this.forceKillChromeProcesses();

      this.isLoggedIn = false;
      this.emit('disconnected');
      this.emit('messenger-disconnected');
      this.logger.info('Instagram Messenger stopped');

    } catch (error) {
      this.logger.error('Error stopping Instagram Messenger:', error);
      throw error;
    }
  }

  // Load settings from database
  async loadSettingsFromDatabase() {
    try {
      const settings = await this.database.getAutoMessageSettings();

      if (settings.maxMessagesBeforeRestart !== undefined) {
        this.maxMessagesBeforeRestart = settings.maxMessagesBeforeRestart;
        this.logger.info(`Loaded maxMessagesBeforeRestart from database: ${this.maxMessagesBeforeRestart}`);
      }

      if (settings.maxMemoryUsageMB !== undefined) {
        this.maxMemoryUsageMB = settings.maxMemoryUsageMB;
        this.logger.info(`Loaded maxMemoryUsageMB from database: ${this.maxMemoryUsageMB}`);
      }
    } catch (error) {
      this.logger.warn('Failed to load settings from database, using defaults:', error.message);
    }
  }

  // Memory monitoring and restart methods
  startMemoryMonitoring() {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
    }

    this.memoryCheckInterval = setInterval(async () => {
      try {
        if (!this.browser || !this.isRunning) return;

        // Get browser memory usage
        const memoryUsage = await this.getBrowserMemoryUsage();

        // Only log memory usage every 5 checks to reduce log spam
        if (this.memoryCheckCount % 5 === 0) {
          this.logger.info(`📊 Memory usage: ${memoryUsage}MB (limit: ${this.maxMemoryUsageMB}MB)`);
        }
        this.memoryCheckCount = (this.memoryCheckCount || 0) + 1;

        if (memoryUsage > this.maxMemoryUsageMB) {
          this.logger.warn(`🚨 High memory usage detected: ${memoryUsage}MB (limit: ${this.maxMemoryUsageMB}MB)`);

          // Check if enough time has passed since last restart
          const timeSinceLastRestart = Date.now() - this.lastRestartTime;
          if (timeSinceLastRestart >= this.minRestartIntervalMs) {

            // PROTECTION: Check if currently sending a message
            if (this.isCurrentlySendingMessage) {
              this.logger.warn('⚠️ High memory but cannot restart - currently sending message to @' + (this.currentMessageData?.username || 'unknown'));
              this.logger.warn('⏳ Marking restart as pending due to high memory - will execute after current message completes');
              this.pendingRestart = true;
            } else {
              this.logger.info('🔄 Restarting browser due to high memory usage...');
              await this.restartBrowser();
            }
          } else {
            this.logger.info(`⏳ Waiting ${Math.ceil((this.minRestartIntervalMs - timeSinceLastRestart) / 1000)}s before next restart`);
          }
        }
      } catch (error) {
        this.logger.error('Error during memory monitoring:', error);
      }
    }, 60000); // Check every 60 seconds (reduced frequency)

    this.logger.info(`📊 Memory monitoring started (limit: ${this.maxMemoryUsageMB}MB, check interval: 60s)`);
  }

  stopMemoryMonitoring() {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
      this.memoryCheckInterval = null;
      this.logger.info('📊 Memory monitoring stopped');
    }
  }

  // Browser connection monitoring to detect crashes
  startBrowserConnectionMonitoring() {
    if (this.browserConnectionCheckInterval) {
      clearInterval(this.browserConnectionCheckInterval);
    }

    this.browserConnectionCheckInterval = setInterval(async () => {
      try {
        if (!this.isRunning) return;

        const isCrashed = await this.isBrowserCrashed();
        if (isCrashed) {
          this.logger.error('🚨 Browser crash detected by connection monitor!');

          // If currently sending a message, wait for it to complete
          if (this.isCurrentlySendingMessage) {
            this.logger.warn('⚠️ Browser crashed but currently sending message - marking for recovery after completion');
            this.pendingBrowserCrashRecovery = true;
          } else {
            this.logger.info('🔄 Starting immediate browser crash recovery...');
            try {
              await this.handleBrowserCrash();
              this.logger.info('✅ Browser crash recovery completed');
            } catch (error) {
              this.logger.error('Failed to recover from browser crash:', error);
              // Stop the service if recovery fails
              this.isRunning = false;
              this.emit('stopped', { reason: 'Browser crash recovery failed' });
            }
          }
        }
      } catch (error) {
        this.logger.error('Error during browser connection monitoring:', error);
      }
    }, 15000); // Check every 15 seconds

    this.logger.info('🔍 Browser connection monitoring started (check interval: 15s)');
  }

  stopBrowserConnectionMonitoring() {
    if (this.browserConnectionCheckInterval) {
      clearInterval(this.browserConnectionCheckInterval);
      this.browserConnectionCheckInterval = null;
      this.logger.info('🔍 Browser connection monitoring stopped');
    }
  }

  async getBrowserMemoryUsage() {
    try {
      if (!this.browser) return 0;

      const pages = await this.browser.pages();
      let totalMemory = 0;

      for (const page of pages) {
        try {
          const metrics = await page.metrics();
          // Convert bytes to MB
          const memoryMB = (metrics.JSHeapUsedSize || 0) / (1024 * 1024);
          totalMemory += memoryMB;
        } catch (error) {
          // Page might be closed, skip
        }
      }

      return Math.round(totalMemory);
    } catch (error) {
      this.logger.error('Error getting browser memory usage:', error);
      return 0;
    }
  }

  async restartBrowser() {
    try {
      this.logger.info('🔄 Starting browser restart process...');

      // Check if enough time has passed since last restart
      const timeSinceLastRestart = Date.now() - this.lastRestartTime;
      if (timeSinceLastRestart < this.minRestartIntervalMs) {
        this.logger.warn(`⏳ Restart too soon, waiting ${Math.ceil((this.minRestartIntervalMs - timeSinceLastRestart) / 1000)}s`);
        return;
      }

      // Save current cookies before restart
      let currentCookies = null;
      if (this.page && !this.page.isClosed()) {
        try {
          currentCookies = await this.page.cookies();
          this.logger.info(`💾 Saved ${currentCookies.length} cookies for restart`);
        } catch (error) {
          this.logger.warn('Failed to save cookies before restart:', error);
        }
      }

      // Stop current browser with improved cleanup
      this.isProcessingQueue = false;

      // Force close all pages first
      if (this.browser) {
        try {
          const pages = await this.browser.pages();
          this.logger.info(`🧹 Closing ${pages.length} browser pages...`);
          await Promise.all(pages.map(page => page.close().catch(() => {})));
        } catch (error) {
          this.logger.warn('Error closing browser pages:', error);
        }
      }

      if (this.page) {
        await this.page.close().catch(() => {});
        this.page = null;
      }

      if (this.browser) {
        try {
          // Force disconnect first to prevent hanging
          await this.browser.disconnect().catch(() => {});
          // Then close
          await this.browser.close().catch(() => {});
        } catch (error) {
          this.logger.warn('Error during browser close in restart:', error);
        }
        this.browser = null;
      }

      // Force kill Chrome processes to prevent accumulation
      await this.forceKillChromeProcesses();

      // Wait longer for complete cleanup
      await new Promise(resolve => setTimeout(resolve, 5000));

      // Reset counters
      this.messagesProcessed = 0;
      this.lastRestartTime = Date.now();

      // Reload settings from database after restart
      await this.loadSettingsFromDatabase();

      // Restart with saved cookies or credentials
      const cookiesToUse = currentCookies || this.savedCookies;

      this.logger.info('🚀 Restarting browser with saved session...');

      // Launch new browser
      this.browser = await puppeteer.launch({
        headless: false,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-blink-features=AutomationControlled',
          '--disable-features=VizDisplayCompositor',
          '--disable-web-security',
          '--disable-dev-shm-usage'
        ],
        defaultViewport: null
      });

      this.page = await this.browser.newPage();

      // Set user agent
      await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');

      // Navigate to Instagram
      await this.page.goto('https://www.instagram.com/', { waitUntil: 'networkidle2' });

      // Try to restore session
      let loginSuccess = false;

      if (cookiesToUse && cookiesToUse.length > 0) {
        try {
          await this.page.setCookie(...cookiesToUse);
          await this.page.reload({ waitUntil: 'networkidle2' });

          const isLoggedIn = await this.checkLoginStatus();
          if (isLoggedIn) {
            this.isLoggedIn = true;
            loginSuccess = true;
            this.logger.info('✅ Successfully restored session with cookies');
          }
        } catch (error) {
          this.logger.warn('Failed to restore session with cookies:', error);
        }
      }

      // If cookie login failed, try credentials
      if (!loginSuccess && this.savedCredentials) {
        try {
          const loginResult = await this.login(this.savedCredentials);
          if (loginResult.success) {
            loginSuccess = true;
            this.logger.info('✅ Successfully logged in with credentials after restart');
          }
        } catch (error) {
          this.logger.error('Failed to login with credentials after restart:', error);
        }
      }

      if (!loginSuccess) {
        throw new Error('Failed to restore session after browser restart');
      }

      this.logger.info('✅ Browser restart completed successfully');
      this.emit('browser-restarted', {
        messagesProcessed: this.messagesProcessed,
        memoryUsage: await this.getBrowserMemoryUsage()
      });

      // ✅ CRITICAL: Resume processing message queue after successful restart
      this.logger.info('🔄 Resuming message queue processing after restart...');

      // Check queue status before resuming
      const queueStats = await this.database.getMessageQueueStats();
      this.logger.info(`📊 Queue status after restart: ${queueStats.pending} pending, ${queueStats.processing} processing, ${queueStats.completed} completed, ${queueStats.failed} failed`);

      // Reset any stuck "processing" messages back to "pending"
      if (queueStats.processing > 0) {
        this.logger.info(`🔧 Resetting ${queueStats.processing} stuck processing messages to pending...`);
        await this.database.resetProcessingMessages();
      }

      // FIXED: Ensure only one queue processor is running
      // Wait a moment to ensure old processor has completely stopped
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Double-check that processing is really stopped
      if (this.isProcessingQueue) {
        this.logger.warn('⚠️ Queue processor still running, forcing stop...');
        this.isProcessingQueue = false;
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      this.logger.info(`🚀 Starting single queue processor after browser restart. Pending messages: ${queueStats.pending}`);
      this.startQueueProcessor(); // This will set isProcessingQueue = true and start processQueue()

      this.logger.info('✅ Queue processing restarted after browser restart');

    } catch (error) {
      this.logger.error('❌ Browser restart failed:', error);

      // If restart fails, stop the messenger
      this.isRunning = false;
      this.isProcessingQueue = false;
      this.emit('restart-failed', { error: error.message });

      throw error;
    }
  }

  // Force kill Chrome processes to prevent accumulation
  // Check if message loading is working properly
  async checkMessageLoadingHealth() {
    try {
      if (!this.page || this.page.isClosed()) {
        this.logger.warn('Page is closed, cannot check message loading health');
        return false;
      }

      // Navigate to direct inbox to test loading
      const currentUrl = this.page.url();
      if (!currentUrl.includes('/direct/')) {
        await this.page.goto('https://www.instagram.com/direct/inbox/', {
          waitUntil: 'domcontentloaded',
          timeout: 10000
        });
      }

      // Wait for inbox to load
      await this.page.waitForTimeout(3000);

      // Check if we can see message elements or user list
      const hasMessageElements = await this.page.evaluate(() => {
        // Check for various message-related elements
        const selectors = [
          '[role="main"]', // Main content area
          '[data-testid="direct-inbox"]', // Inbox container
          'div[role="listbox"]', // User list
          'div[role="button"]', // Message buttons
          'a[href*="/direct/t/"]' // Direct message links
        ];

        return selectors.some(selector => {
          const elements = document.querySelectorAll(selector);
          return elements.length > 0;
        });
      });

      if (!hasMessageElements) {
        this.logger.warn('❌ Message loading health check failed - no message elements found');
        this.messageLoadFailures++;
        return false;
      }

      // Check if we can access user names or message content
      const canLoadUserData = await this.page.evaluate(() => {
        // Look for user names, avatars, or message previews
        const userElements = document.querySelectorAll('img[alt*="profile picture"], span[dir="auto"]');
        return userElements.length > 0;
      });

      if (!canLoadUserData) {
        this.logger.warn('❌ Message loading health check failed - cannot load user data');
        this.messageLoadFailures++;
        return false;
      }

      // Reset failure count on success
      this.messageLoadFailures = 0;
      this.logger.info('✅ Message loading health check passed');
      return true;

    } catch (error) {
      this.logger.error('Message loading health check error:', error);
      this.messageLoadFailures++;
      return false;
    }
  }

  // Force restart due to CPU overload
  async forceRestartDueToCpuOverload() {
    try {
      this.logger.error('🚨 FORCE RESTART DUE TO CPU OVERLOAD');

      // Set flag to indicate this is a CPU overload restart
      this.isRestartingDueToCpuOverload = true;

      // Stop current operations
      await this.stop();

      // Force kill all Chrome processes
      await this.forceKillChromeProcesses();

      // Wait longer for complete cleanup
      await new Promise(resolve => setTimeout(resolve, 10000));

      // Restart exactly like UI "Start Messenger" button - let InstagramMessenger auto-load saved cookies
      this.logger.info('🚀 Restarting messenger after CPU overload (same as UI start)...');

      try {
        // Pass null credentials - let InstagramMessenger load saved cookies from database
        await this.start(null, null);
        this.logger.info('✅ Messenger restarted successfully after CPU overload');
      } catch (startError) {
        this.logger.error('Failed to restart messenger after CPU overload:', startError);
        this.scheduleRetryRestart('CPU overload', 30000);
      }

      this.isRestartingDueToCpuOverload = false;

    } catch (error) {
      this.logger.error('Failed to restart due to CPU overload:', error);
      this.isRestartingDueToCpuOverload = false;

      // Don't throw error - schedule retry instead
      this.scheduleRetryRestart('CPU overload', 30000);
    }
  }

  // Schedule retry restart after failure
  scheduleRetryRestart(reason, delayMs = 30000) {
    this.logger.warn(`⏰ Scheduling retry restart in ${delayMs/1000}s due to: ${reason}`);

    // Clear any existing retry timeout
    if (this.retryRestartTimeout) {
      clearTimeout(this.retryRestartTimeout);
    }

    this.retryRestartTimeout = setTimeout(async () => {
      try {
        this.logger.info(`🔄 Attempting retry restart after: ${reason} (same as UI start)`);

        // Always use same approach as UI - let InstagramMessenger auto-load saved cookies
        await this.start(null, null);
        this.logger.info('✅ Retry restart successful');

        this.retryRestartTimeout = null;

      } catch (error) {
        this.logger.error('Retry restart failed:', error);

        // Schedule another retry with exponential backoff (max 5 minutes)
        const nextDelay = Math.min(delayMs * 2, 300000);
        this.scheduleRetryRestart(`${reason} (retry)`, nextDelay);
      }
    }, delayMs);
  }

  // Check if restart is needed due to message loading failures
  async checkAndHandleMessageLoadingFailures() {
    if (this.messageLoadFailures >= this.maxMessageLoadFailures) {
      this.logger.error(`🚨 Too many message loading failures (${this.messageLoadFailures}/${this.maxMessageLoadFailures})`);

      // Reset failure count
      this.messageLoadFailures = 0;

      // Force restart
      await this.forceRestartDueToMessageLoadingFailure();
      return true;
    }
    return false;
  }

  // Force restart due to message loading failure
  async forceRestartDueToMessageLoadingFailure() {
    try {
      this.logger.error('🚨 FORCE RESTART DUE TO MESSAGE LOADING FAILURE');

      // Stop current operations
      await this.stop();

      // Force kill all Chrome processes and hidden tasks
      await this.forceKillChromeProcesses();

      // Wait for complete cleanup
      await new Promise(resolve => setTimeout(resolve, 8000));

      // Restart exactly like UI "Start Messenger" button - let InstagramMessenger auto-load saved cookies
      this.logger.info('🚀 Restarting messenger after message loading failure (same as UI start)...');

      try {
        // Pass null credentials - let InstagramMessenger load saved cookies from database
        await this.start(null, null);
        this.logger.info('✅ Messenger restarted successfully after message loading failure');
      } catch (startError) {
        this.logger.error('Failed to restart messenger after message loading failure:', startError);
        this.scheduleRetryRestart('Message loading failure', 30000);
      }

    } catch (error) {
      this.logger.error('Failed to restart due to message loading failure:', error);

      // Don't throw error - schedule retry instead
      this.scheduleRetryRestart('Message loading failure', 30000);
    }
  }

  async forceKillChromeProcesses() {
    // TARGETED CLEANUP: Kill only this messenger's Chrome processes, preserve scraper
    try {
      this.logger.info('🔪 Targeted Chrome cleanup for messenger only (preserving scraper)...');

      if (process.platform === 'win32') {
        const { exec } = require('child_process');

        // Method 1: Kill by specific PID if available
        if (this.browserProcessId) {
          this.logger.info(`🎯 Killing messenger browser by PID: ${this.browserProcessId}`);
          await new Promise((resolve) => {
            const timeout = setTimeout(() => {
              this.logger.warn('⏰ PID-based cleanup timeout');
              resolve();
            }, 5000);

            exec(`taskkill /F /PID ${this.browserProcessId} /T`, (error) => {
              clearTimeout(timeout);
              if (error && !error.message.includes('not found')) {
                this.logger.warn(`Error killing messenger PID ${this.browserProcessId}:`, error.message);
              } else {
                this.logger.info(`✅ Messenger browser PID ${this.browserProcessId} killed successfully`);
              }
              resolve();
            });
          });
        }

        // Method 2: Kill by automation flags (messenger-specific)
        this.logger.info('🔧 Killing Chrome processes with messenger flags...');
        await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            this.logger.warn('⏰ Flag-based cleanup timeout');
            resolve();
          }, 8000);

          // Kill Chrome processes with --test-type AND --automation flags (messenger uses both)
          exec('wmic process where "name like \'%chrome%\' and commandline like \'%test-type%\' and commandline like \'%automation%\'" delete', (error) => {
            clearTimeout(timeout);
            if (error && !error.message.includes('not found')) {
              this.logger.warn('Error killing messenger Chrome processes:', error.message);
            } else {
              this.logger.info('✅ Messenger Chrome processes killed by flags');
            }
            resolve();
          });
        });

        // Method 3: Fallback - kill high-memory Chrome processes (likely messenger due to memory leaks)
        this.logger.info('🔧 Fallback: Killing high-memory Chrome processes...');
        await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            this.logger.warn('⏰ High-memory cleanup timeout');
            resolve();
          }, 5000);

          // Kill Chrome processes using more than 1GB (messenger tends to leak memory)
          exec('wmic process where "name=\'chrome.exe\' and WorkingSetSize>1000000000" delete', (error) => {
            clearTimeout(timeout);
            if (error && !error.message.includes('not found')) {
              this.logger.warn('Error killing high-memory Chrome processes:', error.message);
            } else {
              this.logger.info('✅ High-memory Chrome processes killed');
            }
            resolve();
          });
        });

      } else {
        // Linux/Mac - kill by automation patterns
        const { exec } = require('child_process');

        if (this.browserProcessId) {
          this.logger.info(`🎯 Killing messenger browser by PID: ${this.browserProcessId}`);
          await new Promise((resolve) => {
            exec(`kill -9 ${this.browserProcessId}`, (error) => {
              if (error) {
                this.logger.warn(`Error killing messenger PID ${this.browserProcessId}:`, error.message);
              } else {
                this.logger.info(`✅ Messenger browser PID ${this.browserProcessId} killed`);
              }
              resolve();
            });
          });
        }

        await new Promise((resolve) => {
          exec('pkill -f "chrome.*--test-type.*--automation"', (error) => {
            if (error && error.code !== 1) {
              this.logger.warn('Error killing messenger Chrome processes:', error.message);
            } else {
              this.logger.info('✅ Messenger Chrome processes killed');
            }
            resolve();
          });
        });
      }

      this.logger.info('✅ Targeted Chrome cleanup completed - scraper preserved');
    } catch (error) {
      this.logger.error('Failed targeted Chrome cleanup:', error);
    }
  }

  // Browser crash detection and recovery methods
  async checkBrowserHealth() {
    try {
      if (!this.browser || !this.page) {
        this.logger.warn('Browser or page not available for health check');
        return false;
      }

      // Check if browser is connected
      if (!this.browser.isConnected()) {
        this.logger.warn('Browser is not connected');
        return false;
      }

      // Check if page is closed
      if (this.page.isClosed()) {
        this.logger.warn('Page is closed');
        return false;
      }

      // Try a simple page evaluation to test responsiveness
      const isResponsive = await Promise.race([
        this.page.evaluate(() => true),
        new Promise(resolve => setTimeout(() => resolve(false), 5000))
      ]);

      if (!isResponsive) {
        this.logger.warn('Page is not responsive');
        return false;
      }

      return true;
    } catch (error) {
      this.logger.warn('Browser health check failed:', error.message);
      return false;
    }
  }

  async isBrowserCrashed() {
    try {
      // Quick checks first
      if (!this.browser || !this.page) {
        this.logger.warn('Browser or page is null - likely crashed');
        return true;
      }

      // Check if browser is connected
      if (!this.browser.isConnected()) {
        this.logger.warn('Browser is not connected - likely crashed');
        return true;
      }

      // Check if page is closed
      if (this.page.isClosed()) {
        this.logger.warn('Page is closed - likely crashed');
        return true;
      }

      // Try to get page URL with timeout
      try {
        const url = await Promise.race([
          this.page.url(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 3000))
        ]);

        // If we get a blank page or chrome-error page, it's likely crashed
        if (!url || url === 'about:blank' || url.includes('chrome-error://')) {
          this.logger.warn(`Page shows crash indicator URL: ${url}`);
          return true;
        }
      } catch (error) {
        this.logger.warn('Failed to get page URL - likely crashed:', error.message);
        return true;
      }

      // Try a simple page evaluation with short timeout
      try {
        const canEvaluate = await Promise.race([
          this.page.evaluate(() => document.readyState),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 2000))
        ]);

        if (!canEvaluate) {
          this.logger.warn('Page evaluation failed - likely crashed');
          return true;
        }
      } catch (error) {
        this.logger.warn('Page evaluation failed - likely crashed:', error.message);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.warn('Error checking browser crash status:', error.message);
      return true; // Assume crashed if we can't check
    }
  }

  async handleBrowserCrash() {
    try {
      this.logger.error('🚨 HANDLING BROWSER CRASH - Starting recovery process (safe mode)');

      // Stop current operations
      this.isRunning = false;
      this.isProcessingQueue = false;

      // Safe cleanup: Only kill messenger Chrome processes, preserve scraper
      await this.forceKillChromeProcesses();

      // Wait for complete cleanup
      await new Promise(resolve => setTimeout(resolve, 8000));

      // Restart exactly like UI "Start Messenger" button - let InstagramMessenger auto-load saved cookies
      this.logger.info('🚀 Restarting messenger after browser crash (same as UI start, scraper preserved)...');

      try {
        // Pass null credentials - let InstagramMessenger load saved cookies from database
        await this.start(null, null);
        this.logger.info('✅ Messenger restarted successfully after browser crash - scraper unaffected');
      } catch (startError) {
        this.logger.error('Failed to restart messenger after browser crash:', startError);
        this.scheduleRetryRestart('Browser crash', 30000);
      }

    } catch (error) {
      this.logger.error('Failed to recover from browser crash:', error);

      // Don't throw error - schedule retry instead
      this.scheduleRetryRestart('Browser crash', 30000);
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      isLoggedIn: this.isLoggedIn,
      queueLength: this.messageQueue.length,
      isProcessingQueue: this.isProcessingQueue,
      browserConnected: !!this.browser,
      pageConnected: !!this.page,
      messagesProcessed: this.messagesProcessed,
      maxMessagesBeforeRestart: this.maxMessagesBeforeRestart,
      memoryLimit: this.maxMemoryUsageMB,
      lastRestartTime: this.lastRestartTime
    };
  }
}

module.exports = InstagramMessenger;
