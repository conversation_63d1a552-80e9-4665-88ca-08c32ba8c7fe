/**
 * Test script để kiểm tra Chrome cleanup khi tắt app
 */

const { exec } = require('child_process');

async function listChromeProcesses() {
  return new Promise((resolve) => {
    if (process.platform === 'win32') {
      exec('wmic process where "name=\'chrome.exe\'" get ProcessId,CommandLine /format:csv', (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        
        const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node'));
        const processes = lines.map(line => {
          const parts = line.split(',');
          if (parts.length >= 3) {
            return {
              pid: parts[1],
              commandLine: parts[2] || ''
            };
          }
          return null;
        }).filter(p => p && p.pid);
        
        resolve(processes);
      });
    } else {
      exec('ps aux | grep chrome', (error, stdout) => {
        if (error) {
          resolve([]);
          return;
        }
        
        const lines = stdout.split('\n').filter(line => line.includes('chrome'));
        resolve(lines.map(line => ({ commandLine: line })));
      });
    }
  });
}

async function testChromeCleanup() {
  console.log('🔍 Testing Chrome cleanup functionality...\n');
  
  console.log('📊 Current Chrome processes:');
  const beforeProcesses = await listChromeProcesses();
  
  if (beforeProcesses.length === 0) {
    console.log('   ✅ No Chrome processes found');
  } else {
    beforeProcesses.forEach((proc, index) => {
      const isTestingChrome = proc.commandLine.includes('Google Chrome for Testing');
      const hasTestType = proc.commandLine.includes('--test-type');
      const hasAutomation = proc.commandLine.includes('--automation');
      const hasNoSandbox = proc.commandLine.includes('--no-sandbox');
      
      console.log(`   ${index + 1}. PID: ${proc.pid || 'N/A'}`);
      console.log(`      🔍 Testing Chrome: ${isTestingChrome ? '✅' : '❌'}`);
      console.log(`      🔍 Test Type Flag: ${hasTestType ? '✅' : '❌'}`);
      console.log(`      🔍 Automation Flag: ${hasAutomation ? '✅' : '❌'}`);
      console.log(`      🔍 No Sandbox Flag: ${hasNoSandbox ? '✅' : '❌'}`);
      console.log(`      📝 Command: ${proc.commandLine.substring(0, 100)}...`);
      console.log('');
    });
  }
  
  console.log('\n🧪 Testing cleanup commands...\n');
  
  // Test comprehensive cleanup commands
  const cleanupCommands = [
    {
      name: 'Chrome for Testing',
      command: 'wmic process where "name=\'chrome.exe\' and commandline like \'%Google Chrome for Testing%\'" get ProcessId /format:csv'
    },
    {
      name: 'Puppeteer Chrome',
      command: 'wmic process where "name=\'chrome.exe\' and commandline like \'%puppeteer%\'" get ProcessId /format:csv'
    },
    {
      name: 'Test Type Flag',
      command: 'wmic process where "name=\'chrome.exe\' and commandline like \'%test-type%\'" get ProcessId /format:csv'
    },
    {
      name: 'Automation Flag',
      command: 'wmic process where "name=\'chrome.exe\' and commandline like \'%automation%\'" get ProcessId /format:csv'
    },
    {
      name: 'Headless Flag',
      command: 'wmic process where "name=\'chrome.exe\' and commandline like \'%headless%\'" get ProcessId /format:csv'
    },
    {
      name: 'No Sandbox Flag',
      command: 'wmic process where "name=\'chrome.exe\' and commandline like \'%--no-sandbox%\'" get ProcessId /format:csv'
    },
    {
      name: 'Remote Debugging',
      command: 'wmic process where "name=\'chrome.exe\' and commandline like \'%--remote-debugging-port%\'" get ProcessId /format:csv'
    },
    {
      name: 'Temp Directory',
      command: 'wmic process where "name=\'chrome.exe\' and (commandline like \'%temp%\' or commandline like \'%tmp%\')" get ProcessId /format:csv'
    }
  ];
  
  for (const cmd of cleanupCommands) {
    console.log(`🔍 Testing: ${cmd.name}`);
    
    await new Promise((resolve) => {
      exec(cmd.command, (error, stdout) => {
        if (error) {
          console.log(`   ❌ Error: ${error.message}`);
        } else {
          const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node'));
          const pids = lines.map(line => line.split(',')[1]).filter(pid => pid && pid.trim());
          
          if (pids.length === 0) {
            console.log(`   ✅ No processes found matching this pattern`);
          } else {
            console.log(`   🎯 Found ${pids.length} processes: ${pids.join(', ')}`);
          }
        }
        resolve();
      });
    });
  }
  
  console.log('\n📋 Comprehensive Cleanup Strategy:');
  console.log('   1. ✅ Kill "Google Chrome for Testing" processes (primary target)');
  console.log('   2. ✅ Kill Puppeteer Chrome processes');
  console.log('   3. ✅ Kill processes with --test-type, --automation, --headless flags');
  console.log('   4. ✅ Kill processes with Puppeteer flags (--no-sandbox, --disable-setuid-sandbox)');
  console.log('   5. ✅ Kill processes from temp directories');
  console.log('   6. ✅ Kill processes with remote debugging ports');

  console.log('\n💡 Implementation in main.js:');
  console.log('   - Multiple cleanup methods targeting different Chrome Testing patterns');
  console.log('   - Sequential execution with delays to prevent race conditions');
  console.log('   - Handle errors gracefully (processes may already be gone)');
  console.log('   - Use SIGINT/SIGTERM handlers, window-all-closed event, and process.exit');
  console.log('   - Comprehensive coverage for visible + hidden Chrome Testing processes');
  
  console.log('\n✅ Test completed!');
}

// Run test
if (require.main === module) {
  testChromeCleanup()
    .then(() => {
      console.log('\n🎉 Chrome cleanup test finished!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test failed:', error);
      process.exit(1);
    });
}

module.exports = { testChromeCleanup, listChromeProcesses };
