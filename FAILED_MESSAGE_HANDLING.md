# 🚫 Xử lý Tin nhắn Thất bại (Failed Message Handling)

## 📋 Tổng quan

Tính năng này đảm bảo rằng khi tin nhắn tự động gửi thất bại (sau khi đã hết số lần retry), tin nhắn sẽ được:
1. **Lưu vào bảng `failed_messages`** để theo dõi và xử lý thủ công
2. **Xóa khỏi bảng `message_queue`** để tránh tắc nghẽn hàng chờ

## 🔄 Luồng xử lý

### Trước đây:
```
<PERSON> nhắn thất bại → Đánh dấu status = 'failed' trong message_queue → Tin nhắn vẫn ở trong queue
```

### Bây giờ:
```
Tin nhắn thất bại → Lưu vào failed_messages → Xóa khỏi message_queue → Hàng chờ sạch sẽ
```

## 📊 Các trường hợp xử lý

### 1. **Max Retries Reached** (<PERSON><PERSON><PERSON> số lần thử lại)
- **Status**: `max_retries_reached`
- **Hành động**: <PERSON><PERSON><PERSON> vào `failed_messages` và xóa khỏi `message_queue`
- **Log**: `📤 Failed message moved to failed_messages table and removed from queue`

### 2. **User Not Found** (Người dùng không tồn tại)
- **Status**: `user_not_found`
- **Hành động**: Lưu vào `failed_messages` và xóa khỏi `message_queue`
- **Log**: `📤 Skipped message (USER_NOT_FOUND) moved to failed_messages table and removed from queue`

### 3. **Verification Failed** (Lỗi xác thực)
- **Status**: `verification_failed`
- **Hành động**: Lưu vào `failed_messages` và xóa khỏi `message_queue`

### 4. **General Messaging Failed** (Lỗi gửi tin nhắn khác)
- **Status**: `messaging_failed`
- **Hành động**: Lưu vào `failed_messages` và xóa khỏi `message_queue`

## 🗄️ Cấu trúc Database

### Bảng `failed_messages`:
```sql
CREATE TABLE failed_messages (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  username TEXT NOT NULL,
  original_comment TEXT NOT NULL,
  customer_type TEXT,
  status TEXT DEFAULT 'verification_failed',
  error_message TEXT,
  failed_at DATETIME,
  retry_count INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  comment_timestamp DATETIME,
  search_attempted TEXT,
  verification_failed BOOLEAN DEFAULT TRUE,
  resolved BOOLEAN DEFAULT FALSE,
  resolved_at DATETIME,
  resolved_by TEXT,
  notes TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP
)
```

### Bảng `message_queue`:
- Tin nhắn thất bại sẽ được **xóa hoàn toàn** khỏi bảng này
- Chỉ giữ lại tin nhắn đang chờ xử lý (`pending`) và đang xử lý (`processing`)

## 🖥️ Giao diện quản lý

### Trang Failed Messages (`/web/failed-messages`):
- **Hiển thị**: Danh sách tất cả tin nhắn thất bại chưa được xử lý
- **Thông tin**: Username, comment gốc, loại lỗi, thời gian, số lần retry
- **Hành động**: Đánh dấu đã xử lý, thêm ghi chú
- **Tự động làm mới**: Cập nhật real-time khi có tin nhắn thất bại mới

### API Endpoints:
- `GET /api/failed-messages` - Lấy danh sách tin nhắn thất bại
- `POST /api/failed-messages/:id/resolve` - Đánh dấu đã xử lý

## 🔧 Cài đặt và Cấu hình

### Auto-messaging Settings:
- **Max Retries**: Số lần thử lại tối đa (mặc định: 3)
- **Retry Delay**: Thời gian chờ giữa các lần thử lại
- **Auto-cleanup**: Tự động dọn dẹp tin nhắn đã xử lý sau X ngày

## 📈 Lợi ích

### 1. **Hàng chờ sạch sẽ**:
- Không bị tắc nghẽn bởi tin nhắn thất bại
- Xử lý FIFO hiệu quả hơn
- Giảm tải database

### 2. **Theo dõi tốt hơn**:
- Phân loại rõ ràng các loại lỗi
- Lưu trữ đầy đủ thông tin để debug
- Giao diện quản lý trực quan

### 3. **Xử lý thủ công**:
- Admin có thể xem và xử lý tin nhắn thất bại
- Đánh dấu đã xử lý với ghi chú
- Tránh gửi lại tin nhắn đã thất bại

## 🧪 Testing

Chạy test để kiểm tra tính năng:
```bash
node test_failed_message_handling.js
```

Test sẽ kiểm tra:
- ✅ Tin nhắn được thêm vào queue
- ✅ Tin nhắn thất bại được lưu vào failed_messages
- ✅ Tin nhắn được xóa khỏi message_queue
- ✅ Thông tin lỗi được lưu đúng

## 📝 Log Messages

### Thành công:
```
💾 Saved failed message (max_retries_reached) for manual review: @username
📤 Failed message moved to failed_messages table and removed from queue: @username
```

### Lỗi User Not Found:
```
🚫 User @username not found - skipping message and moving to next
📤 Skipped message (USER_NOT_FOUND) moved to failed_messages table and removed from queue: @username
```

## 🔄 Migration

Tính năng này tương thích ngược và không cần migration database. Các tin nhắn thất bại hiện tại trong `message_queue` sẽ được xử lý theo logic mới khi hệ thống restart.
