const winston = require('winston');
const { exec } = require('child_process');
const os = require('os');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.simple(),
  transports: [new winston.transports.Console()]
});

class SystemMonitor {
  constructor() {
    this.isMonitoring = false;
    this.monitorInterval = null;
    this.cpuThreshold = 95; // 95% CPU threshold
    this.checkIntervalMs = 10000; // Check every 10 seconds
    this.consecutiveHighCpuCount = 0;
    this.maxConsecutiveHighCpu = 3; // 3 consecutive high CPU readings before action
    this.lastCpuCheck = 0;
    this.messengerService = null;
    this.onCpuOverloadCallback = null;
  }

  // Set messenger service reference
  setMessengerService(messengerService) {
    this.messengerService = messengerService;
  }

  // Set callback for CPU overload
  setOnCpuOverloadCallback(callback) {
    this.onCpuOverloadCallback = callback;
  }

  // Get Windows system CPU usage percentage
  async getWindowsCpuUsage() {
    return new Promise((resolve) => {
      if (process.platform !== 'win32') {
        resolve(0);
        return;
      }

      // Use PowerShell to get CPU usage
      const command = 'powershell "Get-Counter \'\\Processor(_Total)\\% Processor Time\' | Select-Object -ExpandProperty CounterSamples | Select-Object -ExpandProperty CookedValue"';
      
      const timeout = setTimeout(() => {
        logger.warn('CPU check timeout');
        resolve(0);
      }, 5000);

      exec(command, (error, stdout, stderr) => {
        clearTimeout(timeout);
        
        if (error) {
          logger.warn('Failed to get CPU usage:', error.message);
          resolve(0);
          return;
        }

        try {
          const cpuUsage = parseFloat(stdout.trim());
          resolve(isNaN(cpuUsage) ? 0 : Math.round(cpuUsage));
        } catch (e) {
          logger.warn('Failed to parse CPU usage:', e.message);
          resolve(0);
        }
      });
    });
  }

  // Alternative method using wmic (faster)
  async getWindowsCpuUsageWmic() {
    return new Promise((resolve) => {
      if (process.platform !== 'win32') {
        resolve(0);
        return;
      }

      const command = 'wmic cpu get loadpercentage /value';
      
      const timeout = setTimeout(() => {
        logger.warn('CPU check timeout (wmic)');
        resolve(0);
      }, 3000);

      exec(command, (error, stdout, stderr) => {
        clearTimeout(timeout);
        
        if (error) {
          logger.warn('Failed to get CPU usage (wmic):', error.message);
          resolve(0);
          return;
        }

        try {
          const match = stdout.match(/LoadPercentage=(\d+)/);
          if (match) {
            const cpuUsage = parseInt(match[1]);
            resolve(isNaN(cpuUsage) ? 0 : cpuUsage);
          } else {
            resolve(0);
          }
        } catch (e) {
          logger.warn('Failed to parse CPU usage (wmic):', e.message);
          resolve(0);
        }
      });
    });
  }

  // Get system CPU usage (cross-platform fallback)
  getNodeCpuUsage() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (let type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const usage = 100 - ~~(100 * idle / total);
    
    return Math.max(0, Math.min(100, usage));
  }

  // Get comprehensive CPU usage
  async getCpuUsage() {
    try {
      // Try Windows-specific method first (most accurate)
      if (process.platform === 'win32') {
        const wmicCpu = await this.getWindowsCpuUsageWmic();
        if (wmicCpu > 0) {
          return wmicCpu;
        }
      }

      // Fallback to Node.js method
      return this.getNodeCpuUsage();
    } catch (error) {
      logger.error('Failed to get CPU usage:', error);
      return 0;
    }
  }

  // Check if auto-messaging should be restarted due to high CPU
  async checkCpuAndRestart() {
    try {
      const cpuUsage = await this.getCpuUsage();
      const now = Date.now();
      
      logger.debug(`CPU Usage: ${cpuUsage}%`);

      if (cpuUsage >= this.cpuThreshold) {
        this.consecutiveHighCpuCount++;
        logger.warn(`🚨 High CPU detected: ${cpuUsage}% (${this.consecutiveHighCpuCount}/${this.maxConsecutiveHighCpu})`);

        if (this.consecutiveHighCpuCount >= this.maxConsecutiveHighCpu) {
          logger.error(`🚨 CRITICAL: CPU overload detected (${cpuUsage}%) for ${this.consecutiveHighCpuCount} consecutive checks`);
          
          // Trigger auto-messaging restart
          await this.handleCpuOverload(cpuUsage);
          
          // Reset counter after action
          this.consecutiveHighCpuCount = 0;
        }
      } else {
        // Reset counter if CPU is normal
        if (this.consecutiveHighCpuCount > 0) {
          logger.info(`✅ CPU back to normal: ${cpuUsage}% (reset counter)`);
          this.consecutiveHighCpuCount = 0;
        }
      }

      this.lastCpuCheck = now;
      return { cpuUsage, isHigh: cpuUsage >= this.cpuThreshold };
    } catch (error) {
      logger.error('Failed to check CPU usage:', error);
      return { cpuUsage: 0, isHigh: false, error: error.message };
    }
  }

  // Handle CPU overload by restarting auto-messaging
  async handleCpuOverload(cpuUsage) {
    try {
      logger.error(`🚨 HANDLING CPU OVERLOAD: ${cpuUsage}%`);
      logger.info(`🛡️ SCRAPER PROTECTION: Only restarting messenger, scraper will continue running`);

      if (this.messengerService && this.messengerService.isRunning) {
        logger.warn('🔄 Auto-restarting messenger service due to CPU overload...');

        // Force restart with CPU overload reason
        await this.messengerService.forceRestartDueToCpuOverload();

        logger.info('✅ Messenger service restarted due to CPU overload - scraper unaffected');
      } else {
        logger.warn('⚠️ Messenger service not running, cannot restart');
      }

      // Call external callback if provided
      if (this.onCpuOverloadCallback) {
        this.onCpuOverloadCallback({ cpuUsage, timestamp: new Date().toISOString() });
      }

    } catch (error) {
      logger.error('Failed to handle CPU overload:', error);
    }
  }

  // Start monitoring
  startMonitoring() {
    if (this.isMonitoring) {
      logger.warn('System monitor already running');
      return;
    }

    this.isMonitoring = true;
    logger.info(`🔍 Starting system CPU monitoring (threshold: ${this.cpuThreshold}%, interval: ${this.checkIntervalMs}ms)`);
    logger.info(`⚠️ SCRAPER PROTECTION: System monitor will NOT auto-stop scraper`);

    this.monitorInterval = setInterval(async () => {
      await this.checkCpuAndRestart();
    }, this.checkIntervalMs);

    // Initial check
    this.checkCpuAndRestart();
  }

  // Stop monitoring
  stopMonitoring() {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    
    logger.info('🔍 System CPU monitoring stopped');
  }

  // Get monitoring status
  getStatus() {
    return {
      isMonitoring: this.isMonitoring,
      cpuThreshold: this.cpuThreshold,
      checkIntervalMs: this.checkIntervalMs,
      consecutiveHighCpuCount: this.consecutiveHighCpuCount,
      maxConsecutiveHighCpu: this.maxConsecutiveHighCpu,
      lastCpuCheck: this.lastCpuCheck
    };
  }

  // Manual CPU check
  async manualCpuCheck() {
    const result = await this.checkCpuAndRestart();
    return {
      ...result,
      status: this.getStatus(),
      timestamp: new Date().toISOString()
    };
  }
}

// Singleton instance
const systemMonitor = new SystemMonitor();

module.exports = systemMonitor;
