@echo off
echo 🎨 Testing new CommiLive color scheme...
echo.

echo 📋 Color Changes Applied:
echo ✅ Primary: Purple → Sky Blue (#0ea5e9)
echo ✅ Accent: Pink → Teal (#14b8a6)
echo ✅ Buttons: sky-500/sky-600
echo ✅ Focus rings: sky-500
echo ✅ Navigation: sky-100/sky-900
echo ✅ Comments: sky-50/sky-300
echo ✅ Gradients: sky-500 to teal-500
echo.

echo 🔧 Files Updated:
echo ✅ src/web/src/index.css
echo ✅ src/web/src/App.css
echo ✅ src/web/src/App-clean.css
echo ✅ src/web/tailwind.config.js
echo ✅ src/web/public/index.html (theme-color)
echo ✅ src/web/public/manifest.json (theme_color)
echo ✅ src/web/src/components/Sidebar.js
echo ✅ src/web/src/components/Header.js
echo ✅ src/web/src/components/ColorSettings.js
echo ✅ src/web/src/components/SystemMonitor.js
echo ✅ src/web/src/components/LoadingScreen.js
echo.

echo 🚀 Starting development server to test new colors...
echo Press Ctrl+C to stop
echo.

cd src\web
npm start

echo.
echo 🎯 Test checklist:
echo [ ] Header gradient (sky to teal)
echo [ ] Sidebar active states (sky blue)
echo [ ] Button colors (sky blue)
echo [ ] Input focus rings (sky blue)
echo [ ] Comment cards (sky backgrounds)
echo [ ] Navigation highlights
echo [ ] PWA theme color in browser
echo.
pause
