@echo off
echo 🎨 Building CommiLive with new logo...
echo.

echo 📋 Pre-build checklist:
echo ✅ Source logo: CommiLive_logo_nobackground.png
echo ✅ Electron icon: src\assets\icon.png
echo ✅ Web favicon: src\web\public\favicon.png
echo ✅ Web logos: src\web\public\logo192.png, logo512.png
echo.

echo ⚠️  Note: ICO and ICNS files are currently PNG format
echo    For production, convert them using online tools:
echo    - ICO: https://convertio.co/png-ico/
echo    - ICNS: https://convertio.co/png-icns/
echo.

echo 🔨 Building web application...
cd src\web
call npm run build
if errorlevel 1 (
    echo ❌ Web build failed!
    cd ..\..
    pause
    exit /b 1
)
cd ..\..

echo.
echo 📦 Building Electron application...
call npm run build:win
if errorlevel 1 (
    echo ❌ Electron build failed!
    pause
    exit /b 1
)

echo.
echo 🎉 Build completed successfully!
echo 📁 Check the dist folder for the built application
echo 🖼️  The new logo should be visible in:
echo    - Electron app window icon
echo    - Windows taskbar icon
echo    - Desktop shortcut icon
echo    - Web browser favicon
echo.
pause
