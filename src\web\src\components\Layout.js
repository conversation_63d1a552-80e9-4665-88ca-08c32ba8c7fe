import React from 'react';
import { useLocation } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import { useApp } from '../contexts/AppContext';

const Layout = ({ children }) => {
  const location = useLocation();
  const { state, actions } = useApp();
  const { sidebarOpen } = state;

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header - Fixed at top */}
      <Header />

      {/* Sidebar for desktop - Fixed like header */}
      <div className={`
        hidden md:block fixed left-0 top-16 bottom-0 z-40 transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'w-64' : 'w-16'}
      `}>
        <Sidebar />
      </div>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-40 md:hidden">
          <div className="absolute inset-0 bg-gray-600 opacity-75" onClick={() => actions.setSidebarOpen(false)}></div>
          <div className="absolute top-0 left-0 bottom-0 w-64 bg-white shadow-xl">
            <Sidebar />
          </div>
        </div>
      )}

      <div className="flex flex-1 overflow-hidden">
        {/* Spacer for fixed sidebar on desktop */}
        <div className={`
          hidden md:block flex-shrink-0 transition-all duration-300 ease-in-out
          ${sidebarOpen ? 'w-64' : 'w-16'}
        `}></div>

        {/* Main content */}
        <div className="flex-1 flex flex-col min-w-0">
          <main className="flex-1 flex flex-col overflow-hidden">
            {/* Special handling for Comments page */}
            {location.pathname === '/comments' ? (
              <div className="flex-1 flex flex-col overflow-hidden">
                {children}
              </div>
            ) : (
              <div className="flex-1 overflow-y-auto p-2 sm:p-4">
                <div className="max-w-7xl mx-auto">
                  {children}
                </div>
              </div>
            )}
          </main>
        </div>
      </div>


    </div>
  );
};

export default Layout;
