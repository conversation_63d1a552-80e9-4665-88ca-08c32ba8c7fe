# 🔧 Auto-Restart Login Fix

## 🐛 Vấn đề phát hiện

**User report:** "Hệ thống khi tự động kill auto message nhưng k tự động bật lại đăng nhập để tiếp tục hàng chờ, tôi phải bật thủ công thì mới tiếp tục"

## 🔍 Root Cause Analysis

### **Vấn đề 1: Auto-restart Fails Silently**

#### **Current Logic:**
```javascript
// ❌ Problematic logic
if (this.savedCredentials || this.savedCookies) {
  await this.start(this.savedCredentials, this.savedCookies);
} else {
  this.logger.warn('⚠️ No saved credentials/cookies for restart');
  // ❌ STOPS HERE - no restart attempt
}

// ❌ If restart fails
} catch (error) {
  this.logger.error('Failed to restart:', error);
  throw error; // ❌ CRASHES entire service
}
```

#### **Result:**
- **No credentials**: Service doesn't restart at all
- **Restart fails**: Service crashes completely
- **User experience**: Manual restart required

### **Vấn đề 2: No Retry Mechanism**

#### **Current Behavior:**
- **Single restart attempt**: If fails, service stops
- **No retry logic**: No automatic retry after failure
- **No fallback**: No attempt to restart without credentials

### **Vấn đề 3: No User Notification**

#### **Current Behavior:**
- **Silent failure**: User doesn't know restart failed
- **No UI feedback**: No notification that manual login needed
- **Queue stuck**: Messages remain in queue but not processed

## ✅ Solution Implemented

### **1. Enhanced Auto-Restart Logic**

#### **Restart with Fallback:**
```javascript
// ✅ Enhanced logic
if (this.savedCredentials || this.savedCookies) {
  // Try with saved credentials first
  await this.start(this.savedCredentials, this.savedCookies);
  this.logger.info('✅ Messenger restarted successfully');
} else {
  // ✅ NEW: Try restart without credentials
  this.logger.warn('🔄 Attempting to restart without credentials - user will need to login again');
  
  try {
    await this.start(null, null);
    this.logger.info('✅ Messenger restarted without credentials - manual login required');
    
    // ✅ NEW: Notify UI that manual login is needed
    this.emit('restart-requires-login', { 
      reason: 'CPU overload restart without saved credentials',
      timestamp: new Date().toISOString()
    });
  } catch (startError) {
    // ✅ NEW: Schedule retry instead of crashing
    this.scheduleRetryRestart('CPU overload', 30000);
  }
}
```

#### **Error Handling:**
```javascript
// ✅ Enhanced error handling
} catch (error) {
  this.logger.error('Failed to restart:', error);
  
  // ✅ NEW: Don't throw error - schedule retry instead
  this.scheduleRetryRestart('CPU overload', 30000);
}
```

### **2. Retry Restart Mechanism**

#### **Exponential Backoff Retry:**
```javascript
scheduleRetryRestart(reason, delayMs = 30000) {
  this.logger.warn(`⏰ Scheduling retry restart in ${delayMs/1000}s due to: ${reason}`);
  
  this.retryRestartTimeout = setTimeout(async () => {
    try {
      if (this.savedCredentials || this.savedCookies) {
        await this.start(this.savedCredentials, this.savedCookies);
        this.logger.info('✅ Retry restart successful with saved credentials');
      } else {
        // Try restart without credentials
        await this.start(null, null);
        this.logger.info('✅ Retry restart successful - manual login required');
        
        // Notify UI
        this.emit('restart-requires-login', { 
          reason: `Retry restart after ${reason}`,
          timestamp: new Date().toISOString()
        });
      }
    } catch (error) {
      // Schedule another retry with exponential backoff (max 5 minutes)
      const nextDelay = Math.min(delayMs * 2, 300000);
      this.scheduleRetryRestart(`${reason} (retry)`, nextDelay);
    }
  }, delayMs);
}
```

#### **Retry Schedule:**
```
Attempt 1: 30 seconds
Attempt 2: 60 seconds  
Attempt 3: 120 seconds
Attempt 4: 240 seconds
Attempt 5+: 300 seconds (max)
```

### **3. User Notification System**

#### **Frontend Event:**
```javascript
// Server.js - Event listener
instagramMessenger.on('restart-requires-login', (data) => {
  logger.warn(`🔄 Auto-restart completed but requires manual login: ${data.reason}`);
  io.emit('messenger-restart-requires-login', {
    reason: data.reason,
    timestamp: data.timestamp,
    message: 'Messenger đã được khởi động lại nhưng cần đăng nhập thủ công để tiếp tục hàng chờ'
  });
});
```

#### **UI Notification:**
- **Toast notification**: "Messenger cần đăng nhập lại"
- **Status indicator**: Shows "Cần đăng nhập" 
- **Action button**: "Đăng nhập ngay"

### **4. Applied to All Auto-Restart Scenarios**

#### **CPU Overload Restart:**
```javascript
await this.forceRestartDueToCpuOverload();
// ✅ Now includes fallback + retry + notification
```

#### **Message Loading Failure Restart:**
```javascript
await this.forceRestartDueToMessageLoadingFailure();
// ✅ Now includes fallback + retry + notification
```

#### **Browser Crash Recovery:**
```javascript
await this.handleBrowserCrash();
// ✅ Now includes fallback + retry + notification
```

## 📊 Flow After Fix

### **Scenario 1: Auto-restart with Saved Credentials**
```
1. CPU overload detected
   ↓
2. Auto-restart triggered
   ↓
3. Restart with saved credentials ✅
   ↓
4. Continue processing queue ✅
```

### **Scenario 2: Auto-restart without Saved Credentials**
```
1. CPU overload detected
   ↓
2. Auto-restart triggered
   ↓
3. No saved credentials found
   ↓
4. Restart without credentials ✅
   ↓
5. Emit 'restart-requires-login' event
   ↓
6. UI shows "Cần đăng nhập" notification
   ↓
7. User clicks "Đăng nhập ngay"
   ↓
8. Manual login → Continue queue ✅
```

### **Scenario 3: Auto-restart Fails**
```
1. CPU overload detected
   ↓
2. Auto-restart triggered
   ↓
3. Restart fails (network/error)
   ↓
4. Schedule retry in 30s
   ↓
5. Retry restart (exponential backoff)
   ↓
6. Eventually succeeds or notifies user
```

## 🎯 Benefits

### **1. Reliability:**
- **No service crashes**: Errors don't stop the service
- **Automatic recovery**: Multiple retry attempts
- **Fallback options**: Restart without credentials if needed

### **2. User Experience:**
- **Clear notifications**: User knows when manual action needed
- **Automatic retries**: Reduces manual intervention
- **Queue preservation**: Messages don't get lost

### **3. Monitoring:**
- **Detailed logging**: All restart attempts logged
- **Event tracking**: UI can track restart status
- **Debug information**: Easier to troubleshoot issues

## 🧪 Testing Scenarios

### **Test Case 1: Normal Auto-restart**
```
1. Start messenger with login
2. Trigger CPU overload (high load)
3. Wait for auto-restart
4. Verify: Messenger restarts automatically ✅
5. Verify: Queue continues processing ✅
```

### **Test Case 2: Auto-restart without Credentials**
```
1. Start messenger with login
2. Clear saved credentials/cookies
3. Trigger CPU overload
4. Wait for auto-restart
5. Verify: Messenger starts but shows "Cần đăng nhập" ✅
6. Verify: UI notification appears ✅
7. Manual login → Queue resumes ✅
```

### **Test Case 3: Auto-restart Failure + Retry**
```
1. Start messenger
2. Simulate network failure
3. Trigger auto-restart
4. Verify: First restart fails
5. Verify: Retry scheduled (30s) ✅
6. Verify: Retry attempts with backoff ✅
7. Restore network → Eventually succeeds ✅
```

## ⚠️ Important Notes

### **Backward Compatibility:**
- ✅ Existing auto-restart logic still works
- ✅ No breaking changes to API
- ✅ Enhanced with fallback and retry

### **Performance:**
- ✅ Retry timeouts are cleaned up properly
- ✅ No memory leaks from retry scheduling
- ✅ Exponential backoff prevents spam

### **Security:**
- ✅ No credentials stored in logs
- ✅ Secure handling of saved cookies
- ✅ Safe fallback to no-credentials restart

## 🔮 Future Enhancements

### **1. Smart Credential Recovery:**
- Auto-detect if cookies are still valid
- Attempt to refresh expired tokens
- Fallback credential sources

### **2. Advanced Retry Logic:**
- Different retry strategies per failure type
- User-configurable retry intervals
- Circuit breaker pattern

### **3. Enhanced UI Integration:**
- One-click restart from notification
- Auto-login with saved credentials
- Real-time restart progress indicator
