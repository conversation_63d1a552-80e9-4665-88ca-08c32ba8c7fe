@echo off
echo ========================================
echo   UPDATING WEB INTERFACE ONLY
echo ========================================

echo.
echo [1/2] Building web production...
call npm run build:web
if %errorlevel% neq 0 (
    echo ERROR: Web build failed!
    pause
    exit /b 1
)

echo.
echo [2/2] Copying web build to packaged app...
if exist "dist\commilive-win32-x64\resources\app\src\web\build" (
    rmdir /s /q "dist\commilive-win32-x64\resources\app\src\web\build"
)
xcopy "src\web\build" "dist\commilive-win32-x64\resources\app\src\web\build" /e /i /y

echo.
echo ========================================
echo   WEB UPDATE COMPLETED!
echo ========================================
echo.
echo Updated app: dist\commilive-win32-x64\commilive.exe
echo You can now test or distribute the updated app.
pause
