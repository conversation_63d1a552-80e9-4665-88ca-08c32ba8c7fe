# Hướng dẫn Cài đặt và Triển khai

## 📋 Y<PERSON>u cầu hệ thống

### Phần mềm cần thiết
- **Node.js**: <PERSON><PERSON><PERSON> bản 16.0 trở lên
- **npm**: <PERSON><PERSON><PERSON> bản 8.0 trở lên (hoặc yarn 1.22+)
- **Git**: Đ<PERSON> clone repository
- **Chrome/Chromium**: <PERSON> Puppeteer (sẽ tự động tải xuống)

### Phần mềm tùy chọn
- **Redis**: Cho message queue (có thể dùng in-memory thay thế)
- **PM2**: Cho production deployment

### Yêu cầu phần cứng
- **RAM**: Tối thiểu 4GB (khuyến nghị 8GB+)
- **CPU**: Dual-core trở lên
- **Ổ cứng**: 2GB dung lượng trống
- **Mạng**: Kết nối internet ổn định

## 🚀 Cài đặt nhanh

### Bước 1: Clone repository
```bash
git clone <repository-url>
cd instagram-live-comment-system
```

### Bước 2: Cài đặt dependencies
```bash
# Cài đặt dependencies chính
npm install

# Cài đặt dependencies cho web interface
cd src/web
npm install
cd ../..
```

### Bước 3: Cấu hình môi trường
```bash
# Copy file cấu hình mẫu
cp .env.example .env

# Chỉnh sửa file .env
nano .env
```

### Bước 4: Khởi tạo thư mục
```bash
mkdir -p src/backend/data
mkdir -p logs
```

### Bước 5: Chạy ứng dụng
```bash
# Development mode
npm run dev

# Hoặc chạy từng service
npm run server    # Backend server
npm run web       # Web interface  
npm start         # Desktop app
```

## ⚙️ Cấu hình chi tiết

### File .env
```env
# Server Configuration
PORT=3001
NODE_ENV=development

# Database
DB_PATH=./src/backend/data/instagram_live.db

# Instagram (sẽ cấu hình trong app)
INSTAGRAM_USERNAME=
INSTAGRAM_PASSWORD=
INSTAGRAM_LIVE_URL=

# Security
JWT_SECRET=your-super-secret-jwt-key-here
ENCRYPTION_KEY=your-32-character-encryption-key

# Puppeteer
PUPPETEER_HEADLESS=false
PUPPETEER_TIMEOUT=30000

# Features
ENABLE_AUTO_MESSAGING=true
ENABLE_COMMENT_FILTERING=true
```

### Redis (Tùy chọn)
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install redis-server

# macOS
brew install redis

# Windows
# Tải từ https://redis.io/download
```

## 🖥️ Chạy Desktop Application

### Development
```bash
npm start
```

### Build cho production
```bash
# Windows
npm run build:win

# macOS
npm run build:mac

# Linux
npm run build:linux
```

## 📱 Chạy Web Interface

### Development
```bash
cd src/web
npm start
```

### Build cho production
```bash
cd src/web
npm run build
```

### Serve static files
```bash
# Sử dụng serve
npm install -g serve
serve -s src/web/build -l 3000

# Hoặc nginx
sudo cp nginx.conf /etc/nginx/sites-available/instagram-live
sudo ln -s /etc/nginx/sites-available/instagram-live /etc/nginx/sites-enabled/
sudo nginx -s reload
```

## 🔧 Backend Server

### Development
```bash
npm run server
```

### Production với PM2
```bash
# Cài đặt PM2
npm install -g pm2

# Chạy với PM2
pm2 start ecosystem.config.js

# Xem logs
pm2 logs

# Restart
pm2 restart instagram-live-server

# Stop
pm2 stop instagram-live-server
```

### ecosystem.config.js
```javascript
module.exports = {
  apps: [{
    name: 'instagram-live-server',
    script: 'src/backend/server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    }
  }]
};
```

## 🐳 Docker Deployment

### Dockerfile
```dockerfile
FROM node:16-alpine

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY src/web/package*.json ./src/web/

# Install dependencies
RUN npm ci --only=production
RUN cd src/web && npm ci --only=production && npm run build

# Copy source code
COPY . .

# Create directories
RUN mkdir -p src/backend/data logs

EXPOSE 3001

CMD ["npm", "run", "server"]
```

### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=production
      - REDIS_HOST=redis
    volumes:
      - ./src/backend/data:/app/src/backend/data
      - ./logs:/app/logs
    depends_on:
      - redis

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - app
```

### Chạy với Docker
```bash
# Build và chạy
docker-compose up -d

# Xem logs
docker-compose logs -f

# Stop
docker-compose down
```

## 🔒 Bảo mật

### SSL/HTTPS
```bash
# Tạo self-signed certificate cho development
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes

# Production: sử dụng Let's Encrypt
sudo certbot --nginx -d yourdomain.com
```

### Firewall
```bash
# Ubuntu UFW
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 3001
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-port=3001/tcp
sudo firewall-cmd --reload
```

## 📊 Monitoring

### Logs
```bash
# Xem logs real-time
tail -f logs/combined.log

# Xem error logs
tail -f logs/error.log

# PM2 logs
pm2 logs instagram-live-server
```

### Health Check
```bash
# API health check
curl http://localhost:3001/api/health

# Response
{
  "status": "healthy",
  "timestamp": "2023-...",
  "uptime": 3600,
  "systemState": {...}
}
```

## 🐛 Troubleshooting

### Lỗi thường gặp

#### 1. Port đã được sử dụng
```bash
# Tìm process đang dùng port
lsof -i :3001

# Kill process
kill -9 <PID>
```

#### 2. Puppeteer không chạy được
```bash
# Ubuntu: Cài đặt dependencies
sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2 libcups2 libdbus-1-3 libexpat1 libfontconfig1 libgcc1 libgconf-2-4 libgdk-pixbuf2.0-0 libglib2.0-0 libgtk-3-0 libnspr4 libpango-1.0-0 libpangocairo-1.0-0 libstdc++6 libx11-6 libx11-xcb1 libxcb1 libxcomposite1 libxcursor1 libxdamage1 libxext6 libxfixes3 libxi6 libxrandr2 libxrender1 libxss1 libxtst6 ca-certificates fonts-liberation libappindicator1 libnss3 lsb-release xdg-utils wget

# Hoặc chạy headless
export PUPPETEER_HEADLESS=true
```

#### 3. Database locked
```bash
# Xóa file lock
rm src/backend/data/instagram_live.db-wal
rm src/backend/data/instagram_live.db-shm
```

#### 4. Memory issues
```bash
# Tăng memory limit cho Node.js
export NODE_OPTIONS="--max-old-space-size=4096"
```

### Debug mode
```bash
# Bật debug logs
export DEBUG=instagram-live:*
npm run server

# Hoặc trong .env
DEBUG=instagram-live:*
LOG_LEVEL=debug
```

## 📈 Performance Tuning

### Node.js optimization
```bash
# Cluster mode
export NODE_ENV=production
export UV_THREADPOOL_SIZE=16
```

### Database optimization
```sql
-- SQLite pragmas
PRAGMA journal_mode=WAL;
PRAGMA synchronous=NORMAL;
PRAGMA cache_size=10000;
PRAGMA temp_store=memory;
```

### Nginx optimization
```nginx
# nginx.conf
worker_processes auto;
worker_connections 1024;

gzip on;
gzip_types text/plain text/css application/json application/javascript;

client_max_body_size 10M;
```

## 🔄 Updates

### Cập nhật ứng dụng
```bash
# Pull latest changes
git pull origin main

# Update dependencies
npm install
cd src/web && npm install && cd ../..

# Rebuild web interface
cd src/web && npm run build && cd ../..

# Restart services
pm2 restart instagram-live-server
```

### Backup dữ liệu
```bash
# Backup database
cp src/backend/data/instagram_live.db backup/instagram_live_$(date +%Y%m%d).db

# Backup logs
tar -czf backup/logs_$(date +%Y%m%d).tar.gz logs/
```

## 📞 Support

Nếu gặp vấn đề trong quá trình cài đặt:

1. Kiểm tra [Issues](link-to-issues) trên GitHub
2. Xem [Wiki](link-to-wiki) để biết thêm chi tiết
3. Liên hệ support: <EMAIL>

---

**Chúc bạn cài đặt thành công! 🎉**
