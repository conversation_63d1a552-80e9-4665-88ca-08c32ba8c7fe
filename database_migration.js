#!/usr/bin/env node

/**
 * Database Migration Script
 * <PERSON>gra<PERSON> từ cấu trúc cũ sang API-based comment flow
 */

const Database = require('./src/backend/services/Database');
const MongoDBService = require('./src/backend/services/MongoDBService');

class DatabaseMigration {
  constructor() {
    this.database = null;
    this.mongoDBService = null;
  }

  async initialize() {
    console.log('🚀 Initializing migration...');
    
    // Initialize database
    this.database = new Database();
    await this.database.initialize();
    
    // Initialize MongoDB service
    this.mongoDBService = new MongoDBService();
    
    // Try to connect to MongoDB if connection string exists
    const connectionString = await this.database.getSetting('mongodb_connection_string');
    if (connectionString) {
      await this.mongoDBService.connect(connectionString);
      console.log('✅ MongoDB connected');
    } else {
      console.log('⚠️  MongoDB not configured - will skip MongoDB migration');
    }
  }

  async runMigration() {
    console.log('\n📋 Starting database migration...\n');

    try {
      // Phase 1: Backup current data
      await this.backupCurrentData();
      
      // Phase 2: Create new tables
      await this.createNewTables();
      
      // Phase 3: Clean up duplicate records
      await this.cleanupDuplicateRecords();
      
      // Phase 4: Migrate existing data
      await this.migrateExistingData();
      
      // Phase 5: Update table schemas
      await this.updateTableSchemas();
      
      // Phase 6: Clean up MongoDB
      if (this.mongoDBService.isConnected) {
        await this.cleanupMongoDB();
      }
      
      console.log('\n✅ Migration completed successfully!');
      
    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      throw error;
    }
  }

  async backupCurrentData() {
    console.log('📦 Phase 1: Backing up current data...');
    
    // Get current counts
    const printedHistoryCount = await this.database.getQuery(
      'SELECT COUNT(*) as count FROM printed_history'
    );
    
    console.log(`  - printed_history: ${printedHistoryCount.count} records`);
    
    // Create backup tables
    await this.database.runQuery(`
      CREATE TABLE IF NOT EXISTS printed_history_backup AS 
      SELECT * FROM printed_history
    `);
    
    console.log('✅ Backup completed');
  }

  async createNewTables() {
    console.log('🏗️  Phase 2: Creating new tables...');
    
    // Create comments table for session storage
    await this.database.runQuery(`
      CREATE TABLE IF NOT EXISTS comments (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        pk TEXT NOT NULL UNIQUE,
        username TEXT NOT NULL,
        comment_text TEXT NOT NULL,
        timestamp DATETIME NOT NULL,
        session_id TEXT NOT NULL,
        detected_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        is_processed BOOLEAN DEFAULT FALSE,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create sessions table
    await this.database.runQuery(`
      CREATE TABLE IF NOT EXISTS live_sessions (
        id TEXT PRIMARY KEY,
        started_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        ended_at DATETIME DEFAULT NULL,
        total_comments INTEGER DEFAULT 0,
        processed_comments INTEGER DEFAULT 0,
        printed_comments INTEGER DEFAULT 0,
        status TEXT DEFAULT 'active',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_comments_pk ON comments(pk)',
      'CREATE INDEX IF NOT EXISTS idx_comments_session ON comments(session_id)',
      'CREATE INDEX IF NOT EXISTS idx_comments_username ON comments(username)',
      'CREATE INDEX IF NOT EXISTS idx_comments_timestamp ON comments(timestamp)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_started_at ON live_sessions(started_at)',
      'CREATE INDEX IF NOT EXISTS idx_sessions_status ON live_sessions(status)'
    ];

    for (const index of indexes) {
      await this.database.runQuery(index);
    }

    console.log('✅ New tables created');
  }

  async cleanupDuplicateRecords() {
    console.log('🧹 Phase 3: Cleaning up duplicate records...');
    
    // Find duplicate records in printed_history
    const duplicates = await this.database.allQuery(`
      SELECT comment_id, username, COUNT(*) as count
      FROM printed_history 
      WHERE is_deleted = 0
      GROUP BY comment_id, username 
      HAVING COUNT(*) > 1
      ORDER BY count DESC
    `);

    console.log(`  - Found ${duplicates.length} groups of duplicate records`);

    let totalDuplicatesRemoved = 0;

    for (const dup of duplicates) {
      // Keep only the latest record for each comment_id + username
      const result = await this.database.runQuery(`
        DELETE FROM printed_history 
        WHERE id NOT IN (
          SELECT id FROM printed_history 
          WHERE comment_id = ? AND username = ? AND is_deleted = 0
          ORDER BY printed_at DESC, id DESC 
          LIMIT 1
        ) AND comment_id = ? AND username = ? AND is_deleted = 0
      `, [dup.comment_id, dup.username, dup.comment_id, dup.username]);

      totalDuplicatesRemoved += result.changes;
    }

    console.log(`✅ Removed ${totalDuplicatesRemoved} duplicate records`);
  }

  async migrateExistingData() {
    console.log('📊 Phase 4: Migrating existing data...');
    
    // Generate a default session for existing records
    const defaultSessionId = `migration_session_${Date.now()}`;
    
    // Create default session
    await this.database.runQuery(`
      INSERT INTO live_sessions (id, started_at, status, total_comments, processed_comments, printed_comments)
      SELECT ?, 
             MIN(printed_at) as started_at,
             'archived' as status,
             COUNT(DISTINCT comment_id) as total_comments,
             COUNT(DISTINCT comment_id) as processed_comments,
             COUNT(*) as printed_comments
      FROM printed_history 
      WHERE is_deleted = 0
    `, [defaultSessionId]);

    // Update existing printed_history records with session_id
    const updateResult = await this.database.runQuery(`
      UPDATE printed_history 
      SET session_id = ? 
      WHERE session_id IS NULL
    `, [defaultSessionId]);

    console.log(`✅ Updated ${updateResult.changes} records with session_id`);
  }

  async updateTableSchemas() {
    console.log('🔧 Phase 5: Updating table schemas...');
    
    // Add new columns to printed_history if they don't exist
    const tableInfo = await this.database.allQuery("PRAGMA table_info(printed_history)");
    const hasCommentPk = tableInfo.some(col => col.name === 'comment_pk');
    const hasSessionId = tableInfo.some(col => col.name === 'session_id');

    if (!hasCommentPk) {
      await this.database.runQuery('ALTER TABLE printed_history ADD COLUMN comment_pk TEXT');
      console.log('  - Added comment_pk column to printed_history');
    }

    if (!hasSessionId) {
      await this.database.runQuery('ALTER TABLE printed_history ADD COLUMN session_id TEXT');
      console.log('  - Added session_id column to printed_history');
    }

    // Update comment_pk with comment_id for now (will be replaced with real pk later)
    await this.database.runQuery(`
      UPDATE printed_history 
      SET comment_pk = comment_id 
      WHERE comment_pk IS NULL
    `);

    // Add new columns to message_queue if they don't exist
    const queueTableInfo = await this.database.allQuery("PRAGMA table_info(message_queue)");
    const queueHasCommentPk = queueTableInfo.some(col => col.name === 'comment_pk');
    const queueHasSessionId = queueTableInfo.some(col => col.name === 'session_id');

    if (!queueHasCommentPk) {
      await this.database.runQuery('ALTER TABLE message_queue ADD COLUMN comment_pk TEXT');
      console.log('  - Added comment_pk column to message_queue');
    }

    if (!queueHasSessionId) {
      await this.database.runQuery('ALTER TABLE message_queue ADD COLUMN session_id TEXT');
      console.log('  - Added session_id column to message_queue');
    }

    // Update comment_pk in message_queue
    await this.database.runQuery(`
      UPDATE message_queue 
      SET comment_pk = comment_id 
      WHERE comment_pk IS NULL
    `);

    console.log('✅ Table schemas updated');
  }

  async cleanupMongoDB() {
    console.log('🧹 Phase 6: Cleaning up MongoDB...');
    
    try {
      // Get current MongoDB counts
      const printedHistoryCollection = this.mongoDBService.db.collection('printed_history');
      const mongoCount = await printedHistoryCollection.countDocuments();
      
      console.log(`  - MongoDB printed_history: ${mongoCount} records`);
      
      // Clear MongoDB collections to force fresh sync
      await printedHistoryCollection.deleteMany({});
      console.log('  - Cleared MongoDB printed_history collection');
      
      // Reset sync timestamps to force re-sync
      await this.database.runQuery(`
        UPDATE printed_history 
        SET synced_at = NULL
      `);
      
      console.log('✅ MongoDB cleanup completed');
      
    } catch (error) {
      console.error('⚠️  MongoDB cleanup failed:', error.message);
    }
  }

  async generateMigrationReport() {
    console.log('\n📊 Migration Report:');
    
    // Get final counts
    const printedHistoryCount = await this.database.getQuery(
      'SELECT COUNT(*) as count FROM printed_history WHERE is_deleted = 0'
    );
    
    const sessionsCount = await this.database.getQuery(
      'SELECT COUNT(*) as count FROM live_sessions'
    );
    
    const commentsCount = await this.database.getQuery(
      'SELECT COUNT(*) as count FROM comments'
    );

    console.log(`  - printed_history (active): ${printedHistoryCount.count} records`);
    console.log(`  - live_sessions: ${sessionsCount.count} records`);
    console.log(`  - comments: ${commentsCount.count} records`);
    
    if (this.mongoDBService.isConnected) {
      console.log('  - MongoDB: Ready for fresh sync');
    }
  }
}

// Run migration
async function runMigration() {
  const migration = new DatabaseMigration();
  
  try {
    await migration.initialize();
    await migration.runMigration();
    await migration.generateMigrationReport();
    
    console.log('\n🎉 Database migration completed successfully!');
    console.log('\nNext steps:');
    console.log('1. Update Database.js methods for new schema');
    console.log('2. Update API comment detection to use comments table');
    console.log('3. Update printing logic to use comment_pk');
    console.log('4. Test complete flow and MongoDB sync');
    
  } catch (error) {
    console.error('\n💥 Migration failed:', error);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// Run if called directly
if (require.main === module) {
  runMigration();
}

module.exports = DatabaseMigration;
