const winston = require('winston');
const { exec } = require('child_process');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.simple(),
  transports: [new winston.transports.Console()]
});

class ChromeCleanupManager {
  constructor() {
    this.cleanupInterval = null;
    this.isRunning = false;
    this.cleanupIntervalMs = 10 * 60 * 1000; // 10 minutes
    this.maxChromeProcesses = 15; // Threshold for cleanup
    this.scraperService = null; // Reference to scraper service
    this.messengerService = null; // Reference to messenger service
    this.isCleanupInProgress = false; // Prevent concurrent cleanups
    this.cleanupQueue = []; // Queue cleanup requests
    this.lastCleanupTime = 0; // Track last cleanup time
    this.minCleanupInterval = 30000; // Minimum 30 seconds between cleanups
  }

  // Set service references to check their status
  setServices(scraperService, messengerService) {
    this.scraperService = scraperService;
    this.messengerService = messengerService;
  }

  start() {
    if (this.isRunning) {
      logger.warn('Chrome cleanup manager is already running');
      return;
    }

    this.isRunning = true;
    logger.info('🧹 Starting Chrome cleanup manager...');

    // Initial cleanup
    this.performCleanup();

    // Schedule periodic cleanup
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, this.cleanupIntervalMs);

    logger.info(`Chrome cleanup scheduled every ${this.cleanupIntervalMs / 60000} minutes`);
  }

  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }
    logger.info('Chrome cleanup manager stopped');
  }

  async performCleanup() {
    // Prevent concurrent cleanups
    if (this.isCleanupInProgress) {
      logger.warn('⏳ Cleanup already in progress, skipping...');
      return;
    }

    // Rate limiting - prevent too frequent cleanups
    const now = Date.now();
    if (now - this.lastCleanupTime < this.minCleanupInterval) {
      logger.warn('⏳ Cleanup rate limited, skipping...');
      return;
    }

    this.isCleanupInProgress = true;
    this.lastCleanupTime = now;

    try {
      const processCount = await this.getChromeProcessCount();

      if (processCount > this.maxChromeProcesses) {
        logger.warn(`🚨 High Chrome process count detected: ${processCount} (threshold: ${this.maxChromeProcesses})`);
        await this.forceKillOrphanedChromeProcesses();
      } else {
        logger.info(`✅ Chrome process count is normal: ${processCount}`);
      }
    } catch (error) {
      logger.error('Error during Chrome cleanup:', error);
    } finally {
      this.isCleanupInProgress = false;
    }
  }

  async getChromeProcessCount() {
    try {
      // Use the same method as getDetailedChromeInfo for consistency
      const detailedInfo = await this.getDetailedChromeInfo();
      return detailedInfo.totalProcesses;
    } catch (error) {
      logger.error('Error getting Chrome process count:', error);
      return 0;
    }
  }

  async forceKillOrphanedChromeProcesses() {
    try {
      logger.info('🔪 Smart cleanup: Killing only messenger Chrome processes (preserving scraper)...');

      // Check if scraper is running
      const scraperRunning = this.scraperService && this.scraperService.isRunning;
      const messengerRunning = this.messengerService && this.messengerService.isRunning;

      if (scraperRunning) {
        logger.info('⚠️ Scraper is running - using SAFE cleanup mode to preserve scraper');
      }

      if (process.platform === 'win32') {
        // Execute cleanup commands SEQUENTIALLY to prevent PowerShell process explosion
        // Focus on messenger-specific patterns while preserving scraper

        logger.info('🔧 Step 1: Killing Chrome automation/testing processes (messenger)...');
        await this.executeCleanupCommand('wmic process where "name like \'%chrome%\' and (commandline like \'%test-type%\' or commandline like \'%automation%\')" delete', 'Chrome automation/testing processes');

        // Only kill high-memory processes if scraper is not running, or use higher threshold
        const memoryThreshold = scraperRunning ? 1200000000 : 800000000; // 1.2GB if scraper running, 800MB otherwise
        logger.info(`🔧 Step 2: Killing high-memory Chrome processes (>${Math.round(memoryThreshold/1000000000*10)/10}GB)...`);
        await this.executeCleanupCommand(`wmic process where "name='chrome.exe' and WorkingSetSize>${memoryThreshold}" delete`, `high-memory Chrome processes (>${Math.round(memoryThreshold/1000000000*10)/10}GB)`);

        // Kill renderer processes with messenger-specific patterns
        logger.info('🔧 Step 3: Killing messenger renderer processes...');
        await this.executeCleanupCommand('wmic process where "name=\'chrome.exe\' and commandline like \'%--type=renderer%\' and (commandline like \'%automation%\' or WorkingSetSize>600000000)" delete', 'messenger renderer processes');

        logger.info('✅ Safe Chrome processes cleanup completed - scraper preserved');
      } else {
        // Linux/Mac - only kill Chrome processes with specific automation patterns
        logger.info('🔧 Killing Chrome automation processes on Unix...');
        await this.executeCleanupCommand('pkill -f "chrome.*--test-type"', 'Chrome testing processes');
        await this.executeCleanupCommand('pkill -f "chrome.*automation"', 'Chrome automation processes');
      }
    } catch (error) {
      logger.error('Failed to kill orphaned Chrome processes:', error);
    }
  }

  // Helper method to execute cleanup commands sequentially with proper error handling
  async executeCleanupCommand(command, description) {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        logger.warn(`⏰ Cleanup command timeout for ${description}`);
        resolve();
      }, 15000); // 15 second timeout

      exec(command, (error, stdout, stderr) => {
        clearTimeout(timeout);

        if (error && !error.message.includes('not found') && !error.message.includes('No Instance(s) Available')) {
          logger.warn(`Error killing ${description}:`, error.message);
        } else {
          logger.info(`✅ Killed ${description}`);
        }
        resolve();
      });
    });
  }

  async getDetailedChromeInfo() {
    try {
      const processes = [];

      if (process.platform === 'win32') {
        await new Promise((resolve) => {
          // Use a more reliable command to get Chrome processes
          exec('wmic process where "name=\'chrome.exe\'" get ProcessId,WorkingSetSize,CommandLine /format:csv', (error, stdout) => {
            if (!error && stdout) {
              const lines = stdout.split('\n').filter(line => line.trim() && !line.startsWith('Node') && line.includes('chrome.exe'));

              lines.forEach(line => {
                try {
                  // More robust CSV parsing
                  const parts = line.split(',');
                  if (parts.length >= 4 && parts[2] && parts[3]) {
                    const pid = parts[2].trim();
                    const memory = parseInt(parts[3].trim()) || 0;
                    const commandLine = parts[1] ? parts[1].trim() : '';

                    // Only add if we have valid data
                    if (pid && !isNaN(parseInt(pid))) {
                      processes.push({
                        pid: pid,
                        memory: memory,
                        commandLine: commandLine
                      });
                    }
                  }
                } catch (parseError) {
                  // Skip malformed lines
                  logger.debug('Skipped malformed Chrome process line:', line);
                }
              });
            }
            resolve();
          });
        });
      } else {
        // Linux/Mac fallback
        await new Promise((resolve) => {
          exec('ps aux | grep chrome | grep -v grep', (error, stdout) => {
            if (!error && stdout) {
              const lines = stdout.split('\n').filter(line => line.trim());
              lines.forEach(line => {
                const parts = line.split(/\s+/);
                if (parts.length >= 11) {
                  processes.push({
                    pid: parts[1],
                    memory: parseInt(parts[5]) * 1024 || 0, // Convert KB to bytes
                    commandLine: parts.slice(10).join(' ')
                  });
                }
              });
            }
            resolve();
          });
        });
      }

      // Filter testing processes (Chrome for Testing)
      const testingProcesses = processes.filter(p =>
        p.commandLine.includes('--test-type') ||
        p.commandLine.includes('--automation') ||
        p.commandLine.includes('Chrome for Testing')
      );

      // Calculate high memory processes (only among testing processes)
      const highMemoryTestingProcesses = testingProcesses.filter(p => p.memory > 900000000); // > 900MB

      const result = {
        totalProcesses: processes.length,
        totalMemory: Math.round(processes.reduce((sum, p) => sum + p.memory, 0) / (1024 * 1024)), // Convert to MB
        testingProcesses: testingProcesses.length,
        highMemoryProcesses: highMemoryTestingProcesses.length
      };

      logger.debug(`Chrome process info: ${result.totalProcesses} total, ${result.testingProcesses} testing, ${result.highMemoryProcesses} high memory`);

      return result;
    } catch (error) {
      logger.error('Error getting detailed Chrome info:', error);
      return {
        totalProcesses: 0,
        totalMemory: 0,
        testingProcesses: 0,
        highMemoryProcesses: 0
      };
    }
  }

  getStatus() {
    return {
      isRunning: this.isRunning,
      cleanupIntervalMs: this.cleanupIntervalMs,
      maxChromeProcesses: this.maxChromeProcesses,
      nextCleanup: this.cleanupInterval ? new Date(Date.now() + this.cleanupIntervalMs) : null
    };
  }

  // Manual cleanup method for API endpoint
  async manualCleanup(forceFull = false) {
    // Prevent concurrent manual cleanups
    if (this.isCleanupInProgress) {
      logger.warn('⏳ Cleanup already in progress, please wait...');
      return {
        error: 'Cleanup already in progress',
        processCountAfterCleanup: await this.getChromeProcessCount(),
        cleanupTime: new Date().toISOString()
      };
    }

    this.isCleanupInProgress = true;

    try {
      const scraperRunning = this.scraperService && this.scraperService.isRunning;
      const messengerRunning = this.messengerService && this.messengerService.isRunning;

      if (forceFull) {
        logger.warn('🚨 FORCE FULL cleanup requested - this will affect ALL Chrome processes including scraper!');
        await this.emergencyFullCleanup();
      } else {
        logger.info('🔧 Manual safe Chrome cleanup requested');
        if (scraperRunning) {
          logger.warn('⚠️ Scraper is running - using SAFE cleanup mode to protect scraper');
        }
        await this.forceKillOrphanedChromeProcesses();
      }

      const processCount = await this.getChromeProcessCount();
      const detailedInfo = await this.getDetailedChromeInfo();

      return {
        processCountAfterCleanup: processCount,
        detailedInfo: detailedInfo,
        cleanupTime: new Date().toISOString(),
        scraperProtected: scraperRunning && !forceFull,
        messengerRunning: messengerRunning
      };
    } finally {
      this.isCleanupInProgress = false;
    }
  }

  // Emergency full cleanup - kills ALL Chrome processes (use with caution)
  async emergencyFullCleanup() {
    logger.warn('🚨 EMERGENCY FULL CLEANUP - Killing ALL Chrome processes!');

    if (process.platform === 'win32') {
      await new Promise((resolve) => {
        exec('taskkill /F /IM chrome.exe /T', (error) => {
          if (error && !error.message.includes('not found')) {
            logger.warn('Error killing all Chrome processes:', error.message);
          }
          resolve();
        });
      });
    } else {
      await new Promise((resolve) => {
        exec('pkill -f chrome', (error) => {
          if (error && error.code !== 1) {
            logger.warn('Error killing all Chrome processes:', error.message);
          }
          resolve();
        });
      });
    }

    logger.warn('🚨 Emergency cleanup completed - ALL Chrome processes killed');
  }
}

// Singleton instance
const chromeCleanupManager = new ChromeCleanupManager();

module.exports = chromeCleanupManager;
