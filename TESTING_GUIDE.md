# Hướng dẫn Test Instagram Live Comment System

## Tổng quan về các cải tiến

Đã cập nhật Instagram scraper với các tối ưu hóa từ Instagram scraper example:

### 1. **Tối ưu hóa Comment Detection**
- Thêm method `detectNewComments()` để phát hiện comment mới nhanh hơn
- Sử dụng class `x17y8kql` làm selector chính cho Instagram live comments
- Fallback selectors cho các trường hợp khác nhau
- Tối ưu hóa thứ tự ưu tiên của selectors

### 2. **Cải thiện Comment Extraction**
- Sử dụng selectors chính xác cho username: `span._ap3a._aaco._aacw._aacx._aad7[dir="auto"]`
- Sử dụng selectors chính xác cho comment text: `span._ap3a._aaco._aacu._aacx._aad7._aadf[dir="auto"]`
- Fallback logic thông minh khi selectors chính không hoạt động
- Loại bỏ debug logs dư thừa để tăng performance

### 3. **Tối ưu hóa Monitoring**
- Interval 500ms cho detection nhanh
- Sử dụng `detectNewComments()` làm method chính
- Fallback về `extractComments()` mỗi 10 cycles (5 giây)
- Quản lý memory tốt hơn với cleanup tự động

### 4. **Web Interface Improvements**
- Thêm `ScrapingControl` component để điều khiển scraper
- Tích hợp vào Settings page
- Real-time status và statistics
- API endpoints để start/stop scraping

## Cách Test Hệ Thống

### Bước 1: Khởi động Backend
```bash
cd src/backend
npm install
npm start
```

Backend sẽ chạy trên port 3001.

### Bước 2: Khởi động Web Interface
```bash
cd src/web
npm install
npm start
```

Web interface sẽ chạy trên port 3000.

### Bước 3: Test Connection
1. Mở browser và truy cập `http://localhost:3000`
2. Kiểm tra connection status ở góc trên (màu xanh = connected)
3. Vào Settings page để thấy ScrapingControl component

### Bước 4: Test Instagram Scraping

#### Chuẩn bị:
1. Tìm một Instagram account đang live stream
2. Chuẩn bị thông tin đăng nhập Instagram (khuyến nghị dùng account phụ)

#### Test Steps:
1. **Vào Settings page**
2. **Nhập thông tin:**
   - Tên người dùng Instagram Live (ví dụ: `username` nếu URL là `instagram.com/username/live`)
   - Tên đăng nhập Instagram của bạn
   - Mật khẩu Instagram của bạn

3. **Bấm "Bắt đầu theo dõi"**
   - Hệ thống sẽ mở browser (headless=false để debug)
   - Đăng nhập vào Instagram
   - Điều hướng đến live stream
   - Bắt đầu monitor comments

4. **Kiểm tra Comments page**
   - Chuyển sang Comments page
   - Quan sát comments xuất hiện real-time
   - Kiểm tra format: username, content, timestamp

### Bước 5: Kiểm tra Logs

#### Backend Logs:
- Mở terminal chạy backend
- Quan sát logs về comment detection
- Kiểm tra các selector được sử dụng
- Xem thống kê extraction

#### Browser Console (nếu headless=false):
- Mở DevTools trong browser được mở bởi Puppeteer
- Kiểm tra console logs về comment detection
- Xem cấu trúc HTML của comments

### Bước 6: Test Error Handling

#### Test các trường hợp lỗi:
1. **Username không tồn tại**
2. **User không đang live**
3. **Thông tin đăng nhập sai**
4. **Mất kết nối internet**
5. **Instagram thay đổi cấu trúc**

## Debugging Tips

### 1. Kiểm tra Comment Structure
Nếu không lấy được comments, sử dụng method debug:
```javascript
await instagramScraper.debugCommentDetection();
```

### 2. Kiểm tra Selectors
Trong browser console:
```javascript
// Kiểm tra elements với class x17y8kql
document.querySelectorAll('div.x17y8kql').length

// Kiểm tra spans với dir="auto"
document.querySelectorAll('span[dir="auto"]').length

// Kiểm tra cấu trúc comment cụ thể
document.querySelector('div.x17y8kql').innerHTML
```

### 3. Performance Monitoring
- Theo dõi memory usage
- Kiểm tra comment processing speed
- Monitor network requests

## Các Vấn đề Thường Gặp

### 1. Không lấy được comments
- **Nguyên nhân:** Instagram thay đổi selectors
- **Giải pháp:** Cập nhật selectors trong `commentSelectors`

### 2. Comments bị duplicate
- **Nguyên nhân:** ID generation không unique
- **Giải pháp:** Đã implement duplicate checking với Set

### 3. Performance chậm
- **Nguyên nhân:** Too many DOM queries
- **Giải pháp:** Đã tối ưu với fast detection method

### 4. Memory leak
- **Nguyên nhân:** Processed comments tích lũy
- **Giải pháp:** Auto cleanup khi > 1000 comments

## Kết quả Mong đợi

Sau khi test thành công:
1. ✅ Comments xuất hiện real-time trên web interface
2. ✅ Username và content được extract chính xác
3. ✅ Không có duplicate comments
4. ✅ Performance ổn định (500ms interval)
5. ✅ Error handling hoạt động tốt
6. ✅ Memory usage được kiểm soát

## Lưu ý Bảo mật

1. **Không commit credentials** vào code
2. **Sử dụng account phụ** để test
3. **Không lưu trữ password** trong localStorage
4. **Monitor rate limiting** từ Instagram
5. **Respect Instagram's Terms of Service**
