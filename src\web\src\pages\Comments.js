import React, { useState, useEffect, useRef } from 'react';
import {
  MessageCircle,
  Trash2,
  Play,
  Loader,
  AlertCircle,
  LogIn
} from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { useApp } from '../contexts/AppContext';
import CommentItem from '../components/CommentItem';
import LoginPopup from '../components/LoginPopup';
import { getApiUrl } from '../config/api';

// moment import moved to CommentItem component
import toast from 'react-hot-toast';

const Comments = () => {
  const { recentComments, printComment, isConnected, clearSessionComments, socket, systemState } = useSocket();
  const { actions } = useApp();

  const [regularCustomers, setRegularCustomers] = useState(new Set());

  // Quick start states
  const [quickStartData, setQuickStartData] = useState({
    liveUsername: '',
    scraperCookies: false,
    messengerCookies: false,
    scraperRunning: false,
    messengerRunning: false,
    isStartingScraper: false,
    isStartingMessenger: false
  });

  // Login popup states
  const [showScraperLoginPopup, setShowScraperLoginPopup] = useState(false);
  const [showMessengerLoginPopup, setShowMessengerLoginPopup] = useState(false);
  const [userInteracting, setUserInteracting] = useState(false);
  const [isTouching, setIsTouching] = useState(false);
  const [isHolding, setIsHolding] = useState(false);
  const [isScrolling, setIsScrolling] = useState(false);
  const [colorSettings, setColorSettings] = useState({
    newCustomer: {
      background: '#f3f4f6',
      border: '#d1d5db',
      username: '#7c3aed',
      text: '#374151'
    },
    regularCustomer: {
      background: '#fef3c7',
      border: '#f59e0b',
      username: '#d97706',
      text: '#374151'
    }
  });
  const commentsEndRef = useRef(null);
  const interactionTimeoutRef = useRef(null);
  const lastTouchEndRef = useRef(null);
  const touchSequenceCountRef = useRef(0);
  const holdTimeoutRef = useRef(null);
  const scrollTimeoutRef = useRef(null);
  const lastScrollTimeRef = useRef(null);

  // Detect if device is mobile
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

  // Debug: Log recentComments changes
  useEffect(() => {
    console.log('=== COMMENTS PAGE: recentComments updated ===');
    console.log('Comments count:', recentComments.length);
    console.log('Comments:', recentComments);
  }, [recentComments]);

  useEffect(() => {
    actions.setCurrentPage('comments');
    loadRegularCustomers();
    checkCookieStatus();
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Refresh cookie status and service status when system state changes
  useEffect(() => {
    checkCookieStatus();
    checkServiceStatus();
  }, [systemState?.isRunning, systemState?.messengerConnected]);

  // Update service running status from system state
  useEffect(() => {
    setQuickStartData(prev => ({
      ...prev,
      scraperRunning: systemState?.isRunning || false,
      messengerRunning: systemState?.messengerConnected || false
    }));
  }, [systemState?.isRunning, systemState?.messengerConnected]);

  // Load color settings from localStorage on mount
  useEffect(() => {
    const savedColors = localStorage.getItem('instagram-live-color-settings');
    if (savedColors) {
      try {
        const parsedColors = JSON.parse(savedColors);
        setColorSettings(parsedColors);
      } catch (error) {
        console.error('Failed to parse saved color settings:', error);
      }
    }

    // Listen for color settings changes
    const handleColorSettingsChange = (event) => {
      setColorSettings(event.detail);
    };

    window.addEventListener('colorSettingsChanged', handleColorSettingsChange);
    return () => {
      window.removeEventListener('colorSettingsChanged', handleColorSettingsChange);
    };
  }, []);



  // Socket listeners for regular customers sync
  useEffect(() => {
    if (!socket) return;

    const handleRegularCustomerAdded = (data) => {
      setRegularCustomers(prev => {
        const newSet = new Set(prev);
        newSet.add(data.username);
        return newSet;
      });
    };

    const handleRegularCustomerRemoved = (data) => {
      setRegularCustomers(prev => {
        const newSet = new Set(prev);
        newSet.delete(data.username);
        return newSet;
      });
    };

    socket.on('regular-customer-added', handleRegularCustomerAdded);
    socket.on('regular-customer-removed', handleRegularCustomerRemoved);

    return () => {
      socket.off('regular-customer-added', handleRegularCustomerAdded);
      socket.off('regular-customer-removed', handleRegularCustomerRemoved);
    };
  }, [socket]);

  // Load regular customers from API
  const loadRegularCustomers = async () => {
    try {
      const response = await fetch(getApiUrl('/api/regular-customers'));
      const data = await response.json();

      if (data.success) {
        const customerSet = new Set(data.customers.map(c => c.username));
        setRegularCustomers(customerSet);
      }
    } catch (error) {
      console.error('Failed to load regular customers:', error);
    }
  };

  // Check cookie status for quick start
  const checkCookieStatus = async () => {
    try {
      // Check scraper cookies
      const scraperResponse = await fetch(getApiUrl('/api/saved-cookies'));
      const scraperData = await scraperResponse.json();

      // Check messenger cookies
      const messengerResponse = await fetch(getApiUrl('/api/messenger-saved-cookies'));
      const messengerData = await messengerResponse.json();

      setQuickStartData(prev => ({
        ...prev,
        scraperCookies: scraperData.success && scraperData.hasCookies,
        messengerCookies: messengerData.success && messengerData.hasCookies
      }));
    } catch (error) {
      console.error('Failed to check cookie status:', error);
    }
  };

  // Check service running status
  const checkServiceStatus = async () => {
    try {
      // Check scraper status
      const scraperResponse = await fetch(getApiUrl('/api/scraper-status'));
      const scraperData = await scraperResponse.json();

      // Check messenger status
      const messengerResponse = await fetch(getApiUrl('/api/messenger-status'));
      const messengerData = await messengerResponse.json();

      setQuickStartData(prev => ({
        ...prev,
        scraperRunning: scraperData.success && scraperData.isRunning,
        messengerRunning: messengerData.success && messengerData.isRunning
      }));
    } catch (error) {
      console.error('Failed to check service status:', error);
    }
  };

  // Quick start scraper
  const handleQuickStartScraper = async () => {
    if (!quickStartData.liveUsername.trim()) {
      toast.error('Vui lòng nhập tên Instagram Live');
      return;
    }

    setQuickStartData(prev => ({ ...prev, isStartingScraper: true }));

    try {
      const response = await fetch(getApiUrl('/api/start-scraping'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          liveUsername: quickStartData.liveUsername.trim(),
          useSavedCookies: true
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Đã khởi động thu thập bình luận!');
      } else {
        throw new Error(data.error || 'Failed to start scraper');
      }
    } catch (error) {
      console.error('Failed to start scraper:', error);
      toast.error(`Lỗi khởi động thu thập bình luận: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingScraper: false }));
    }
  };

  // Quick start messenger
  const handleQuickStartMessenger = async () => {
    setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));

    try {
      const response = await fetch(getApiUrl('/api/start-messenger'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          useSavedCookies: true
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Đã khởi động tin nhắn tự động!');
      } else {
        throw new Error(data.error || 'Failed to start messenger');
      }
    } catch (error) {
      console.error('Failed to start messenger:', error);
      toast.error(`Lỗi khởi động messenger: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
    }
  };

  // Handle scraper login/start from popup
  const handleScraperPopupLogin = async (loginCredentials) => {
    // If cookies exist but service not running, just start with saved cookies
    if (quickStartData.scraperCookies && !quickStartData.scraperRunning) {
      if (!quickStartData.liveUsername.trim()) {
        toast.error('Vui lòng nhập tên Instagram Live');
        return;
      }

      setQuickStartData(prev => ({ ...prev, isStartingScraper: true }));
      try {
        const response = await fetch(getApiUrl('/api/start-scraping'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            liveUsername: quickStartData.liveUsername.trim(),
            useSavedCookies: true
          })
        });

        const data = await response.json();
        if (data.success) {
          toast.success('Đã khởi động thu thập bình luận với phiên đăng nhập đã lưu');
          setShowScraperLoginPopup(false);
          await checkServiceStatus();
        } else {
          throw new Error(data.error || 'Failed to start scraper');
        }
      } catch (error) {
        console.error('Failed to start scraper:', error);
        toast.error(`Lỗi khởi động thu thập bình luận: ${error.message}`);
      } finally {
        setQuickStartData(prev => ({ ...prev, isStartingScraper: false }));
      }
      return;
    }

    // If no cookies, need to login first
    if (!quickStartData.liveUsername.trim()) {
      toast.error('Vui lòng nhập tên Instagram Live');
      return;
    }

    setQuickStartData(prev => ({ ...prev, isStartingScraper: true }));
    try {
      const response = await fetch(getApiUrl('/api/start-scraping'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          liveUsername: quickStartData.liveUsername.trim(),
          credentials: {
            username: loginCredentials.username.trim(),
            password: loginCredentials.password.trim()
          },
          useSavedCookies: false
        })
      });

      const data = await response.json();
      if (data.success) {
        toast.success('Đã bắt đầu thu thập bình luận - Phiên đăng nhập đã được lưu');
        setShowScraperLoginPopup(false);
        // Reload cookie and service status
        await checkCookieStatus();
        await checkServiceStatus();
      } else {
        throw new Error(data.error || 'Failed to start scraper');
      }
    } catch (error) {
      console.error('Failed to start scraper:', error);
      toast.error(`Lỗi khởi động thu thập bình luận: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingScraper: false }));
    }
  };

  // Handle messenger login/start from popup
  const handleMessengerPopupLogin = async (loginCredentials) => {
    // If cookies exist but service not running, just start with saved cookies
    if (quickStartData.messengerCookies && !quickStartData.messengerRunning) {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));
      try {
        const response = await fetch(getApiUrl('/api/start-messenger'), {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            useSavedCookies: true
          })
        });

        const data = await response.json();
        if (data.success) {
          toast.success('Đã khởi động tin nhắn tự động với phiên đăng nhập đã lưu');
          setShowMessengerLoginPopup(false);
          await checkServiceStatus();
        } else {
          throw new Error(data.error || 'Failed to start messenger');
        }
      } catch (error) {
        console.error('Failed to start messenger:', error);
        toast.error(`Lỗi khởi động tin nhắn tự động: ${error.message}`);
      } finally {
        setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
      }
      return;
    }

    // If no cookies, need to login first
    setQuickStartData(prev => ({ ...prev, isStartingMessenger: true }));
    try {
      const response = await fetch(getApiUrl('/api/start-messenger'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          credentials: {
            username: loginCredentials.username.trim(),
            password: loginCredentials.password.trim()
          },
          useSavedCookies: false
        })
      });

      const data = await response.json();
      if (data.pending2FA) {
        toast.error('Cần xác thực 2FA! Vui lòng nhập mã xác thực trong cửa sổ trình duyệt.', {
          duration: 10000,
          icon: '🔐'
        });
        setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
        return;
      }

      if (data.success) {
        toast.success('Đã bắt đầu Instagram Messenger - Phiên đăng nhập đã được lưu');
        setShowMessengerLoginPopup(false);
        // Reload cookie and service status
        await checkCookieStatus();
        await checkServiceStatus();
      } else {
        throw new Error(data.error || 'Failed to start messenger');
      }
    } catch (error) {
      console.error('Failed to start messenger:', error);
      toast.error(`Lỗi khởi động tin nhắn tự động: ${error.message}`);
    } finally {
      setQuickStartData(prev => ({ ...prev, isStartingMessenger: false }));
    }
  };

  // Auto scroll to bottom when new comments arrive
  useEffect(() => {
    const shouldAutoScroll = recentComments.length >= 7 && !userInteracting && !isTouching && !isScrolling;

    // On mobile, also check isHolding state
    const mobileHoldCheck = isMobile ? !isHolding : true;

    if (shouldAutoScroll && mobileHoldCheck) {
      scrollToBottom();
    }
  }, [recentComments, userInteracting, isTouching, isHolding, isScrolling, isMobile]);

  // Handle scroll events (including momentum scrolling)
  const handleScroll = () => {
    setIsScrolling(true);
    setUserInteracting(true);
    lastScrollTimeRef.current = Date.now();

    // Clear existing timeouts
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
    }
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Detect when scrolling stops (including momentum)
    scrollTimeoutRef.current = setTimeout(() => {
      setIsScrolling(false);

      // Start main timeout after scrolling completely stops
      interactionTimeoutRef.current = setTimeout(() => {
        setUserInteracting(false);
      }, isMobile ? 3500 : 2000);
    }, 150); // 150ms to detect scroll stop
  };

  // Handle user interaction detection (for non-scroll events)
  const handleUserInteraction = () => {
    setUserInteracting(true);

    // Clear existing timeout
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
    }

    // Set timeout to resume auto scroll
    interactionTimeoutRef.current = setTimeout(() => {
      setUserInteracting(false);
    }, 2000);
  };

  // Handle touch events
  const handleTouchStart = () => {
    setIsTouching(true);
    setUserInteracting(true);

    // Clear existing timeouts
    if (interactionTimeoutRef.current) {
      clearTimeout(interactionTimeoutRef.current);
    }
    if (holdTimeoutRef.current) {
      clearTimeout(holdTimeoutRef.current);
    }

    // Only set hold detection on mobile devices
    if (isMobile) {
      holdTimeoutRef.current = setTimeout(() => {
        setIsHolding(true);
      }, 500);
    }
  };

  const handleTouchEnd = () => {
    setIsTouching(false);

    // Clear hold timeout
    if (holdTimeoutRef.current) {
      clearTimeout(holdTimeoutRef.current);
    }

    if (isMobile) {
      setIsHolding(false);
    }

    // On mobile, don't start timeout immediately after touchEnd
    // Let the scroll handler detect when momentum scrolling stops
    if (!isMobile) {
      const now = Date.now();
      const timeSinceLastTouch = lastTouchEndRef.current ? now - lastTouchEndRef.current : Infinity;

      // Desktop logic remains the same
      if (timeSinceLastTouch < 3000) {
        touchSequenceCountRef.current += 1;
      } else {
        touchSequenceCountRef.current = 1;
      }

      lastTouchEndRef.current = now;

      let timeoutDuration = 2000;
      if (touchSequenceCountRef.current > 1) {
        timeoutDuration = 5000;
      }

      interactionTimeoutRef.current = setTimeout(() => {
        setUserInteracting(false);
        touchSequenceCountRef.current = 0;
      }, timeoutDuration);
    }
    // Mobile timeout is handled by scroll handler to account for momentum
  };

  const scrollToBottom = () => {
    commentsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };



  const toggleRegularCustomer = async (username) => {
    try {
      const isCurrentlyRegular = regularCustomers.has(username);

      if (isCurrentlyRegular) {
        // Remove from regular customers
        const response = await fetch(getApiUrl(`/api/regular-customers/${username}`), {
          method: 'DELETE'
        });

        if (response.ok) {
          // Local update will be handled by socket event
          console.log(`Đã bỏ đánh dấu khách quen: @${username}`);

          // Emit socket event to sync with other clients
          if (socket) {
            socket.emit('regular-customer-removed', { username });
          }
        }
      } else {
        // Add to regular customers
        const response = await fetch(getApiUrl('/api/regular-customers'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ username })
        });

        if (response.ok) {
          // Local update will be handled by socket event
          console.log(`Đã đánh dấu khách quen: @${username}`);

          // Emit socket event to sync with other clients
          if (socket) {
            socket.emit('regular-customer-added', { username });
          }
        }
      }
    } catch (error) {
      console.error('Failed to toggle regular customer:', error);
    }
  };

  // REMOVED: handleBackupClick - now handled by BackupButton component

  return (
    <div className="h-full">
      {/* Comments Stream - Full height scrollable */}
      <div
        className="h-full overflow-y-auto p-2 space-y-2 comments-container"
        style={{
          overscrollBehavior: 'contain',
          WebkitOverflowScrolling: 'touch',
          overflowAnchor: 'none'
        }}
        onScroll={handleScroll}
        onWheel={handleUserInteraction}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        onTouchMove={() => {
          // During touch move, clear timeouts and cancel hold detection
          if (interactionTimeoutRef.current) {
            clearTimeout(interactionTimeoutRef.current);
          }
          if (holdTimeoutRef.current) {
            clearTimeout(holdTimeoutRef.current);
          }
          // Cancel hold if user moves (mobile only)
          if (isMobile) {
            setIsHolding(false);
          }
        }}
      >
        {recentComments.length > 0 ? (
          recentComments.map((comment, index) => {
            const isRegular = regularCustomers.has(comment.username);
            const currentColors = isRegular ? colorSettings.regularCustomer : colorSettings.newCustomer;

            return (
              <CommentItem
                key={comment.id || index}
                comment={comment}
                isRegular={isRegular}
                currentColors={currentColors}
                regularCustomers={regularCustomers}
                toggleRegularCustomer={toggleRegularCustomer}
                isConnected={isConnected}
              />
            );
          })
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 p-6">
            {/* Check if both services are not running */}
            {(!systemState?.isRunning && !systemState?.messengerConnected) ? (
              // Show quick start interface
              <div className="w-full max-w-sm sm:max-w-md">
                {/* Check if cookies are available and services are running */}
                {quickStartData.scraperCookies && quickStartData.messengerCookies &&
                 quickStartData.scraperRunning && quickStartData.messengerRunning ? (
                  // Both cookies available - show quick start
                  <div className="bg-white rounded-lg shadow-lg p-3 sm:p-6 border border-gray-200">
                    <div className="text-center mb-4 sm:mb-6">
                      <Play className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 sm:mb-3 text-blue-600" />
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">Khởi động nhanh</h3>
                      <p className="text-xs sm:text-sm text-gray-600">
                        Bắt đầu thu thập bình luận và tin nhắn tự động
                      </p>
                    </div>

                    {/* Instagram Live Username Input */}
                    <div className="mb-3 sm:mb-4">
                      <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2">
                        Tên Instagram Live
                      </label>
                      <input
                        type="text"
                        value={quickStartData.liveUsername}
                        onChange={(e) => setQuickStartData(prev => ({ ...prev, liveUsername: e.target.value }))}
                        placeholder="Nhập tên Instagram Live..."
                        className="w-full px-2 py-1.5 sm:px-3 sm:py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>

                    {/* Action Buttons */}
                    <div className="space-y-2 sm:space-y-3">
                      <button
                        onClick={handleQuickStartScraper}
                        disabled={quickStartData.isStartingScraper || !quickStartData.liveUsername.trim()}
                        className="w-full flex items-center justify-center px-3 py-2 sm:px-4 sm:py-2 text-sm sm:text-base bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors touch-manipulation"
                      >
                        {quickStartData.isStartingScraper ? (
                          <>
                            <Loader className="h-3 w-3 sm:h-4 sm:w-4 mr-2 animate-spin" />
                            <span className="text-xs sm:text-sm">Đang khởi động...</span>
                          </>
                        ) : (
                          <>
                            <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                            <span className="text-xs sm:text-sm">Thu thập bình luận</span>
                          </>
                        )}
                      </button>

                      <button
                        onClick={handleQuickStartMessenger}
                        disabled={quickStartData.isStartingMessenger}
                        className="w-full flex items-center justify-center px-3 py-2 sm:px-4 sm:py-2 text-sm sm:text-base bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors touch-manipulation"
                      >
                        {quickStartData.isStartingMessenger ? (
                          <>
                            <Loader className="h-3 w-3 sm:h-4 sm:w-4 mr-2 animate-spin" />
                            <span className="text-xs sm:text-sm">Đang khởi động...</span>
                          </>
                        ) : (
                          <>
                            <Play className="h-3 w-3 sm:h-4 sm:w-4 mr-2" />
                            <span className="text-xs sm:text-sm">Tin nhắn tự động</span>
                          </>
                        )}
                      </button>
                    </div>

                    <div className="mt-4 text-xs text-gray-500 text-center">
                      Cả hai chức năng đã sẵn sàng và đang hoạt động
                    </div>
                  </div>
                ) : (
                  // Missing cookies - show login instructions
                  <div className="bg-white rounded-lg shadow-lg p-3 sm:p-6 border border-gray-200">
                    <div className="text-center mb-4 sm:mb-6">
                      <AlertCircle className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-2 sm:mb-3 text-orange-500" />
                      <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-1 sm:mb-2">Cần đăng nhập</h3>
                      <p className="text-xs sm:text-sm text-gray-600">
                        Vui lòng đăng nhập vào các chức năng sau để sử dụng
                      </p>
                    </div>

                    <div className="space-y-2 sm:space-y-3">
                      {!quickStartData.scraperCookies && (
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-3 bg-red-50 border border-red-200 rounded-lg space-y-2 sm:space-y-0">
                          <div className="flex items-center">
                            <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2" />
                            <span className="text-xs sm:text-sm text-red-700">Thu thập bình luận - Cần đăng nhập</span>
                          </div>
                          <button
                            onClick={() => setShowScraperLoginPopup(true)}
                            className="flex items-center justify-center px-2 py-1 sm:px-3 sm:py-1 bg-red-600 text-white text-xs sm:text-sm rounded hover:bg-red-700 transition-colors touch-manipulation w-full sm:w-auto"
                          >
                            <LogIn className="h-3 w-3 mr-1" />
                            Đăng nhập
                          </button>
                        </div>
                      )}

                      {quickStartData.scraperCookies && !quickStartData.scraperRunning && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4">
                          <div className="flex items-center mb-3">
                            <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500 mr-2" />
                            <span className="text-xs sm:text-sm text-yellow-700 font-medium">Thu thập bình luận - Chưa khởi động</span>
                          </div>

                          {/* Instagram Live Username Input */}
                          <div className="mb-3">
                            <label className="block text-xs sm:text-sm font-medium text-gray-700 mb-1">
                              Tên Instagram Live
                            </label>
                            <input
                              type="text"
                              value={quickStartData.liveUsername}
                              onChange={(e) => setQuickStartData(prev => ({ ...prev, liveUsername: e.target.value }))}
                              placeholder="Nhập tên Instagram Live..."
                              className="w-full px-2 py-1.5 sm:px-3 sm:py-2 text-sm border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                              disabled={quickStartData.isStartingScraper}
                            />
                            <p className="text-xs text-gray-500 mt-1">
                              Ví dụ: nếu URL là instagram.com/username/live thì nhập "username"
                            </p>
                          </div>

                          <button
                            onClick={handleQuickStartScraper}
                            disabled={quickStartData.isStartingScraper || !quickStartData.liveUsername.trim()}
                            className="w-full flex items-center justify-center px-3 py-2 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                          >
                            {quickStartData.isStartingScraper ? (
                              <>
                                <Loader className="h-4 w-4 mr-2 animate-spin" />
                                Đang khởi động...
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Khởi động thu thập bình luận
                              </>
                            )}
                          </button>
                        </div>
                      )}

                      {!quickStartData.messengerCookies && (
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-3 bg-red-50 border border-red-200 rounded-lg space-y-2 sm:space-y-0">
                          <div className="flex items-center">
                            <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-red-500 mr-2" />
                            <span className="text-xs sm:text-sm text-red-700">Tin nhắn tự động - Cần đăng nhập</span>
                          </div>
                          <button
                            onClick={() => setShowMessengerLoginPopup(true)}
                            className="flex items-center justify-center px-2 py-1 sm:px-3 sm:py-1 bg-red-600 text-white text-xs sm:text-sm rounded hover:bg-red-700 transition-colors touch-manipulation w-full sm:w-auto"
                          >
                            <LogIn className="h-3 w-3 mr-1" />
                            Đăng nhập
                          </button>
                        </div>
                      )}

                      {quickStartData.messengerCookies && !quickStartData.messengerRunning && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 sm:p-4">
                          <div className="flex items-center mb-3">
                            <AlertCircle className="h-4 w-4 sm:h-5 sm:w-5 text-yellow-500 mr-2" />
                            <span className="text-xs sm:text-sm text-yellow-700 font-medium">Tin nhắn tự động - Chưa khởi động</span>
                          </div>

                          <button
                            onClick={handleQuickStartMessenger}
                            disabled={quickStartData.isStartingMessenger}
                            className="w-full flex items-center justify-center px-3 py-2 text-sm bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
                          >
                            {quickStartData.isStartingMessenger ? (
                              <>
                                <Loader className="h-4 w-4 mr-2 animate-spin" />
                                Đang khởi động...
                              </>
                            ) : (
                              <>
                                <Play className="h-4 w-4 mr-2" />
                                Khởi động tin nhắn tự động
                              </>
                            )}
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              // At least one service is running - show normal empty state
              <div className="text-center">
                <MessageCircle className="h-16 w-16 mb-4 text-gray-400 mx-auto" />
                <h3 className="text-lg font-medium mb-2 text-gray-600">Chưa có bình luận nào</h3>
                <p className="text-sm text-center text-gray-500">
                  Bình luận sẽ xuất hiện ở đây khi có người bình luận trên Instagram Live
                </p>
              </div>
            )}
          </div>
        )}

        {/* Auto scroll anchor */}
        <div ref={commentsEndRef} />
      </div>

      {/* Login Popups */}
      <LoginPopup
        isOpen={showScraperLoginPopup}
        onClose={() => setShowScraperLoginPopup(false)}
        onLogin={handleScraperPopupLogin}
        title={quickStartData.scraperCookies && !quickStartData.scraperRunning
          ? "Khởi động thu thập bình luận"
          : "Đăng nhập Instagram - Thu thập bình luận"
        }
        isLoading={quickStartData.isStartingScraper}
        showLiveUsernameInput={true}
        liveUsername={quickStartData.liveUsername}
        onLiveUsernameChange={(value) => setQuickStartData(prev => ({ ...prev, liveUsername: value }))}
        startupMode={quickStartData.scraperCookies && !quickStartData.scraperRunning}
      />

      <LoginPopup
        isOpen={showMessengerLoginPopup}
        onClose={() => setShowMessengerLoginPopup(false)}
        onLogin={handleMessengerPopupLogin}
        title={quickStartData.messengerCookies && !quickStartData.messengerRunning
          ? "Khởi động tin nhắn tự động"
          : "Đăng nhập Instagram - Tin nhắn tự động"
        }
        isLoading={quickStartData.isStartingMessenger}
        startupMode={quickStartData.messengerCookies && !quickStartData.messengerRunning}
      />
    </div>
  );
};

export default Comments;
