const winston = require('winston');
const { exec } = require('child_process');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.simple(),
  transports: [new winston.transports.Console()]
});

class ProcessManager {
  constructor() {
    this.activeProcesses = new Set(); // Track active PowerShell processes
    this.maxConcurrentProcesses = 3; // Limit concurrent PowerShell processes
    this.processQueue = []; // Queue for pending processes
    this.isProcessing = false;
  }

  // Execute command with process limit control
  async executeCommand(command, description = 'Unknown command', timeout = 15000) {
    return new Promise((resolve) => {
      // Add to queue if too many processes are running
      if (this.activeProcesses.size >= this.maxConcurrentProcesses) {
        logger.warn(`⏳ Process queue: ${description} (${this.activeProcesses.size}/${this.maxConcurrentProcesses} active)`);
        this.processQueue.push({ command, description, timeout, resolve });
        this.processQueueIfNeeded();
        return;
      }

      this.executeCommandNow(command, description, timeout, resolve);
    });
  }

  executeCommandNow(command, description, timeout, resolve) {
    const processId = Date.now() + Math.random();
    this.activeProcesses.add(processId);

    logger.info(`🚀 Executing: ${description} (${this.activeProcesses.size}/${this.maxConcurrentProcesses} active)`);

    const timeoutHandle = setTimeout(() => {
      logger.warn(`⏰ Command timeout: ${description}`);
      this.activeProcesses.delete(processId);
      this.processQueueIfNeeded();
      resolve({ success: false, error: 'Timeout' });
    }, timeout);

    exec(command, (error, stdout, stderr) => {
      clearTimeout(timeoutHandle);
      this.activeProcesses.delete(processId);

      if (error && !error.message.includes('not found') && !error.message.includes('No Instance(s) Available')) {
        logger.warn(`❌ Command failed: ${description} - ${error.message}`);
        resolve({ success: false, error: error.message, stdout, stderr });
      } else {
        logger.info(`✅ Command completed: ${description}`);
        resolve({ success: true, stdout, stderr });
      }

      // Process next item in queue
      this.processQueueIfNeeded();
    });
  }

  processQueueIfNeeded() {
    if (this.processQueue.length > 0 && this.activeProcesses.size < this.maxConcurrentProcesses) {
      const { command, description, timeout, resolve } = this.processQueue.shift();
      this.executeCommandNow(command, description, timeout, resolve);
    }
  }

  // Get current process status
  getStatus() {
    return {
      activeProcesses: this.activeProcesses.size,
      maxConcurrentProcesses: this.maxConcurrentProcesses,
      queuedProcesses: this.processQueue.length,
      totalPending: this.activeProcesses.size + this.processQueue.length
    };
  }

  // Emergency stop all processes
  async emergencyStop() {
    logger.warn('🚨 Emergency stop: Clearing all process queues');
    
    // Clear queue
    this.processQueue.length = 0;
    
    // Kill PowerShell processes if too many
    if (process.platform === 'win32') {
      try {
        await this.executeCommand(
          'tasklist /FI "IMAGENAME eq powershell.exe" /FO CSV | find /C "powershell.exe"',
          'Count PowerShell processes',
          5000
        );
        
        // If more than 10 PowerShell processes, kill some
        const result = await this.executeCommand(
          'wmic process where "name=\'powershell.exe\' and commandline like \'%wmic%\'" delete',
          'Kill WMIC PowerShell processes',
          10000
        );
        
        if (result.success) {
          logger.info('✅ Cleaned up excess PowerShell processes');
        }
      } catch (error) {
        logger.error('Failed to cleanup PowerShell processes:', error);
      }
    }
    
    // Reset active processes tracking
    this.activeProcesses.clear();
  }

  // Monitor system for PowerShell process explosion
  async monitorPowerShellProcesses() {
    if (process.platform !== 'win32') return;

    try {
      const result = await this.executeCommand(
        'tasklist /FI "IMAGENAME eq powershell.exe" /FO CSV | find /C "powershell.exe"',
        'Monitor PowerShell count',
        5000
      );

      if (result.success && result.stdout) {
        const count = parseInt(result.stdout.trim()) || 0;
        
        if (count > 20) {
          logger.error(`🚨 PowerShell process explosion detected: ${count} processes!`);
          await this.emergencyStop();
          return { critical: true, count };
        } else if (count > 10) {
          logger.warn(`⚠️ High PowerShell process count: ${count}`);
          return { warning: true, count };
        }
        
        return { normal: true, count };
      }
    } catch (error) {
      logger.error('Failed to monitor PowerShell processes:', error);
    }
    
    return { error: true, count: 0 };
  }

  // Start monitoring
  startMonitoring() {
    // Monitor every 30 seconds
    this.monitorInterval = setInterval(async () => {
      const status = await this.monitorPowerShellProcesses();
      
      if (status.critical) {
        logger.error('🚨 CRITICAL: PowerShell process explosion - emergency cleanup triggered');
      } else if (status.warning) {
        logger.warn(`⚠️ WARNING: ${status.count} PowerShell processes detected`);
      }
    }, 30000);
    
    logger.info('🔍 PowerShell process monitoring started');
  }

  stopMonitoring() {
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
      logger.info('🔍 PowerShell process monitoring stopped');
    }
  }
}

// Singleton instance
const processManager = new ProcessManager();

module.exports = processManager;
