import React, { useState, useEffect } from 'react';
import { Printer, Package, X } from 'lucide-react';
import { useSocket } from '../contexts/SocketContext';
import { useCancelSettings } from '../contexts/CancelSettingsContext';
import { showPrintToast, showErrorToast } from '../utils/toastManager';

const PrintButton = ({ comment, disabled = false, size = 'sm', regularCustomers = new Set() }) => {
  const { printComment, isConnected } = useSocket();
  const { settings, isCancelEnabled } = useCancelSettings();
  const [isLoading, setIsLoading] = useState(false);
  const [showPrintCancel, setShowPrintCancel] = useState(false);
  const [printCancelTimeout, setPrintCancelTimeout] = useState(null);
  const [progressInterval, setProgressInterval] = useState(null);
  const [progress, setProgress] = useState(100);

  // No need for template loading since backend handles it automatically

  const handlePrintClick = (templateType = 'print_notification') => {
    if (!isConnected) {
      showErrorToast('Không có kết nối với server');
      return;
    }

    // Check if cancel is enabled for print action
    if (!isCancelEnabled('print')) {
      // If cancel is disabled, execute print immediately
      executePrint(templateType);
      return;
    }

    setShowPrintCancel(true);
    setProgress(100);

    // Clear any existing timeouts/intervals
    if (printCancelTimeout) {
      clearTimeout(printCancelTimeout);
    }
    if (progressInterval) {
      clearInterval(progressInterval);
    }

    // Use duration from settings
    const duration = settings.duration;
    const interval = 16; // ~60fps
    const steps = duration / interval;
    const progressStep = 100 / steps;

    let currentProgress = 100;
    const newProgressInterval = setInterval(() => {
      currentProgress -= progressStep;
      if (currentProgress <= 0) {
        clearInterval(newProgressInterval);
        setProgressInterval(null);
        setProgress(0);
        setShowPrintCancel(false);
        executePrint(templateType);
      } else {
        setProgress(currentProgress);
      }
    }, interval);

    setProgressInterval(newProgressInterval);

    // Set timeout as backup (should not be needed with progress animation)
    const timeout = setTimeout(() => {
      if (newProgressInterval) {
        clearInterval(newProgressInterval);
        setProgressInterval(null);
      }
      setShowPrintCancel(false);
      executePrint(templateType);
    }, duration);

    setPrintCancelTimeout(timeout);
  };

  const handleCancel = () => {
    setShowPrintCancel(false);
    setProgress(100);
    if (printCancelTimeout) {
      clearTimeout(printCancelTimeout);
      setPrintCancelTimeout(null);
    }
    if (progressInterval) {
      clearInterval(progressInterval);
      setProgressInterval(null);
    }
  };

  const executePrint = async (templateType = 'print_notification') => {
    setIsLoading(true);

    try {
      // Send comment with template type specification and wait for response
      const response = await printComment(comment, templateType);

      const isRegularCustomer = regularCustomers.has(comment.username);
      const customerTypeText = isRegularCustomer ? 'khách cũ' : 'khách mới';

      // Show success toast only after server confirms success
      showPrintToast(`Đã in bình luận của @${comment.username} (${customerTypeText})`);

      // Log the response for debugging
      console.log('Print response:', response);
    } catch (error) {
      showErrorToast(`Lỗi khi in bình luận: ` + error.message);
    } finally {
      setIsLoading(false);
    }
  };

  // Cleanup timeouts and intervals on unmount
  useEffect(() => {
    return () => {
      if (printCancelTimeout) {
        clearTimeout(printCancelTimeout);
      }
      if (progressInterval) {
        clearInterval(progressInterval);
      }
    };
  }, [printCancelTimeout, progressInterval]);

  // Remove unused functions since we only need simple print

  const getButtonSize = () => {
    switch (size) {
      case 'xs':
        return 'px-1 py-0.5 text-xs';
      case 'sm':
        return 'px-1.5 py-0.5 text-xs';
      case 'md':
        return 'px-3 py-1.5 text-sm';
      case 'lg':
        return 'px-4 py-2 text-base touch-manipulation';
      case 'xl':
        return 'px-6 py-3 text-lg touch-manipulation';
      default:
        return 'px-1.5 py-0.5 text-xs';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'xs':
        return 'h-2.5 w-2.5';
      case 'sm':
        return 'h-3 w-3';
      case 'md':
        return 'h-4 w-4';
      case 'lg':
        return 'h-5 w-5';
      case 'xl':
        return 'h-6 w-6';
      default:
        return 'h-3 w-3';
    }
  };

  // Print button only (backup button moved to comment layout)
  return (
    <div className="relative">
      <button
        onClick={showPrintCancel ? handleCancel : () => handlePrintClick()}
        disabled={disabled || !isConnected || isLoading}
        className={`flex items-center space-x-1 ${
          showPrintCancel
            ? 'bg-red-600 hover:bg-red-700'
            : 'bg-blue-600 hover:bg-blue-700'
        } disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded transition-colors ${getButtonSize()}`}
      >
        {showPrintCancel ? (
          <>
            <X className={getIconSize()} />
            <span>Hủy</span>
          </>
        ) : (
          <>
            <Printer className={getIconSize()} />
            <span>{isLoading ? 'Đang in...' : 'In'}</span>
          </>
        )}
      </button>

      {/* Circular progress border overlay */}
      {showPrintCancel && (
        <div className="absolute inset-0 pointer-events-none rounded">
          <svg className="w-full h-full" style={{ transform: 'rotate(-90deg)' }}>
            <circle
              cx="50%"
              cy="50%"
              r="calc(50% - 3px)"
              fill="none"
              stroke="rgba(255, 255, 255, 0.9)"
              strokeWidth="3"
              strokeDasharray="100 100"
              strokeDashoffset={100 - progress}
              strokeLinecap="round"
              style={{
                transition: 'stroke-dashoffset 16ms linear',
                filter: 'drop-shadow(0 0 2px rgba(255, 255, 255, 0.5))'
              }}
            />
          </svg>
        </div>
      )}
    </div>
  );
};

export default PrintButton;
