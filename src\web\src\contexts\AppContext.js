import React, { createContext, useContext, useReducer, useEffect, useMemo } from 'react';
import toast from 'react-hot-toast';

const AppContext = createContext();

export const useApp = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
};

// Initial state
const initialState = {
  // UI State
  loading: false,
  sidebarOpen: window.innerWidth >= 768, // Open by default on desktop
  currentPage: 'dashboard',

  // Data State
  comments: [],
  orders: [],
  templates: [],
  statistics: {},

  // Settings
  settings: {
    autoRefresh: true,
    refreshInterval: 5000,
    notificationsEnabled: true,
    soundEnabled: true,
    theme: 'light',
    language: 'vi',
    maxCommentsInMemory: 100000, // Không giới hạn - hệ thống xử lý được 100k+ comments
    autoCleanupInterval: 0, // Tắt cleanup
    messageRetryAttempts: 3
  },

  // Filters and Search
  filters: {
    comments: {
      search: '',
      processed: null,
      dateRange: null,
      username: ''
    },
    orders: {
      search: '',
      status: null,
      dateRange: null
    }
  },

  // Pagination
  pagination: {
    comments: {
      page: 1,
      limit: 20,
      total: 0
    },
    orders: {
      page: 1,
      limit: 20,
      total: 0
    }
  }
};

// Action types
const actionTypes = {
  SET_LOADING: 'SET_LOADING',
  SET_SIDEBAR_OPEN: 'SET_SIDEBAR_OPEN',
  SET_CURRENT_PAGE: 'SET_CURRENT_PAGE',

  SET_COMMENTS: 'SET_COMMENTS',
  ADD_COMMENT: 'ADD_COMMENT',
  UPDATE_COMMENT: 'UPDATE_COMMENT',

  SET_ORDERS: 'SET_ORDERS',
  ADD_ORDER: 'ADD_ORDER',
  UPDATE_ORDER: 'UPDATE_ORDER',

  SET_TEMPLATES: 'SET_TEMPLATES',
  ADD_TEMPLATE: 'ADD_TEMPLATE',
  UPDATE_TEMPLATE: 'UPDATE_TEMPLATE',
  DELETE_TEMPLATE: 'DELETE_TEMPLATE',

  SET_STATISTICS: 'SET_STATISTICS',

  UPDATE_SETTINGS: 'UPDATE_SETTINGS',

  SET_COMMENT_FILTER: 'SET_COMMENT_FILTER',
  SET_ORDER_FILTER: 'SET_ORDER_FILTER',
  CLEAR_FILTERS: 'CLEAR_FILTERS',

  SET_COMMENT_PAGINATION: 'SET_COMMENT_PAGINATION',
  SET_ORDER_PAGINATION: 'SET_ORDER_PAGINATION'
};

// Reducer function
const appReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      };

    case actionTypes.SET_SIDEBAR_OPEN:
      return {
        ...state,
        sidebarOpen: action.payload
      };

    case actionTypes.SET_CURRENT_PAGE:
      return {
        ...state,
        currentPage: action.payload
      };

    case actionTypes.SET_COMMENTS:
      return {
        ...state,
        comments: action.payload
      };

    case actionTypes.ADD_COMMENT:
      return {
        ...state,
        comments: [action.payload, ...state.comments]
      };

    case actionTypes.UPDATE_COMMENT:
      return {
        ...state,
        comments: state.comments.map(comment =>
          comment.id === action.payload.id
            ? { ...comment, ...action.payload }
            : comment
        )
      };

    case actionTypes.SET_ORDERS:
      return {
        ...state,
        orders: action.payload
      };

    case actionTypes.ADD_ORDER:
      return {
        ...state,
        orders: [action.payload, ...state.orders]
      };

    case actionTypes.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id
            ? { ...order, ...action.payload }
            : order
        )
      };

    case actionTypes.SET_TEMPLATES:
      return {
        ...state,
        templates: action.payload
      };

    case actionTypes.ADD_TEMPLATE:
      return {
        ...state,
        templates: [...state.templates, action.payload]
      };

    case actionTypes.UPDATE_TEMPLATE:
      return {
        ...state,
        templates: state.templates.map(template =>
          template.id === action.payload.id
            ? { ...template, ...action.payload }
            : template
        )
      };

    case actionTypes.DELETE_TEMPLATE:
      return {
        ...state,
        templates: state.templates.filter(template => template.id !== action.payload)
      };

    case actionTypes.SET_STATISTICS:
      return {
        ...state,
        statistics: action.payload
      };

    case actionTypes.UPDATE_SETTINGS:
      return {
        ...state,
        settings: {
          ...state.settings,
          ...action.payload
        }
      };

    case actionTypes.SET_COMMENT_FILTER:
      return {
        ...state,
        filters: {
          ...state.filters,
          comments: {
            ...state.filters.comments,
            ...action.payload
          }
        }
      };

    case actionTypes.SET_ORDER_FILTER:
      return {
        ...state,
        filters: {
          ...state.filters,
          orders: {
            ...state.filters.orders,
            ...action.payload
          }
        }
      };

    case actionTypes.CLEAR_FILTERS:
      return {
        ...state,
        filters: {
          comments: {
            search: '',
            processed: null,
            dateRange: null,
            username: ''
          },
          orders: {
            search: '',
            status: null,
            dateRange: null
          }
        }
      };

    case actionTypes.SET_COMMENT_PAGINATION:
      return {
        ...state,
        pagination: {
          ...state.pagination,
          comments: {
            ...state.pagination.comments,
            ...action.payload
          }
        }
      };

    case actionTypes.SET_ORDER_PAGINATION:
      return {
        ...state,
        pagination: {
          ...state.pagination,
          orders: {
            ...state.pagination.orders,
            ...action.payload
          }
        }
      };

    default:
      return state;
  }
};

export const AppProvider = ({ children }) => {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Load settings from localStorage on mount
  useEffect(() => {
    const savedSettings = localStorage.getItem('instagram-live-settings');
    if (savedSettings) {
      try {
        const parsedSettings = JSON.parse(savedSettings);
        dispatch({
          type: actionTypes.UPDATE_SETTINGS,
          payload: parsedSettings
        });
      } catch (error) {
        console.error('Failed to parse saved settings:', error);
      }
    }
  }, []);

  // Save settings to localStorage when they change
  useEffect(() => {
    localStorage.setItem('instagram-live-settings', JSON.stringify(state.settings));
  }, [state.settings]);

  // Handle window resize for responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      const isDesktop = window.innerWidth >= 768;
      if (isDesktop && !state.sidebarOpen) {
        dispatch({ type: actionTypes.SET_SIDEBAR_OPEN, payload: true });
      } else if (!isDesktop && state.sidebarOpen) {
        dispatch({ type: actionTypes.SET_SIDEBAR_OPEN, payload: false });
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [state.sidebarOpen]);

  // Socket event listeners for real-time updates
  useEffect(() => {
    if (typeof window !== 'undefined' && window.socket) {
      const socket = window.socket;

      // Listen for message verification failures
      const handleVerificationFailed = (data) => {
        console.warn('Message verification failed:', data);

        // Show toast notification
        toast.error(
          `⚠️ Xác minh tin nhắn thất bại cho @${data.username}: ${data.error}`,
          {
            duration: 8000,
            position: 'top-right',
          }
        );
      };

      socket.on('message-verification-failed', handleVerificationFailed);

      // Cleanup
      return () => {
        socket.off('message-verification-failed', handleVerificationFailed);
      };
    }
  }, []);

  // Action creators - memoized to prevent infinite re-renders
  const actions = useMemo(() => ({
    setLoading: (loading) => dispatch({ type: actionTypes.SET_LOADING, payload: loading }),
    setSidebarOpen: (open) => dispatch({ type: actionTypes.SET_SIDEBAR_OPEN, payload: open }),
    setCurrentPage: (page) => dispatch({ type: actionTypes.SET_CURRENT_PAGE, payload: page }),

    setComments: (comments) => dispatch({ type: actionTypes.SET_COMMENTS, payload: comments }),
    addComment: (comment) => dispatch({ type: actionTypes.ADD_COMMENT, payload: comment }),
    updateComment: (comment) => dispatch({ type: actionTypes.UPDATE_COMMENT, payload: comment }),

    setOrders: (orders) => dispatch({ type: actionTypes.SET_ORDERS, payload: orders }),
    addOrder: (order) => dispatch({ type: actionTypes.ADD_ORDER, payload: order }),
    updateOrder: (order) => dispatch({ type: actionTypes.UPDATE_ORDER, payload: order }),

    setTemplates: (templates) => dispatch({ type: actionTypes.SET_TEMPLATES, payload: templates }),
    addTemplate: (template) => dispatch({ type: actionTypes.ADD_TEMPLATE, payload: template }),
    updateTemplate: (template) => dispatch({ type: actionTypes.UPDATE_TEMPLATE, payload: template }),
    deleteTemplate: (templateId) => dispatch({ type: actionTypes.DELETE_TEMPLATE, payload: templateId }),

    setStatistics: (statistics) => dispatch({ type: actionTypes.SET_STATISTICS, payload: statistics }),

    updateSettings: (settings) => dispatch({ type: actionTypes.UPDATE_SETTINGS, payload: settings }),

    setCommentFilter: (filter) => dispatch({ type: actionTypes.SET_COMMENT_FILTER, payload: filter }),
    setOrderFilter: (filter) => dispatch({ type: actionTypes.SET_ORDER_FILTER, payload: filter }),
    clearFilters: () => dispatch({ type: actionTypes.CLEAR_FILTERS }),

    setCommentPagination: (pagination) => dispatch({ type: actionTypes.SET_COMMENT_PAGINATION, payload: pagination }),
    setOrderPagination: (pagination) => dispatch({ type: actionTypes.SET_ORDER_PAGINATION, payload: pagination })
  }), [dispatch]);

  const value = useMemo(() => ({
    state,
    actions,
    dispatch
  }), [state, actions, dispatch]);

  return (
    <AppContext.Provider value={value}>
      {children}
    </AppContext.Provider>
  );
};
