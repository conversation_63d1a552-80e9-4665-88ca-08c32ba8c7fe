#!/usr/bin/env node

/**
 * Backup script để lưu trữ dữ liệu quan trọng tr<PERSON>ớc khi reset database
 * Backup: customers, templates, settings, threads, send_once_history
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

const BASE_URL = 'http://localhost:3001';

// Colors for console output
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, 'green');
}

function logError(message) {
  log(`❌ ${message}`, 'red');
}

function logInfo(message) {
  log(`ℹ️  ${message}`, 'blue');
}

function logWarning(message) {
  log(`⚠️  ${message}`, 'yellow');
}

async function makeRequest(method, endpoint, data = null) {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      timeout: 30000
    };
    
    if (data) {
      config.data = data;
    }
    
    const response = await axios(config);
    return { success: true, data: response.data };
  } catch (error) {
    return { 
      success: false, 
      error: error.response?.data?.error || error.message,
      status: error.response?.status
    };
  }
}

async function createBackupDirectory() {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const backupDir = `backup_${timestamp}`;
  
  try {
    await fs.mkdir(backupDir, { recursive: true });
    logSuccess(`Created backup directory: ${backupDir}`);
    return backupDir;
  } catch (error) {
    logError(`Failed to create backup directory: ${error.message}`);
    throw error;
  }
}

async function saveToFile(filePath, data, description) {
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf8');
    logSuccess(`${description} saved to ${filePath}`);
    return true;
  } catch (error) {
    logError(`Failed to save ${description}: ${error.message}`);
    return false;
  }
}

async function backupCustomers(backupDir) {
  logInfo('Backing up customers...');
  
  const result = await makeRequest('GET', '/api/customers');
  if (result.success) {
    const filePath = path.join(backupDir, 'customers.json');
    await saveToFile(filePath, result.data, 'Customers');
    logInfo(`Backed up ${result.data.length} customers`);
    return true;
  } else {
    logError(`Failed to backup customers: ${result.error}`);
    return false;
  }
}

async function backupSettings(backupDir) {
  logInfo('Backing up settings...');
  
  // Get all important settings
  const settingsToBackup = [
    'device_id',
    'mongodb_connection_string',
    'mongodb_last_sync',
    'printer_settings',
    'template_settings',
    'auto_message_settings'
  ];
  
  const settings = {};
  let successCount = 0;
  
  for (const setting of settingsToBackup) {
    const result = await makeRequest('GET', `/api/settings/${setting}`);
    if (result.success) {
      settings[setting] = result.data;
      successCount++;
    } else {
      logWarning(`Could not backup setting: ${setting}`);
      settings[setting] = null;
    }
  }
  
  const filePath = path.join(backupDir, 'settings.json');
  await saveToFile(filePath, settings, 'Settings');
  logInfo(`Backed up ${successCount}/${settingsToBackup.length} settings`);
  return true;
}

async function backupThreads(backupDir) {
  logInfo('Backing up Instagram threads...');
  
  const result = await makeRequest('GET', '/api/instagram-threads');
  if (result.success) {
    const filePath = path.join(backupDir, 'instagram_threads.json');
    await saveToFile(filePath, result.data, 'Instagram threads');
    logInfo(`Backed up ${result.data.length} threads`);
    return true;
  } else {
    logError(`Failed to backup threads: ${result.error}`);
    return false;
  }
}

async function backupSendOnceHistory(backupDir) {
  logInfo('Backing up send_once history...');
  
  const result = await makeRequest('GET', '/api/send-once-history');
  if (result.success) {
    const filePath = path.join(backupDir, 'send_once_history.json');
    await saveToFile(filePath, result.data, 'Send_once history');
    logInfo(`Backed up ${result.data.length} send_once records`);
    return true;
  } else {
    logError(`Failed to backup send_once history: ${result.error}`);
    return false;
  }
}

async function backupTemplates(backupDir) {
  logInfo('Backing up templates...');
  
  const result = await makeRequest('GET', '/api/templates');
  if (result.success) {
    const filePath = path.join(backupDir, 'templates.json');
    await saveToFile(filePath, result.data, 'Templates');
    logInfo(`Backed up ${result.data.length} templates`);
    return true;
  } else {
    logError(`Failed to backup templates: ${result.error}`);
    return false;
  }
}

async function backupSystemInfo(backupDir) {
  logInfo('Backing up system info...');
  
  // Get system status
  const statusResult = await makeRequest('GET', '/api/status');
  const mongoResult = await makeRequest('GET', '/api/mongodb/status');
  const countsResult = await makeRequest('GET', '/api/mongodb/debug-counts');
  
  const systemInfo = {
    status: statusResult.success ? statusResult.data : null,
    mongodb: mongoResult.success ? mongoResult.data : null,
    counts: countsResult.success ? countsResult.data : null,
    backup_timestamp: new Date().toISOString(),
    backup_reason: 'Pre-reset backup for database restructure'
  };
  
  const filePath = path.join(backupDir, 'system_info.json');
  await saveToFile(filePath, systemInfo, 'System info');
  return true;
}

async function createRestoreScript(backupDir) {
  logInfo('Creating restore script...');
  
  const restoreScript = `#!/usr/bin/env node

/**
 * Restore script for backup created on ${new Date().toISOString()}
 * Run this script to restore data after database reset
 */

const axios = require('axios');
const fs = require('fs').promises;
const path = require('path');

const BASE_URL = 'http://localhost:3001';

async function restoreData() {
  console.log('🔄 Starting data restore...');
  
  try {
    // Restore customers
    const customers = JSON.parse(await fs.readFile('customers.json', 'utf8'));
    console.log(\`Restoring \${customers.length} customers...\`);
    // Add restore logic here
    
    // Restore settings
    const settings = JSON.parse(await fs.readFile('settings.json', 'utf8'));
    console.log('Restoring settings...');
    // Add restore logic here
    
    // Restore threads
    const threads = JSON.parse(await fs.readFile('instagram_threads.json', 'utf8'));
    console.log(\`Restoring \${threads.length} threads...\`);
    // Add restore logic here
    
    console.log('✅ Restore completed successfully');
  } catch (error) {
    console.error('❌ Restore failed:', error.message);
  }
}

restoreData();
`;
  
  const filePath = path.join(backupDir, 'restore.js');
  await fs.writeFile(filePath, restoreScript, 'utf8');
  logSuccess(`Restore script created: ${filePath}`);
  return true;
}

async function runBackup() {
  log('🚀 Starting comprehensive data backup...', 'bold');
  
  try {
    // Create backup directory
    const backupDir = await createBackupDirectory();
    
    // Run all backup operations
    const results = await Promise.allSettled([
      backupCustomers(backupDir),
      backupSettings(backupDir),
      backupThreads(backupDir),
      backupSendOnceHistory(backupDir),
      backupTemplates(backupDir),
      backupSystemInfo(backupDir),
      createRestoreScript(backupDir)
    ]);
    
    // Count successes
    const successful = results.filter(r => r.status === 'fulfilled' && r.value).length;
    const total = results.length;
    
    log(`\n=== BACKUP SUMMARY ===`, 'bold');
    log(`Backup directory: ${backupDir}`, 'blue');
    log(`Operations: ${successful}/${total} successful`, successful === total ? 'green' : 'yellow');
    
    if (successful === total) {
      logSuccess('All data backed up successfully! 🎉');
      logInfo('You can now proceed with database reset.');
    } else {
      logWarning('Some backup operations failed. Review the logs above.');
    }
    
    return backupDir;
    
  } catch (error) {
    logError(`Backup failed: ${error.message}`);
    throw error;
  }
}

// Run backup
runBackup().catch(error => {
  logError(`Backup script failed: ${error.message}`);
  process.exit(1);
});
