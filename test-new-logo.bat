@echo off
echo 🎨 Testing new CommiLive logo...
echo.

echo 📁 Checking logo files...
if exist "CommiLive_logo_nobackground.png" (
    echo ✅ Source logo found: CommiLive_logo_nobackground.png
) else (
    echo ❌ Source logo not found!
    pause
    exit /b 1
)

if exist "src\assets\icon.png" (
    echo ✅ Electron icon found: src\assets\icon.png
) else (
    echo ❌ Electron icon not found!
)

if exist "src\web\public\favicon.png" (
    echo ✅ Web favicon found: src\web\public\favicon.png
) else (
    echo ❌ Web favicon not found!
)

echo.
echo 🚀 Starting Electron app to test logo...
echo Press Ctrl+C to stop the app
echo.

npm run dev

echo.
echo 🎯 To test web favicon:
echo 1. Open browser and go to http://localhost:3001
echo 2. Check the favicon in browser tab
echo 3. Check mobile PWA icon when adding to home screen
echo.
pause
