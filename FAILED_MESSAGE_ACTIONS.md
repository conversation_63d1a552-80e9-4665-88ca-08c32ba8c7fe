# 🔄 Failed Message Actions: Delete & Retry

## 🎯 Tổng quan

Tính năng quản lý tin nhắn thất bại với các hành động:
- **Thử lại**: <PERSON><PERSON><PERSON> tin nhắn thất bại trở lại hàng đợi
- **Xóa**: <PERSON><PERSON><PERSON> vĩnh viễn tin nhắn thất bại
- **Đ<PERSON>h dấu đã xử lý**: Đánh dấu đã xử lý thủ công (existing)

## 🚀 API Endpoints

### **Delete Failed Message**
```bash
DELETE /api/failed-messages/{id}
```

**Response:**
```json
{
  "success": true,
  "message": "Failed message deleted successfully"
}
```

### **Retry Failed Message**
```bash
POST /api/failed-messages/{id}/retry
```

**Response:**
```json
{
  "success": true,
  "message": "Message added back to queue for retry"
}
```

### **Existing Resolve Endpoint**
```bash
POST /api/failed-messages/{id}/resolve
```

## 🗄️ Database Methods

### **Delete Failed Message**
```javascript
async deleteFailedMessage(id) {
  await this.runQuery('DELETE FROM failed_messages WHERE id = ?', [id]);
  this.logger.info('Failed message deleted:', id);
  return true;
}
```

### **Get Failed Message by ID**
```javascript
async getFailedMessageById(id) {
  const query = 'SELECT * FROM failed_messages WHERE id = ?';
  const result = await this.getQuery(query, [id]);
  return result;
}
```

## 🔄 Retry Logic

### **Retry Process:**
1. **Get failed message details** từ database
2. **Create new message** với retry prefix
3. **Add to message queue** với status 'pending'
4. **Delete from failed_messages** table
5. **Auto-processing** bởi message queue

### **Retry Message Structure:**
```javascript
const messageData = {
  id: `retry_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  comment_id: failedMessage.comment_id || null,
  username: failedMessage.username,
  original_comment: failedMessage.original_comment,
  customer_type: failedMessage.customer_type || 'regular',
  template_name: failedMessage.template_name || null,
  template_type: failedMessage.template_type || 'normal',
  status: 'pending',
  retries: 0,  // Reset retry count
  max_retries: 3
};
```

## 🖥️ Frontend Features

### **Action Buttons Layout**
```jsx
<div className="ml-4 flex flex-col space-y-2">
  {/* Retry Button - Blue */}
  <button onClick={() => handleRetry(message.id)}>
    <RotateCcw className="h-3 w-3 mr-1" />
    Thử lại
  </button>

  {/* Delete Button - Red */}
  <button onClick={() => handleDelete(message.id)}>
    <Trash2 className="h-3 w-3 mr-1" />
    Xóa
  </button>

  {/* Resolve Button - Green */}
  <button onClick={() => handleResolve(message.id)}>
    <CheckCircle className="h-3 w-3 mr-1" />
    Đã xử lý
  </button>
</div>
```

### **Button States**
- **Normal**: Hiển thị icon + text
- **Loading**: Spinner animation + disabled
- **Disabled**: Opacity 50% khi đang xử lý

### **Color Coding**
- **Retry**: Blue (bg-blue-100, text-blue-800)
- **Delete**: Red (bg-red-100, text-red-800)  
- **Resolve**: Green (bg-green-100, text-green-800)

## 🔧 Frontend Handlers

### **Delete Handler**
```javascript
const handleDelete = async (messageId) => {
  if (!window.confirm('Bạn có chắc chắn muốn xóa tin nhắn thất bại này?')) {
    return;
  }

  try {
    setResolving(prev => ({ ...prev, [messageId]: true }));
    
    const response = await fetch(`/api/failed-messages/${messageId}`, {
      method: 'DELETE'
    });

    if (data.success) {
      toast.success('Đã xóa tin nhắn thất bại');
      setFailedMessages(prev => prev.filter(msg => msg.id !== messageId));
    }
  } catch (error) {
    toast.error('Lỗi khi xóa tin nhắn thất bại');
  } finally {
    setResolving(prev => ({ ...prev, [messageId]: false }));
  }
};
```

### **Retry Handler**
```javascript
const handleRetry = async (messageId) => {
  try {
    setResolving(prev => ({ ...prev, [messageId]: true }));
    
    const response = await fetch(`/api/failed-messages/${messageId}/retry`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' }
    });

    if (data.success) {
      toast.success('Tin nhắn đã được thêm vào hàng đợi để thử lại');
      setFailedMessages(prev => prev.filter(msg => msg.id !== messageId));
    }
  } catch (error) {
    toast.error('Lỗi khi thử lại tin nhắn');
  } finally {
    setResolving(prev => ({ ...prev, [messageId]: false }));
  }
};
```

## 📊 User Experience

### **Workflow Examples**

#### **Retry Workflow:**
1. **User thấy tin nhắn thất bại** → Click "Thử lại"
2. **Tin nhắn được add vào queue** → Biến mất khỏi failed list
3. **Auto-processing** → Messenger service xử lý
4. **Thành công**: Tin nhắn được gửi
5. **Thất bại lại**: Quay lại failed list với retry count mới

#### **Delete Workflow:**
1. **User thấy tin nhắn không cần thiết** → Click "Xóa"
2. **Confirmation dialog** → User confirm
3. **Tin nhắn bị xóa vĩnh viễn** → Biến mất khỏi failed list
4. **Không thể khôi phục** → Permanent deletion

#### **Resolve Workflow:**
1. **User đã xử lý thủ công** → Click "Đã xử lý"
2. **Tin nhắn được mark resolved** → Biến mất khỏi failed list
3. **Lưu trong database** → Có thể track resolved messages

## ⚠️ Important Notes

### **Retry Considerations**
- **Reset retry count**: Retry từ 0, không inherit retry count cũ
- **Same template**: Sử dụng lại template và customer type gốc
- **New message ID**: Generate ID mới với prefix "retry_"
- **Queue position**: Thêm vào cuối queue (FIFO)

### **Delete Considerations**
- **Permanent deletion**: Không thể khôi phục
- **Confirmation required**: User phải confirm trước khi xóa
- **No backup**: Không có backup mechanism

### **State Management**
- **Loading states**: Prevent multiple clicks
- **Optimistic updates**: Remove from UI immediately
- **Error handling**: Show error toast và không remove khỏi UI

## 🎯 Benefits

### **User Control**
- **Flexible management**: User có thể quyết định xử lý như thế nào
- **Reduce clutter**: Xóa messages không cần thiết
- **Second chances**: Retry messages có thể thành công

### **System Efficiency**
- **Clean database**: Xóa failed messages không cần thiết
- **Reduce noise**: Chỉ giữ lại messages thực sự cần xử lý
- **Better monitoring**: Focus vào messages thực sự có vấn đề

### **Operational Benefits**
- **Less manual work**: Automated retry thay vì manual resend
- **Better tracking**: Clear action history
- **Improved workflow**: Streamlined failed message management
- **Bulk efficiency**: Handle multiple messages at once
- **Quick cleanup**: Clear all failed messages with one click

## 🔄 Bulk Operations

### **Delete All Failed Messages**
```bash
DELETE /api/failed-messages/bulk/all
```

**Response:**
```json
{
  "success": true,
  "message": "Deleted 15 failed messages",
  "deletedCount": 15
}
```

### **Retry All Failed Messages**
```bash
POST /api/failed-messages/bulk/retry-all
```

**Response:**
```json
{
  "success": true,
  "message": "Retried 12 messages, 3 errors",
  "successCount": 12,
  "errorCount": 3,
  "totalProcessed": 15
}
```

### **Database Method for Bulk Delete**
```javascript
async deleteAllFailedMessages() {
  // Get count before deletion
  const countResult = await this.getQuery('SELECT COUNT(*) as count FROM failed_messages WHERE resolved = FALSE');
  const count = countResult.count;

  // Delete all unresolved failed messages
  await this.runQuery('DELETE FROM failed_messages WHERE resolved = FALSE');

  return count;
}
```

### **Bulk Retry Logic**
```javascript
// Get all failed messages (up to 1000)
const failedMessages = await database.getFailedMessages(1000, 0);

for (const failedMessage of failedMessages) {
  try {
    // Create retry message
    const messageData = {
      id: `retry_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
      username: failedMessage.username,
      original_comment: failedMessage.original_comment,
      // ... other fields
      status: 'pending',
      retries: 0
    };

    // Add to queue and delete from failed
    await database.addToMessageQueue(messageData);
    await database.deleteFailedMessage(failedMessage.id);

    successCount++;
  } catch (error) {
    errorCount++;
  }
}
```

### **Frontend Bulk Handlers**
```javascript
const handleDeleteAll = async () => {
  if (!window.confirm(`Bạn có chắc chắn muốn xóa tất cả ${failedMessages.length} tin nhắn thất bại?`)) {
    return;
  }

  try {
    setBulkOperating(true);
    const response = await fetch('/api/failed-messages/bulk/all', { method: 'DELETE' });

    if (data.success) {
      toast.success(`Đã xóa ${data.deletedCount} tin nhắn thất bại`);
      setFailedMessages([]); // Clear UI
    }
  } finally {
    setBulkOperating(false);
  }
};

const handleRetryAll = async () => {
  if (!window.confirm(`Bạn có chắc chắn muốn thử lại tất cả ${failedMessages.length} tin nhắn thất bại?`)) {
    return;
  }

  try {
    setBulkOperating(true);
    const response = await fetch('/api/failed-messages/bulk/retry-all', { method: 'POST' });

    if (data.success) {
      toast.success(`Đã thử lại ${data.successCount} tin nhắn`);
      setFailedMessages([]); // Clear UI since moved to queue
    }
  } finally {
    setBulkOperating(false);
  }
};
```

### **UI Layout for Bulk Operations**
```jsx
<div className="flex items-center space-x-2">
  {failedMessages.length > 0 && (
    <>
      {/* Retry All Button - Blue */}
      <button onClick={handleRetryAll} disabled={bulkOperating}>
        <RotateCcw className="h-4 w-4 mr-2" />
        Thử lại tất cả
      </button>

      {/* Delete All Button - Red */}
      <button onClick={handleDeleteAll} disabled={bulkOperating}>
        <Trash2 className="h-4 w-4 mr-2" />
        Xóa tất cả
      </button>
    </>
  )}

  {/* Refresh Button */}
  <button onClick={fetchFailedMessages} disabled={bulkOperating}>
    Làm mới
  </button>
</div>
```

## 🔮 Future Enhancements

### **Advanced Bulk Operations**
1. **Selective bulk**: Select specific messages để bulk operation
2. **Bulk resolve**: Mark multiple messages as resolved
3. **Filtered bulk**: Bulk operations based on filters (by error type, user, etc.)

### **Smart Retry**
1. **Retry with delay**: Thêm delay trước khi retry
2. **Retry conditions**: Chỉ retry certain types of failures
3. **Retry limits**: Limit số lần retry per message
4. **Retry scheduling**: Schedule retry vào thời gian cụ thể

### **Enhanced Tracking**
1. **Action history**: Track ai làm gì với failed message
2. **Retry success rate**: Track success rate của retried messages
3. **Failure patterns**: Analyze common failure reasons
4. **Performance metrics**: Track time to resolution

### **Advanced Features**
1. **Auto-retry**: Tự động retry certain types of failures
2. **Smart categorization**: Group failures by type
3. **Priority handling**: Priority queue cho retry messages
4. **Notification system**: Alert khi có failed messages mới
